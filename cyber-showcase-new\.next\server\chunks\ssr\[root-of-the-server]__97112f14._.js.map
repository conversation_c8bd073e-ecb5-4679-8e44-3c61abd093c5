{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/store/app-store.ts"], "sourcesContent": ["/**\n * 🔮 赛博朋克展示平台 - 全局状态管理\n * 使用Zustand实现轻量级状态管理\n */\n\nimport { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { UserType, PathRecommendation, UserBehavior } from '@/lib/path-detector';\n\n// 🎨 主题类型定义\nexport type Theme = 'mystical' | 'business' | 'adaptive';\n\n// 🌟 展示模式定义\nexport type ShowcaseMode = 'intro' | 'products' | 'capabilities' | 'cases' | 'contact';\n\n// 📊 产品类型定义\nexport type ProductType = 'zencode' | 'spore' | 'training';\n\n// 🎯 用户交互状态\nexport interface UserInteraction {\n  currentPage: string;\n  timeSpent: number;\n  interactions: string[];\n  preferences: Record<string, any>;\n}\n\n// 🔧 应用状态接口\nexport interface AppState {\n  // 🎨 主题和外观\n  theme: Theme;\n  isDarkMode: boolean;\n  showParticles: boolean;\n  enableAnimations: boolean;\n  \n  // 🧭 导航和路径\n  currentMode: ShowcaseMode;\n  userType: UserType;\n  pathRecommendation: PathRecommendation | null;\n  \n  // 📱 用户交互\n  userInteraction: UserInteraction;\n  behaviorData: Partial<UserBehavior>;\n  \n  // 🎬 展示控制\n  isIntroPlaying: boolean;\n  currentProductFocus: ProductType | null;\n  demoMode: boolean;\n  \n  // 📊 数据和内容\n  contentLanguage: 'zh' | 'en';\n  showTechnicalDetails: boolean;\n  personalizedContent: string[];\n  \n  // 🔄 加载状态\n  isLoading: boolean;\n  loadingMessage: string;\n  \n  // 🎵 音效控制\n  soundEnabled: boolean;\n  backgroundMusicEnabled: boolean;\n}\n\n// ⚡ 状态操作接口\nexport interface AppActions {\n  // 🎨 主题操作\n  setTheme: (theme: Theme) => void;\n  toggleDarkMode: () => void;\n  toggleParticles: () => void;\n  toggleAnimations: () => void;\n  \n  // 🧭 导航操作\n  setCurrentMode: (mode: ShowcaseMode) => void;\n  setUserType: (userType: UserType) => void;\n  setPathRecommendation: (recommendation: PathRecommendation) => void;\n  \n  // 📱 用户交互操作\n  recordInteraction: (interaction: string) => void;\n  updateTimeSpent: (page: string, time: number) => void;\n  setBehaviorData: (behavior: Partial<UserBehavior>) => void;\n  \n  // 🎬 展示控制操作\n  startIntro: () => void;\n  stopIntro: () => void;\n  focusProduct: (product: ProductType) => void;\n  toggleDemoMode: () => void;\n  \n  // 📊 内容操作\n  setContentLanguage: (lang: 'zh' | 'en') => void;\n  toggleTechnicalDetails: () => void;\n  setPersonalizedContent: (content: string[]) => void;\n  \n  // 🔄 加载操作\n  setLoading: (loading: boolean, message?: string) => void;\n  \n  // 🎵 音效操作\n  toggleSound: () => void;\n  toggleBackgroundMusic: () => void;\n  \n  // 🔄 重置操作\n  resetUserData: () => void;\n  resetToDefaults: () => void;\n}\n\n// 🏪 完整的Store类型\nexport type AppStore = AppState & AppActions;\n\n// 🌟 默认状态\nconst defaultState: AppState = {\n  // 🎨 主题和外观\n  theme: 'mystical',\n  isDarkMode: true,\n  showParticles: true,\n  enableAnimations: true,\n  \n  // 🧭 导航和路径\n  currentMode: 'intro',\n  userType: 'mixed-track',\n  pathRecommendation: null,\n  \n  // 📱 用户交互\n  userInteraction: {\n    currentPage: '/',\n    timeSpent: 0,\n    interactions: [],\n    preferences: {}\n  },\n  behaviorData: {},\n  \n  // 🎬 展示控制\n  isIntroPlaying: false,\n  currentProductFocus: null,\n  demoMode: false,\n  \n  // 📊 数据和内容\n  contentLanguage: 'zh',\n  showTechnicalDetails: false,\n  personalizedContent: [],\n  \n  // 🔄 加载状态\n  isLoading: false,\n  loadingMessage: '',\n  \n  // 🎵 音效控制\n  soundEnabled: true,\n  backgroundMusicEnabled: false\n};\n\n// 🔮 创建Zustand Store\nexport const useAppStore = create<AppStore>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        ...defaultState,\n        \n        // 🎨 主题操作实现\n        setTheme: (theme) => {\n          set({ theme }, false, 'setTheme');\n          \n          // 根据主题自动调整其他设置\n          if (theme === 'business') {\n            set({ \n              showParticles: false, \n              enableAnimations: false,\n              isDarkMode: false \n            });\n          } else if (theme === 'mystical') {\n            set({ \n              showParticles: true, \n              enableAnimations: true,\n              isDarkMode: true \n            });\n          }\n        },\n        \n        toggleDarkMode: () => {\n          set((state) => ({ isDarkMode: !state.isDarkMode }), false, 'toggleDarkMode');\n        },\n        \n        toggleParticles: () => {\n          set((state) => ({ showParticles: !state.showParticles }), false, 'toggleParticles');\n        },\n        \n        toggleAnimations: () => {\n          set((state) => ({ enableAnimations: !state.enableAnimations }), false, 'toggleAnimations');\n        },\n        \n        // 🧭 导航操作实现\n        setCurrentMode: (mode) => {\n          set({ currentMode: mode }, false, 'setCurrentMode');\n          \n          // 记录页面访问\n          const { recordInteraction } = get();\n          recordInteraction(`navigate_to_${mode}`);\n        },\n        \n        setUserType: (userType) => {\n          set({ userType }, false, 'setUserType');\n          \n          // 根据用户类型自动调整主题\n          if (userType === 'tech-track') {\n            get().setTheme('mystical');\n          } else if (userType === 'business-track') {\n            get().setTheme('business');\n          } else {\n            get().setTheme('adaptive');\n          }\n        },\n        \n        setPathRecommendation: (recommendation) => {\n          set({ \n            pathRecommendation: recommendation,\n            personalizedContent: recommendation.personalizedContent \n          }, false, 'setPathRecommendation');\n        },\n        \n        // 📱 用户交互操作实现\n        recordInteraction: (interaction) => {\n          set((state) => ({\n            userInteraction: {\n              ...state.userInteraction,\n              interactions: [...state.userInteraction.interactions, interaction]\n            }\n          }), false, 'recordInteraction');\n        },\n        \n        updateTimeSpent: (page, time) => {\n          set((state) => ({\n            userInteraction: {\n              ...state.userInteraction,\n              currentPage: page,\n              timeSpent: state.userInteraction.timeSpent + time\n            }\n          }), false, 'updateTimeSpent');\n        },\n        \n        setBehaviorData: (behavior) => {\n          set((state) => ({\n            behaviorData: { ...state.behaviorData, ...behavior }\n          }), false, 'setBehaviorData');\n        },\n        \n        // 🎬 展示控制操作实现\n        startIntro: () => {\n          set({ isIntroPlaying: true, currentMode: 'intro' }, false, 'startIntro');\n        },\n        \n        stopIntro: () => {\n          set({ isIntroPlaying: false }, false, 'stopIntro');\n        },\n        \n        focusProduct: (product) => {\n          set({ \n            currentProductFocus: product,\n            currentMode: 'products' \n          }, false, 'focusProduct');\n        },\n        \n        toggleDemoMode: () => {\n          set((state) => ({ demoMode: !state.demoMode }), false, 'toggleDemoMode');\n        },\n        \n        // 📊 内容操作实现\n        setContentLanguage: (lang) => {\n          set({ contentLanguage: lang }, false, 'setContentLanguage');\n        },\n        \n        toggleTechnicalDetails: () => {\n          set((state) => ({ \n            showTechnicalDetails: !state.showTechnicalDetails \n          }), false, 'toggleTechnicalDetails');\n        },\n        \n        setPersonalizedContent: (content) => {\n          set({ personalizedContent: content }, false, 'setPersonalizedContent');\n        },\n        \n        // 🔄 加载操作实现\n        setLoading: (loading, message = '') => {\n          set({ isLoading: loading, loadingMessage: message }, false, 'setLoading');\n        },\n        \n        // 🎵 音效操作实现\n        toggleSound: () => {\n          set((state) => ({ soundEnabled: !state.soundEnabled }), false, 'toggleSound');\n        },\n        \n        toggleBackgroundMusic: () => {\n          set((state) => ({ \n            backgroundMusicEnabled: !state.backgroundMusicEnabled \n          }), false, 'toggleBackgroundMusic');\n        },\n        \n        // 🔄 重置操作实现\n        resetUserData: () => {\n          set({\n            userInteraction: defaultState.userInteraction,\n            behaviorData: {},\n            pathRecommendation: null,\n            personalizedContent: []\n          }, false, 'resetUserData');\n        },\n        \n        resetToDefaults: () => {\n          set(defaultState, false, 'resetToDefaults');\n        }\n      }),\n      {\n        name: 'cyber-showcase-store',\n        partialize: (state) => ({\n          // 只持久化用户偏好，不持久化临时状态\n          theme: state.theme,\n          isDarkMode: state.isDarkMode,\n          showParticles: state.showParticles,\n          enableAnimations: state.enableAnimations,\n          contentLanguage: state.contentLanguage,\n          soundEnabled: state.soundEnabled,\n          backgroundMusicEnabled: state.backgroundMusicEnabled,\n          userInteraction: state.userInteraction,\n          behaviorData: state.behaviorData\n        })\n      }\n    ),\n    {\n      name: 'cyber-showcase-store'\n    }\n  )\n);\n\n// 🎯 选择器函数 - 用于优化性能\nexport const selectTheme = (state: AppStore) => state.theme;\nexport const selectUserType = (state: AppStore) => state.userType;\nexport const selectCurrentMode = (state: AppStore) => state.currentMode;\nexport const selectIsLoading = (state: AppStore) => state.isLoading;\nexport const selectUserInteraction = (state: AppStore) => state.userInteraction;\nexport const selectPersonalizedContent = (state: AppStore) => state.personalizedContent;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAED;AACA;;;AAoGA,UAAU;AACV,MAAM,eAAyB;IAC7B,WAAW;IACX,OAAO;IACP,YAAY;IACZ,eAAe;IACf,kBAAkB;IAElB,WAAW;IACX,aAAa;IACb,UAAU;IACV,oBAAoB;IAEpB,UAAU;IACV,iBAAiB;QACf,aAAa;QACb,WAAW;QACX,cAAc,EAAE;QAChB,aAAa,CAAC;IAChB;IACA,cAAc,CAAC;IAEf,UAAU;IACV,gBAAgB;IAChB,qBAAqB;IACrB,UAAU;IAEV,WAAW;IACX,iBAAiB;IACjB,sBAAsB;IACtB,qBAAqB,EAAE;IAEvB,UAAU;IACV,WAAW;IACX,gBAAgB;IAEhB,UAAU;IACV,cAAc;IACd,wBAAwB;AAC1B;AAGO,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,GAAG,YAAY;QAEf,YAAY;QACZ,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM,GAAG,OAAO;YAEtB,eAAe;YACf,IAAI,UAAU,YAAY;gBACxB,IAAI;oBACF,eAAe;oBACf,kBAAkB;oBAClB,YAAY;gBACd;YACF,OAAO,IAAI,UAAU,YAAY;gBAC/B,IAAI;oBACF,eAAe;oBACf,kBAAkB;oBAClB,YAAY;gBACd;YACF;QACF;QAEA,gBAAgB;YACd,IAAI,CAAC,QAAU,CAAC;oBAAE,YAAY,CAAC,MAAM,UAAU;gBAAC,CAAC,GAAG,OAAO;QAC7D;QAEA,iBAAiB;YACf,IAAI,CAAC,QAAU,CAAC;oBAAE,eAAe,CAAC,MAAM,aAAa;gBAAC,CAAC,GAAG,OAAO;QACnE;QAEA,kBAAkB;YAChB,IAAI,CAAC,QAAU,CAAC;oBAAE,kBAAkB,CAAC,MAAM,gBAAgB;gBAAC,CAAC,GAAG,OAAO;QACzE;QAEA,YAAY;QACZ,gBAAgB,CAAC;YACf,IAAI;gBAAE,aAAa;YAAK,GAAG,OAAO;YAElC,SAAS;YACT,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,kBAAkB,CAAC,YAAY,EAAE,MAAM;QACzC;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS,GAAG,OAAO;YAEzB,eAAe;YACf,IAAI,aAAa,cAAc;gBAC7B,MAAM,QAAQ,CAAC;YACjB,OAAO,IAAI,aAAa,kBAAkB;gBACxC,MAAM,QAAQ,CAAC;YACjB,OAAO;gBACL,MAAM,QAAQ,CAAC;YACjB;QACF;QAEA,uBAAuB,CAAC;YACtB,IAAI;gBACF,oBAAoB;gBACpB,qBAAqB,eAAe,mBAAmB;YACzD,GAAG,OAAO;QACZ;QAEA,cAAc;QACd,mBAAmB,CAAC;YAClB,IAAI,CAAC,QAAU,CAAC;oBACd,iBAAiB;wBACf,GAAG,MAAM,eAAe;wBACxB,cAAc;+BAAI,MAAM,eAAe,CAAC,YAAY;4BAAE;yBAAY;oBACpE;gBACF,CAAC,GAAG,OAAO;QACb;QAEA,iBAAiB,CAAC,MAAM;YACtB,IAAI,CAAC,QAAU,CAAC;oBACd,iBAAiB;wBACf,GAAG,MAAM,eAAe;wBACxB,aAAa;wBACb,WAAW,MAAM,eAAe,CAAC,SAAS,GAAG;oBAC/C;gBACF,CAAC,GAAG,OAAO;QACb;QAEA,iBAAiB,CAAC;YAChB,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc;wBAAE,GAAG,MAAM,YAAY;wBAAE,GAAG,QAAQ;oBAAC;gBACrD,CAAC,GAAG,OAAO;QACb;QAEA,cAAc;QACd,YAAY;YACV,IAAI;gBAAE,gBAAgB;gBAAM,aAAa;YAAQ,GAAG,OAAO;QAC7D;QAEA,WAAW;YACT,IAAI;gBAAE,gBAAgB;YAAM,GAAG,OAAO;QACxC;QAEA,cAAc,CAAC;YACb,IAAI;gBACF,qBAAqB;gBACrB,aAAa;YACf,GAAG,OAAO;QACZ;QAEA,gBAAgB;YACd,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU,CAAC,MAAM,QAAQ;gBAAC,CAAC,GAAG,OAAO;QACzD;QAEA,YAAY;QACZ,oBAAoB,CAAC;YACnB,IAAI;gBAAE,iBAAiB;YAAK,GAAG,OAAO;QACxC;QAEA,wBAAwB;YACtB,IAAI,CAAC,QAAU,CAAC;oBACd,sBAAsB,CAAC,MAAM,oBAAoB;gBACnD,CAAC,GAAG,OAAO;QACb;QAEA,wBAAwB,CAAC;YACvB,IAAI;gBAAE,qBAAqB;YAAQ,GAAG,OAAO;QAC/C;QAEA,YAAY;QACZ,YAAY,CAAC,SAAS,UAAU,EAAE;YAChC,IAAI;gBAAE,WAAW;gBAAS,gBAAgB;YAAQ,GAAG,OAAO;QAC9D;QAEA,YAAY;QACZ,aAAa;YACX,IAAI,CAAC,QAAU,CAAC;oBAAE,cAAc,CAAC,MAAM,YAAY;gBAAC,CAAC,GAAG,OAAO;QACjE;QAEA,uBAAuB;YACrB,IAAI,CAAC,QAAU,CAAC;oBACd,wBAAwB,CAAC,MAAM,sBAAsB;gBACvD,CAAC,GAAG,OAAO;QACb;QAEA,YAAY;QACZ,eAAe;YACb,IAAI;gBACF,iBAAiB,aAAa,eAAe;gBAC7C,cAAc,CAAC;gBACf,oBAAoB;gBACpB,qBAAqB,EAAE;YACzB,GAAG,OAAO;QACZ;QAEA,iBAAiB;YACf,IAAI,cAAc,OAAO;QAC3B;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,oBAAoB;YACpB,OAAO,MAAM,KAAK;YAClB,YAAY,MAAM,UAAU;YAC5B,eAAe,MAAM,aAAa;YAClC,kBAAkB,MAAM,gBAAgB;YACxC,iBAAiB,MAAM,eAAe;YACtC,cAAc,MAAM,YAAY;YAChC,wBAAwB,MAAM,sBAAsB;YACpD,iBAAiB,MAAM,eAAe;YACtC,cAAc,MAAM,YAAY;QAClC,CAAC;AACH,IAEF;IACE,MAAM;AACR;AAKG,MAAM,cAAc,CAAC,QAAoB,MAAM,KAAK;AACpD,MAAM,iBAAiB,CAAC,QAAoB,MAAM,QAAQ;AAC1D,MAAM,oBAAoB,CAAC,QAAoB,MAAM,WAAW;AAChE,MAAM,kBAAkB,CAAC,QAAoB,MAAM,SAAS;AAC5D,MAAM,wBAAwB,CAAC,QAAoB,MAAM,eAAe;AACxE,MAAM,4BAA4B,CAAC,QAAoB,MAAM,mBAAmB", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/components/navigation/CyberNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAppStore } from '@/store/app-store';\nimport { Menu, X, Zap, Shield, Users, Phone, Settings } from 'lucide-react';\n\n/**\n * 🧭 赛博朋克导航组件\n */\nconst CyberNavigation: React.FC = () => {\n  const {\n    theme,\n    currentMode,\n    userType,\n    setCurrentMode,\n    setTheme,\n    recordInteraction\n  } = useAppStore();\n\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  // 🎯 滚动检测\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // 🎨 导航项配置\n  const navigationItems = [\n    {\n      id: 'intro',\n      label: '序章',\n      icon: Zap,\n      description: '神秘开场',\n      techLabel: '系统初始化',\n      businessLabel: '公司介绍'\n    },\n    {\n      id: 'products',\n      label: '产品',\n      icon: Shield,\n      description: '核心产品矩阵',\n      techLabel: '技术架构',\n      businessLabel: '解决方案'\n    },\n    {\n      id: 'capabilities',\n      label: '能力',\n      icon: Zap,\n      description: '攻防实力展示',\n      techLabel: '技术能力',\n      businessLabel: '服务能力'\n    },\n    {\n      id: 'cases',\n      label: '案例',\n      icon: Users,\n      description: '成功案例',\n      techLabel: '技术案例',\n      businessLabel: '商业案例'\n    },\n    {\n      id: 'contact',\n      label: '联系',\n      icon: Phone,\n      description: '加入我们',\n      techLabel: '技术交流',\n      businessLabel: '商务合作'\n    }\n  ];\n\n  // 🎬 动画变体\n  const navVariants = {\n    hidden: { y: -100, opacity: 0 },\n    visible: { \n      y: 0, \n      opacity: 1,\n      transition: { \n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  const menuVariants = {\n    closed: { \n      opacity: 0,\n      scale: 0.95,\n      y: -20\n    },\n    open: { \n      opacity: 1,\n      scale: 1,\n      y: 0,\n      transition: {\n        duration: 0.2,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  const itemVariants = {\n    closed: { opacity: 0, x: -20 },\n    open: (i: number) => ({\n      opacity: 1,\n      x: 0,\n      transition: {\n        delay: i * 0.1,\n        duration: 0.3\n      }\n    })\n  };\n\n  // 🎯 处理导航点击\n  const handleNavClick = (mode: string) => {\n    setCurrentMode(mode as any);\n    setIsMenuOpen(false);\n    recordInteraction(`navigate_${mode}`);\n  };\n\n  // 🎨 获取主题样式\n  const getNavStyles = () => {\n    const baseStyles = \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\";\n    \n    if (isScrolled) {\n      switch (theme) {\n        case 'mystical':\n          return `${baseStyles} bg-cyber-dark/90 backdrop-blur-md border-b border-cyber-green/30`;\n        case 'business':\n          return `${baseStyles} bg-white/90 backdrop-blur-md border-b border-gray-200`;\n        case 'adaptive':\n          return `${baseStyles} bg-slate-900/90 backdrop-blur-md border-b border-cyber-green/20`;\n        default:\n          return `${baseStyles} bg-cyber-dark/90 backdrop-blur-md border-b border-cyber-green/30`;\n      }\n    }\n    \n    return `${baseStyles} bg-transparent`;\n  };\n\n  const getTextColor = () => {\n    switch (theme) {\n      case 'mystical':\n        return 'text-cyber-green';\n      case 'business':\n        return 'text-slate-800';\n      case 'adaptive':\n        return 'text-cyber-green';\n      default:\n        return 'text-cyber-green';\n    }\n  };\n\n  const getAccentColor = () => {\n    switch (theme) {\n      case 'mystical':\n        return 'text-cyber-gold';\n      case 'business':\n        return 'text-blue-600';\n      case 'adaptive':\n        return 'text-cyber-gold';\n      default:\n        return 'text-cyber-gold';\n    }\n  };\n\n  return (\n    <motion.nav\n      className={getNavStyles()}\n      variants={navVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* 🔮 Logo区域 */}\n          <motion.div \n            className=\"flex items-center space-x-3 cursor-pointer\"\n            onClick={() => handleNavClick('intro')}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <div className=\"relative\">\n              <div className={`w-8 h-8 rounded-full border-2 ${theme === 'mystical' ? 'border-cyber-green' : 'border-current'} flex items-center justify-center`}>\n                <span className=\"text-sm font-bold\">☸️</span>\n              </div>\n              {theme === 'mystical' && (\n                <div className=\"absolute inset-0 rounded-full border-2 border-cyber-green animate-cyber-pulse opacity-50\" />\n              )}\n            </div>\n            <div>\n              <h1 className={`text-lg font-bold font-mono ${getTextColor()}`}>\n                神秘技术组织\n              </h1>\n              <p className={`text-xs ${getAccentColor()}`}>\n                技术驱动，安全至上，代码无界\n              </p>\n            </div>\n          </motion.div>\n\n          {/* 🖥️ 桌面端导航 */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = currentMode === item.id;\n              const label = userType === 'tech-track' ? item.techLabel : \n                           userType === 'business-track' ? item.businessLabel : \n                           item.label;\n\n              return (\n                <motion.button\n                  key={item.id}\n                  onClick={() => handleNavClick(item.id)}\n                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                    isActive \n                      ? `${theme === 'mystical' ? 'bg-cyber-green/20 text-cyber-green' : 'bg-blue-100 text-blue-600'} cyber-glow` \n                      : `${getTextColor()} hover:${theme === 'mystical' ? 'text-cyber-gold' : 'text-blue-600'}`\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Icon size={16} />\n                  <span className=\"font-mono text-sm\">{label}</span>\n                  {isActive && theme === 'mystical' && (\n                    <motion.div\n                      className=\"w-1 h-1 bg-cyber-green rounded-full\"\n                      animate={{ scale: [1, 1.5, 1] }}\n                      transition={{ duration: 1, repeat: Infinity }}\n                    />\n                  )}\n                </motion.button>\n              );\n            })}\n          </div>\n\n          {/* 🎨 主题切换和设置 */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {/* 主题切换 */}\n            <div className=\"flex items-center space-x-1 bg-black/20 rounded-lg p-1\">\n              {(['mystical', 'business', 'adaptive'] as const).map((themeOption) => (\n                <button\n                  key={themeOption}\n                  onClick={() => setTheme(themeOption)}\n                  className={`px-2 py-1 rounded text-xs font-mono transition-all ${\n                    theme === themeOption \n                      ? 'bg-cyber-green text-black' \n                      : `${getTextColor()} hover:bg-white/10`\n                  }`}\n                  title={`切换到${themeOption}主题`}\n                >\n                  {themeOption === 'mystical' ? '🔮' : \n                   themeOption === 'business' ? '💼' : '🌐'}\n                </button>\n              ))}\n            </div>\n\n            {/* 设置按钮 */}\n            <motion.button\n              className={`p-2 rounded-lg ${getTextColor()} hover:bg-white/10 transition-colors`}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              <Settings size={16} />\n            </motion.button>\n          </div>\n\n          {/* 📱 移动端菜单按钮 */}\n          <motion.button\n            className={`md:hidden p-2 rounded-lg ${getTextColor()}`}\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n          >\n            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </motion.button>\n        </div>\n      </div>\n\n      {/* 📱 移动端菜单 */}\n      <AnimatePresence>\n        {isMenuOpen && (\n          <motion.div\n            className={`md:hidden absolute top-full left-0 right-0 ${\n              theme === 'mystical' \n                ? 'bg-cyber-dark/95 border-cyber-green/30' \n                : 'bg-white/95 border-gray-200'\n            } backdrop-blur-md border-b`}\n            variants={menuVariants}\n            initial=\"closed\"\n            animate=\"open\"\n            exit=\"closed\"\n          >\n            <div className=\"px-4 py-6 space-y-4\">\n              {navigationItems.map((item, index) => {\n                const Icon = item.icon;\n                const isActive = currentMode === item.id;\n                const label = userType === 'tech-track' ? item.techLabel : \n                             userType === 'business-track' ? item.businessLabel : \n                             item.label;\n\n                return (\n                  <motion.button\n                    key={item.id}\n                    custom={index}\n                    variants={itemVariants}\n                    onClick={() => handleNavClick(item.id)}\n                    className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all ${\n                      isActive \n                        ? `${theme === 'mystical' ? 'bg-cyber-green/20 text-cyber-green' : 'bg-blue-100 text-blue-600'}` \n                        : `${getTextColor()} hover:bg-white/10`\n                    }`}\n                  >\n                    <Icon size={20} />\n                    <div className=\"text-left\">\n                      <div className=\"font-mono font-medium\">{label}</div>\n                      <div className={`text-xs ${getAccentColor()}`}>{item.description}</div>\n                    </div>\n                  </motion.button>\n                );\n              })}\n              \n              {/* 移动端主题切换 */}\n              <div className=\"pt-4 border-t border-current/20\">\n                <p className={`text-sm font-mono mb-2 ${getTextColor()}`}>主题选择</p>\n                <div className=\"flex space-x-2\">\n                  {(['mystical', 'business', 'adaptive'] as const).map((themeOption) => (\n                    <button\n                      key={themeOption}\n                      onClick={() => setTheme(themeOption)}\n                      className={`flex-1 py-2 rounded text-xs font-mono transition-all ${\n                        theme === themeOption \n                          ? 'bg-cyber-green text-black' \n                          : `${getTextColor()} border border-current/30`\n                      }`}\n                    >\n                      {themeOption === 'mystical' ? '🔮 神秘' : \n                       themeOption === 'business' ? '💼 商务' : '🌐 自适应'}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n};\n\nexport default CyberNavigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA;;CAEC,GACD,MAAM,kBAA4B;IAChC,MAAM,EACJ,KAAK,EACL,WAAW,EACX,QAAQ,EACR,cAAc,EACd,QAAQ,EACR,iBAAiB,EAClB,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,WAAW;IACX,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,OAAO;YACP,MAAM,gMAAA,CAAA,MAAG;YACT,aAAa;YACb,WAAW;YACX,eAAe;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,sMAAA,CAAA,SAAM;YACZ,aAAa;YACb,WAAW;YACX,eAAe;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,gMAAA,CAAA,MAAG;YACT,aAAa;YACb,WAAW;YACX,eAAe;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;YACb,WAAW;YACX,eAAe;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;YACb,WAAW;YACX,eAAe;QACjB;KACD;IAED,UAAU;IACV,MAAM,cAAc;QAClB,QAAQ;YAAE,GAAG,CAAC;YAAK,SAAS;QAAE;QAC9B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,OAAO;YACP,GAAG,CAAC;QACN;QACA,MAAM;YACJ,SAAS;YACT,OAAO;YACP,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,MAAM,CAAC,IAAc,CAAC;gBACpB,SAAS;gBACT,GAAG;gBACH,YAAY;oBACV,OAAO,IAAI;oBACX,UAAU;gBACZ;YACF,CAAC;IACH;IAEA,YAAY;IACZ,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,cAAc;QACd,kBAAkB,CAAC,SAAS,EAAE,MAAM;IACtC;IAEA,YAAY;IACZ,MAAM,eAAe;QACnB,MAAM,aAAa;QAEnB,IAAI,YAAY;YACd,OAAQ;gBACN,KAAK;oBACH,OAAO,GAAG,WAAW,iEAAiE,CAAC;gBACzF,KAAK;oBACH,OAAO,GAAG,WAAW,sDAAsD,CAAC;gBAC9E,KAAK;oBACH,OAAO,GAAG,WAAW,gEAAgE,CAAC;gBACxF;oBACE,OAAO,GAAG,WAAW,iEAAiE,CAAC;YAC3F;QACF;QAEA,OAAO,GAAG,WAAW,eAAe,CAAC;IACvC;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;;0BAER,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS,IAAM,eAAe;4BAC9B,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,8BAA8B,EAAE,UAAU,aAAa,uBAAuB,iBAAiB,iCAAiC,CAAC;sDAChJ,cAAA,8OAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;wCAErC,UAAU,4BACT,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGnB,8OAAC;;sDACC,8OAAC;4CAAG,WAAW,CAAC,4BAA4B,EAAE,gBAAgB;sDAAE;;;;;;sDAGhE,8OAAC;4CAAE,WAAW,CAAC,QAAQ,EAAE,kBAAkB;sDAAE;;;;;;;;;;;;;;;;;;sCAOjD,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,gBAAgB,KAAK,EAAE;gCACxC,MAAM,QAAQ,aAAa,eAAe,KAAK,SAAS,GAC3C,aAAa,mBAAmB,KAAK,aAAa,GAClD,KAAK,KAAK;gCAEvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS,IAAM,eAAe,KAAK,EAAE;oCACrC,WAAW,CAAC,6EAA6E,EACvF,WACI,GAAG,UAAU,aAAa,uCAAuC,4BAA4B,WAAW,CAAC,GACzG,GAAG,eAAe,OAAO,EAAE,UAAU,aAAa,oBAAoB,iBAAiB,EAC3F;oCACF,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,8OAAC;4CAAK,MAAM;;;;;;sDACZ,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;wCACpC,YAAY,UAAU,4BACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;;;;;;;mCAhB3C,KAAK,EAAE;;;;;4BAqBlB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,AAAC;wCAAC;wCAAY;wCAAY;qCAAW,CAAW,GAAG,CAAC,CAAC,4BACpD,8OAAC;4CAEC,SAAS,IAAM,SAAS;4CACxB,WAAW,CAAC,mDAAmD,EAC7D,UAAU,cACN,8BACA,GAAG,eAAe,kBAAkB,CAAC,EACzC;4CACF,OAAO,CAAC,GAAG,EAAE,YAAY,EAAE,CAAC;sDAE3B,gBAAgB,aAAa,OAC7B,gBAAgB,aAAa,OAAO;2CAVhC;;;;;;;;;;8CAgBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAW,CAAC,eAAe,EAAE,eAAe,oCAAoC,CAAC;oCACjF,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;8CAEvB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAKpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAW,CAAC,yBAAyB,EAAE,gBAAgB;4BACvD,SAAS,IAAM,cAAc,CAAC;4BAC9B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,UAAU;gCAAE,OAAO;4BAAI;sCAEtB,2BAAa,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;yFAAS,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAMlD,8OAAC,yLAAA,CAAA,kBAAe;0BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,CAAC,2CAA2C,EACrD,UAAU,aACN,2CACA,8BACL,0BAA0B,CAAC;oBAC5B,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;8BAEL,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM;gCAC1B,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,gBAAgB,KAAK,EAAE;gCACxC,MAAM,QAAQ,aAAa,eAAe,KAAK,SAAS,GAC3C,aAAa,mBAAmB,KAAK,aAAa,GAClD,KAAK,KAAK;gCAEvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,QAAQ;oCACR,UAAU;oCACV,SAAS,IAAM,eAAe,KAAK,EAAE;oCACrC,WAAW,CAAC,iEAAiE,EAC3E,WACI,GAAG,UAAU,aAAa,uCAAuC,6BAA6B,GAC9F,GAAG,eAAe,kBAAkB,CAAC,EACzC;;sDAEF,8OAAC;4CAAK,MAAM;;;;;;sDACZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAyB;;;;;;8DACxC,8OAAC;oDAAI,WAAW,CAAC,QAAQ,EAAE,kBAAkB;8DAAG,KAAK,WAAW;;;;;;;;;;;;;mCAb7D,KAAK,EAAE;;;;;4BAiBlB;0CAGA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAW,CAAC,uBAAuB,EAAE,gBAAgB;kDAAE;;;;;;kDAC1D,8OAAC;wCAAI,WAAU;kDACZ,AAAC;4CAAC;4CAAY;4CAAY;yCAAW,CAAW,GAAG,CAAC,CAAC,4BACpD,8OAAC;gDAEC,SAAS,IAAM,SAAS;gDACxB,WAAW,CAAC,qDAAqD,EAC/D,UAAU,cACN,8BACA,GAAG,eAAe,yBAAyB,CAAC,EAChD;0DAED,gBAAgB,aAAa,UAC7B,gBAAgB,aAAa,UAAU;+CATnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoB3B;uCAEe", "debugId": null}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/app/commercial/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, useScroll, useTransform, AnimatePresence, useInView } from 'framer-motion';\nimport CyberNavigation from '@/components/navigation/CyberNavigation';\nimport { Shield, Target, Eye, Zap, Users, Award, ChevronDown, Play, ArrowRight, CheckCircle, TrendingUp, ChevronRight } from 'lucide-react';\n\n/**\n * 🔴 商业红蓝对抗版本页面 - 现代化大气设计\n * 参考苹果和FireEye风格，分section展示\n */\nexport default function CommercialPage() {\n  const [activeSection, setActiveSection] = useState(0);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: containerRef,\n    offset: [\"start start\", \"end start\"]\n  });\n\n  const backgroundY = useTransform(scrollYProgress, [0, 1], [\"0%\", \"50%\"]);\n  const textY = useTransform(scrollYProgress, [0, 1], [\"0%\", \"200%\"]);\n\n  const services = [\n    {\n      icon: Shield,\n      title: \"红队攻击演练\",\n      subtitle: \"Red Team Operations\",\n      description: \"模拟真实攻击场景，全面测试企业安全防护能力\",\n      features: [\"APT攻击模拟\", \"社会工程学\", \"内网渗透\", \"0day利用\"]\n    },\n    {\n      icon: Target,\n      title: \"蓝队防御体系\",\n      subtitle: \"Blue Team Defense\",\n      description: \"构建完整的安全监控和响应体系\",\n      features: [\"威胁狩猎\", \"事件响应\", \"安全运营\", \"取证分析\"]\n    },\n    {\n      icon: Eye,\n      title: \"APT威胁分析\",\n      subtitle: \"Advanced Persistent Threat\",\n      description: \"深度分析高级持续性威胁，提供专业情报支持\",\n      features: [\"威胁情报\", \"恶意软件分析\", \"攻击链重构\", \"归因分析\"]\n    }\n  ];\n\n  return (\n    <div ref={containerRef} className=\"relative bg-slate-900\">\n      <CyberNavigation />\n\n      {/* Hero Section - 大气首屏 */}\n      <section className=\"relative h-screen flex items-center justify-center overflow-hidden\">\n        {/* 动态背景 */}\n        <motion.div\n          className=\"absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800\"\n          style={{ y: backgroundY }}\n        />\n\n        {/* 粒子效果背景 */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]\" />\n          <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(239,68,68,0.1),transparent_50%)]\" />\n          <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(34,197,94,0.1),transparent_50%)]\" />\n        </div>\n\n        {/* 网格背景 */}\n        <div className=\"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]\" />\n\n        {/* 主要内容 */}\n        <motion.div\n          className=\"relative z-10 text-center max-w-6xl mx-auto px-4\"\n          style={{ y: textY }}\n        >\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.2 }}\n            className=\"mb-8\"\n          >\n            <motion.div\n              initial={{ scale: 0.8, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              transition={{ duration: 0.8, delay: 0.1 }}\n              className=\"mb-6\"\n            >\n              <span className=\"inline-block px-4 py-2 bg-blue-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-mono mb-6\">\n                Elite Cyber Security Solutions\n              </span>\n            </motion.div>\n\n            <h1 className=\"text-5xl md:text-7xl lg:text-8xl font-bold text-white mb-6 tracking-tight leading-none\">\n              <span className=\"bg-gradient-to-r from-blue-400 via-purple-400 to-red-400 bg-clip-text text-transparent\">\n                世界级\n              </span>\n              <br />\n              <span className=\"text-white\">红蓝对抗</span>\n            </h1>\n            <p className=\"text-xl md:text-2xl text-slate-300 font-light mb-8 max-w-4xl mx-auto leading-relaxed\">\n              专业的网络安全攻防演练服务，帮助企业构建坚不可摧的安全防线\n            </p>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\"\n          >\n            <motion.button\n              className=\"group px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold text-lg hover:shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <span className=\"flex items-center gap-2\">\n                <Play size={20} />\n                观看演示\n                <ArrowRight size={16} className=\"group-hover:translate-x-1 transition-transform\" />\n              </span>\n            </motion.button>\n            <motion.button\n              className=\"px-8 py-4 border-2 border-blue-400 text-blue-400 rounded-full font-semibold text-lg hover:bg-blue-400 hover:text-white transition-all duration-300\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              联系我们\n            </motion.button>\n          </motion.div>\n        </motion.div>\n\n        {/* 滚动指示器 */}\n        <motion.div\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          <ChevronDown className=\"text-white/60\" size={32} />\n        </motion.div>\n      </section>\n\n      {/* Transition Section - 核心能力过渡 */}\n      <section className=\"relative h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900/30 to-slate-900 overflow-hidden\">\n        {/* 网格背景 */}\n        <div className=\"absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:100px_100px] animate-pulse\" />\n\n        {/* 中心光晕 */}\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(59,130,246,0.2),transparent_70%)]\" />\n\n        {/* 动态六边形 */}\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          {[...Array(6)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute border-2 border-blue-400/30\"\n              style={{\n                width: 200 + i * 100,\n                height: 200 + i * 100,\n                clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',\n              }}\n              animate={{\n                rotate: [0, 360],\n                scale: [1, 1.1, 1],\n              }}\n              transition={{\n                duration: 10 + i * 2,\n                repeat: Infinity,\n                ease: \"linear\",\n              }}\n            />\n          ))}\n        </div>\n\n        {/* 主标题 */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0.5 }}\n          whileInView={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 2, ease: \"easeOut\" }}\n          viewport={{ once: true }}\n          className=\"relative z-10 text-center\"\n        >\n          <motion.div\n            initial={{ opacity: 0, y: 100 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1.5, delay: 0.5 }}\n            viewport={{ once: true }}\n          >\n            <h1 className=\"text-8xl md:text-9xl font-black text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-cyan-300 to-blue-500 mb-8 tracking-wider\">\n              CORE\n            </h1>\n            <h2 className=\"text-6xl md:text-7xl font-bold text-white mb-6 tracking-wide\">\n              CAPABILITIES\n            </h2>\n            <div className=\"w-32 h-1 bg-gradient-to-r from-blue-400 to-cyan-400 mx-auto mb-8\" />\n            <p className=\"text-2xl text-blue-200 font-light tracking-widest\">\n              核心技术能力展示\n            </p>\n          </motion.div>\n        </motion.div>\n\n        {/* 底部指示器 */}\n        <motion.div\n          className=\"absolute bottom-16 left-1/2 transform -translate-x-1/2\"\n          animate={{ y: [0, 20, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          <div className=\"flex flex-col items-center\">\n            <div className=\"w-px h-16 bg-gradient-to-b from-blue-400 to-transparent mb-4\" />\n            <ChevronDown className=\"text-blue-400\" size={24} />\n          </div>\n        </motion.div>\n      </section>\n\n      {/* Services Section - 服务展示 */}\n      <section className=\"relative min-h-screen py-40 bg-slate-800/50 flex items-center\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Section Header */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-24\"\n          >\n            <div className=\"mb-8\">\n              <span className=\"inline-block px-4 py-2 bg-blue-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-mono\">\n                Core Services\n              </span>\n            </div>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-8\">\n              核心服务能力\n            </h2>\n            <p className=\"text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed\">\n              提供全方位的网络安全攻防演练服务，从红队攻击到蓝队防御，全面提升企业安全能力\n            </p>\n          </motion.div>\n\n          {/* Services Grid */}\n          <div className=\"grid md:grid-cols-3 gap-12\">\n            {services.map((service, index) => {\n              const Icon = service.icon;\n              return (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 50 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.2 }}\n                  viewport={{ once: true }}\n                  className=\"group relative\"\n                >\n                  <div className=\"relative p-8 bg-slate-900/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl hover:border-blue-500/50 transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/10\">\n                    {/* Icon */}\n                    <div className=\"mb-6\">\n                      <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                        <Icon className=\"text-white\" size={32} />\n                      </div>\n                    </div>\n\n                    {/* Content */}\n                    <h3 className=\"text-2xl font-bold text-white mb-2\">\n                      {service.title}\n                    </h3>\n                    <p className=\"text-blue-400 font-mono text-sm mb-4\">\n                      {service.subtitle}\n                    </p>\n                    <p className=\"text-slate-300 mb-6 leading-relaxed\">\n                      {service.description}\n                    </p>\n\n                    {/* Features */}\n                    <ul className=\"space-y-2\">\n                      {service.features.map((feature, featureIndex) => (\n                        <li key={featureIndex} className=\"flex items-center text-slate-400\">\n                          <CheckCircle className=\"text-green-400 mr-2\" size={16} />\n                          <span className=\"text-sm\">{feature}</span>\n                        </li>\n                      ))}\n                    </ul>\n\n                    {/* Hover Effect */}\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                  </div>\n                </motion.div>\n              );\n            })}\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Transition - 专业数据过渡 */}\n      <section className=\"relative h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-green-900/20 to-slate-900 overflow-hidden\">\n        {/* 数据矩阵背景 */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute inset-0 bg-[linear-gradient(rgba(34,197,94,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(34,197,94,0.1)_1px,transparent_1px)] bg-[size:80px_80px]\" />\n        </div>\n\n        {/* 中心数据环 */}\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          {[...Array(4)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute border border-green-400/40 rounded-full\"\n              style={{\n                width: 300 + i * 150,\n                height: 300 + i * 150,\n              }}\n              animate={{\n                rotate: [0, 360],\n                opacity: [0.2, 0.6, 0.2],\n              }}\n              transition={{\n                duration: 8 + i * 2,\n                repeat: Infinity,\n                ease: \"linear\",\n              }}\n            />\n          ))}\n        </div>\n\n        {/* 浮动数据点 */}\n        <div className=\"absolute inset-0\">\n          {['99.9%', '500+', '24/7', '<5min'].map((stat, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute text-green-400 font-mono text-2xl font-bold\"\n              style={{\n                left: `${20 + i * 20}%`,\n                top: `${30 + (i % 2) * 40}%`,\n              }}\n              animate={{\n                y: [0, -30, 0],\n                opacity: [0.3, 1, 0.3],\n                scale: [0.8, 1.2, 0.8],\n              }}\n              transition={{\n                duration: 3,\n                repeat: Infinity,\n                delay: i * 0.5,\n              }}\n            >\n              {stat}\n            </motion.div>\n          ))}\n        </div>\n\n        {/* 主标题 */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0.5 }}\n          whileInView={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 2, ease: \"easeOut\" }}\n          viewport={{ once: true }}\n          className=\"relative z-10 text-center\"\n        >\n          <motion.div\n            initial={{ opacity: 0, y: 100 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1.5, delay: 0.5 }}\n            viewport={{ once: true }}\n          >\n            <h1 className=\"text-8xl md:text-9xl font-black text-transparent bg-clip-text bg-gradient-to-r from-green-400 via-emerald-300 to-green-500 mb-8 tracking-wider\">\n              DATA\n            </h1>\n            <h2 className=\"text-6xl md:text-7xl font-bold text-white mb-6 tracking-wide\">\n              ANALYTICS\n            </h2>\n            <div className=\"w-32 h-1 bg-gradient-to-r from-green-400 to-emerald-400 mx-auto mb-8\" />\n            <p className=\"text-2xl text-green-200 font-light tracking-widest\">\n              专业数据分析展示\n            </p>\n          </motion.div>\n        </motion.div>\n\n        {/* 底部指示器 */}\n        <motion.div\n          className=\"absolute bottom-16 left-1/2 transform -translate-x-1/2\"\n          animate={{ y: [0, 20, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          <div className=\"flex flex-col items-center\">\n            <div className=\"w-px h-16 bg-gradient-to-b from-green-400 to-transparent mb-4\" />\n            <TrendingUp className=\"text-green-400\" size={24} />\n          </div>\n        </motion.div>\n      </section>\n\n      {/* Stats Section - 数据展示 */}\n      <section className=\"relative min-h-screen py-32 bg-gradient-to-r from-blue-900/20 to-purple-900/20 backdrop-blur-sm flex items-center\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Stats Header */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n              专业数据展示\n            </h2>\n            <p className=\"text-slate-400 max-w-2xl mx-auto\">\n              用数据说话，展示我们的专业实力和服务质量\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-4 gap-12 text-center\">\n            {[\n              { number: \"7x24\", label: \"全天候监控\", icon: Eye, color: \"text-blue-400\" },\n              { number: \"99.9%\", label: \"威胁检测率\", icon: Target, color: \"text-green-400\" },\n              { number: \"< 3min\", label: \"应急响应\", icon: Zap, color: \"text-yellow-400\" },\n              { number: \"500+\", label: \"成功案例\", icon: Award, color: \"text-purple-400\" }\n            ].map((stat, index) => {\n              const Icon = stat.icon;\n              return (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"group relative\"\n                >\n                  {/* 背景光晕 */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n\n                  <div className=\"relative p-8\">\n                    <div className=\"mb-6\">\n                      <Icon className={`mx-auto ${stat.color} group-hover:scale-110 transition-all duration-300`} size={40} />\n                    </div>\n                    <div className={`text-4xl md:text-5xl font-black ${stat.color} mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                      {stat.number}\n                    </div>\n                    <div className=\"text-slate-300 text-lg font-medium\">\n                      {stat.label}\n                    </div>\n\n                    {/* 底部装饰线 */}\n                    <div className={`w-16 h-1 ${stat.color.replace('text-', 'bg-')} mx-auto mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300`} />\n                  </div>\n                </motion.div>\n              );\n            })}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Transition - 立即行动过渡 */}\n      <section className=\"relative h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-orange-900/20 to-slate-900 overflow-hidden\">\n        {/* 能量场背景 */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(249,115,22,0.2),transparent_60%)]\" />\n          <div className=\"absolute inset-0 bg-[linear-gradient(45deg,rgba(249,115,22,0.1)_1px,transparent_1px),linear-gradient(-45deg,rgba(249,115,22,0.1)_1px,transparent_1px)] bg-[size:60px_60px]\" />\n        </div>\n\n        {/* 中心能量环 */}\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          {[...Array(3)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute border-2 border-orange-400/50 rounded-full\"\n              style={{\n                width: 400 + i * 200,\n                height: 400 + i * 200,\n              }}\n              animate={{\n                scale: [1, 1.2, 1],\n                opacity: [0.3, 0.8, 0.3],\n                rotate: [0, 180, 360],\n              }}\n              transition={{\n                duration: 4 + i,\n                repeat: Infinity,\n                ease: \"easeInOut\",\n              }}\n            />\n          ))}\n        </div>\n\n        {/* 闪电效果 */}\n        <div className=\"absolute inset-0\">\n          {[...Array(8)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute w-1 bg-gradient-to-b from-yellow-400 to-orange-500\"\n              style={{\n                left: `${10 + i * 10}%`,\n                top: '20%',\n                height: '60%',\n                transformOrigin: 'top',\n              }}\n              animate={{\n                scaleY: [0, 1, 0],\n                opacity: [0, 1, 0],\n              }}\n              transition={{\n                duration: 0.5,\n                repeat: Infinity,\n                delay: i * 0.2,\n                repeatDelay: 2,\n              }}\n            />\n          ))}\n        </div>\n\n        {/* 主标题 */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0.5 }}\n          whileInView={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 2, ease: \"easeOut\" }}\n          viewport={{ once: true }}\n          className=\"relative z-10 text-center\"\n        >\n          <motion.div\n            initial={{ opacity: 0, y: 100 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1.5, delay: 0.5 }}\n            viewport={{ once: true }}\n          >\n            <h1 className=\"text-8xl md:text-9xl font-black text-transparent bg-clip-text bg-gradient-to-r from-orange-400 via-red-400 to-yellow-400 mb-8 tracking-wider\">\n              ACTION\n            </h1>\n            <h2 className=\"text-6xl md:text-7xl font-bold text-white mb-6 tracking-wide\">\n              REQUIRED\n            </h2>\n            <div className=\"w-32 h-1 bg-gradient-to-r from-orange-400 to-red-400 mx-auto mb-8\" />\n            <p className=\"text-2xl text-orange-200 font-light tracking-widest\">\n              立即开始行动计划\n            </p>\n          </motion.div>\n        </motion.div>\n\n        {/* 底部指示器 */}\n        <motion.div\n          className=\"absolute bottom-16 left-1/2 transform -translate-x-1/2\"\n          animate={{\n            y: [0, 20, 0],\n            scale: [1, 1.2, 1],\n          }}\n          transition={{ duration: 1.5, repeat: Infinity }}\n        >\n          <div className=\"flex flex-col items-center\">\n            <div className=\"w-px h-16 bg-gradient-to-b from-orange-400 to-transparent mb-4\" />\n            <Zap className=\"text-orange-400\" size={24} />\n          </div>\n        </motion.div>\n      </section>\n\n      {/* CTA Section - 行动召唤 */}\n      <section className=\"relative min-h-screen py-40 bg-slate-900 flex items-center\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"mb-8\">\n              <span className=\"inline-block px-4 py-2 bg-green-500/10 border border-green-500/20 rounded-full text-green-400 text-sm font-mono\">\n                Ready to Start\n              </span>\n            </div>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-8\">\n              准备好提升您的安全防护了吗？\n            </h2>\n            <p className=\"text-xl text-slate-400 mb-12 max-w-2xl mx-auto leading-relaxed\">\n              联系我们的专家团队，获取定制化的网络安全解决方案\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <motion.button\n                className=\"px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold text-lg hover:shadow-2xl hover:shadow-blue-500/25 transition-all duration-300\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                立即咨询\n              </motion.button>\n              <motion.button\n                onClick={() => window.location.href = '/'}\n                className=\"px-8 py-4 border-2 border-slate-600 text-slate-300 rounded-full font-semibold text-lg hover:border-slate-400 hover:text-white transition-all duration-300\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                返回主页\n              </motion.button>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAe;SAAY;IACtC;IAEA,MAAM,cAAc,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAM;KAAM;IACvE,MAAM,QAAQ,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAM;KAAO;IAElE,MAAM,WAAW;QACf;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;gBAAC;gBAAW;gBAAS;gBAAQ;aAAS;QAClD;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;gBAAC;gBAAQ;gBAAQ;gBAAQ;aAAO;QAC5C;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,UAAU;YACV,aAAa;YACb,UAAU;gBAAC;gBAAQ;gBAAU;gBAAS;aAAO;QAC/C;KACD;IAED,qBACE,8OAAC;QAAI,KAAK;QAAc,WAAU;;0BAChC,8OAAC,mJAAA,CAAA,UAAe;;;;;0BAGhB,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BAAE,GAAG;wBAAY;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BAAE,GAAG;wBAAM;;0CAElB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;gCACtC,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,OAAO;4CAAK,SAAS;wCAAE;wCAClC,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAAoH;;;;;;;;;;;kDAKtI,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAyF;;;;;;0DAGzG,8OAAC;;;;;0DACD,8OAAC;gDAAK,WAAU;0DAAa;;;;;;;;;;;;kDAE/B,8OAAC;wCAAE,WAAU;kDAAuF;;;;;;;;;;;;0CAKtG,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;;;;;;gDAAM;8DAElB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;;;;;;kDAGpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDACzB;;;;;;;;;;;;;;;;;;kCAOL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;kCAE5C,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;4BAAgB,MAAM;;;;;;;;;;;;;;;;;0BAKjD,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,OAAO,MAAM,IAAI;oCACjB,QAAQ,MAAM,IAAI;oCAClB,UAAU;gCACZ;gCACA,SAAS;oCACP,QAAQ;wCAAC;wCAAG;qCAAI;oCAChB,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU,KAAK,IAAI;oCACnB,QAAQ;oCACR,MAAM;gCACR;+BAfK;;;;;;;;;;kCAqBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,aAAa;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBACpC,YAAY;4BAAE,UAAU;4BAAG,MAAM;wBAAU;wBAC3C,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAI;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAA4I;;;;;;8CAG1J,8OAAC;oCAAG,WAAU;8CAA+D;;;;;;8CAG7E,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAoD;;;;;;;;;;;;;;;;;kCAOrE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;kCAE5C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;oCAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+G;;;;;;;;;;;8CAIjI,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,8OAAC;oCAAE,WAAU;8CAA2D;;;;;;;;;;;;sCAM1E,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS;gCACtB,MAAM,OAAO,QAAQ,IAAI;gCACzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;wDAAa,MAAM;;;;;;;;;;;;;;;;0DAKvC,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,QAAQ;;;;;;0DAEnB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAItB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;gEAAsB,MAAM;;;;;;0EACnD,8OAAC;gEAAK,WAAU;0EAAW;;;;;;;uDAFpB;;;;;;;;;;0DAQb,8OAAC;gDAAI,WAAU;;;;;;;;;;;;mCArCZ;;;;;4BAyCX;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,OAAO,MAAM,IAAI;oCACjB,QAAQ,MAAM,IAAI;gCACpB;gCACA,SAAS;oCACP,QAAQ;wCAAC;wCAAG;qCAAI;oCAChB,SAAS;wCAAC;wCAAK;wCAAK;qCAAI;gCAC1B;gCACA,YAAY;oCACV,UAAU,IAAI,IAAI;oCAClB,QAAQ;oCACR,MAAM;gCACR;+BAdK;;;;;;;;;;kCAoBX,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAS;4BAAQ;4BAAQ;yBAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,kBAC7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;oCACvB,KAAK,GAAG,KAAK,AAAC,IAAI,IAAK,GAAG,CAAC,CAAC;gCAC9B;gCACA,SAAS;oCACP,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;oCACd,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;oCACtB,OAAO;wCAAC;wCAAK;wCAAK;qCAAI;gCACxB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,OAAO,IAAI;gCACb;0CAEC;+BAjBI;;;;;;;;;;kCAuBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,aAAa;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBACpC,YAAY;4BAAE,UAAU;4BAAG,MAAM;wBAAU;wBAC3C,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAI;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAAiJ;;;;;;8CAG/J,8OAAC;oCAAG,WAAU;8CAA+D;;;;;;8CAG7E,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;;;;;;;;;;;;kCAOtE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;kCAE5C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;oCAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;sCAKlD,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,QAAQ;oCAAQ,OAAO;oCAAS,MAAM,gMAAA,CAAA,MAAG;oCAAE,OAAO;gCAAgB;gCACpE;oCAAE,QAAQ;oCAAS,OAAO;oCAAS,MAAM,sMAAA,CAAA,SAAM;oCAAE,OAAO;gCAAiB;gCACzE;oCAAE,QAAQ;oCAAU,OAAO;oCAAQ,MAAM,gMAAA,CAAA,MAAG;oCAAE,OAAO;gCAAkB;gCACvE;oCAAE,QAAQ;oCAAQ,OAAO;oCAAQ,MAAM,oMAAA,CAAA,QAAK;oCAAE,OAAO;gCAAkB;6BACxE,CAAC,GAAG,CAAC,CAAC,MAAM;gCACX,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAGV,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,kDAAkD,CAAC;wDAAE,MAAM;;;;;;;;;;;8DAEpG,8OAAC;oDAAI,WAAW,CAAC,gCAAgC,EAAE,KAAK,KAAK,CAAC,6DAA6D,CAAC;8DACzH,KAAK,MAAM;;;;;;8DAEd,8OAAC;oDAAI,WAAU;8DACZ,KAAK,KAAK;;;;;;8DAIb,8OAAC;oDAAI,WAAW,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,SAAS,OAAO,+EAA+E,CAAC;;;;;;;;;;;;;mCAtB5I;;;;;4BA0BX;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,OAAO,MAAM,IAAI;oCACjB,QAAQ,MAAM,IAAI;gCACpB;gCACA,SAAS;oCACP,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;oCAClB,SAAS;wCAAC;wCAAK;wCAAK;qCAAI;oCACxB,QAAQ;wCAAC;wCAAG;wCAAK;qCAAI;gCACvB;gCACA,YAAY;oCACV,UAAU,IAAI;oCACd,QAAQ;oCACR,MAAM;gCACR;+BAfK;;;;;;;;;;kCAqBX,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;oCACvB,KAAK;oCACL,QAAQ;oCACR,iBAAiB;gCACnB;gCACA,SAAS;oCACP,QAAQ;wCAAC;wCAAG;wCAAG;qCAAE;oCACjB,SAAS;wCAAC;wCAAG;wCAAG;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,OAAO,IAAI;oCACX,aAAa;gCACf;+BAjBK;;;;;;;;;;kCAuBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,aAAa;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBACpC,YAAY;4BAAE,UAAU;4BAAG,MAAM;wBAAU;wBAC3C,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAI;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAA+I;;;;;;8CAG7J,8OAAC;oCAAG,WAAU;8CAA+D;;;;;;8CAG7E,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAsD;;;;;;;;;;;;;;;;;kCAOvE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;4BACb,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BAAE,UAAU;4BAAK,QAAQ;wBAAS;kCAE9C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;oCAAkB,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAM7C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAkH;;;;;;;;;;;0CAIpI,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAAiE;;;;;;0CAG9E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDACzB;;;;;;kDAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wCACtC,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}