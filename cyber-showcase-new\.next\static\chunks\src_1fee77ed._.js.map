{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/store/app-store.ts"], "sourcesContent": ["/**\n * 🔮 赛博朋克展示平台 - 全局状态管理\n * 使用Zustand实现轻量级状态管理\n */\n\nimport { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { UserType, PathRecommendation, UserBehavior } from '@/lib/path-detector';\n\n// 🎨 主题类型定义\nexport type Theme = 'mystical' | 'business' | 'adaptive';\n\n// 🌟 展示模式定义\nexport type ShowcaseMode = 'intro' | 'products' | 'capabilities' | 'cases' | 'contact';\n\n// 📊 产品类型定义\nexport type ProductType = 'zencode' | 'spore' | 'training';\n\n// 🎯 用户交互状态\nexport interface UserInteraction {\n  currentPage: string;\n  timeSpent: number;\n  interactions: string[];\n  preferences: Record<string, any>;\n}\n\n// 🔧 应用状态接口\nexport interface AppState {\n  // 🎨 主题和外观\n  theme: Theme;\n  isDarkMode: boolean;\n  showParticles: boolean;\n  enableAnimations: boolean;\n  \n  // 🧭 导航和路径\n  currentMode: ShowcaseMode;\n  userType: UserType;\n  pathRecommendation: PathRecommendation | null;\n  \n  // 📱 用户交互\n  userInteraction: UserInteraction;\n  behaviorData: Partial<UserBehavior>;\n  \n  // 🎬 展示控制\n  isIntroPlaying: boolean;\n  currentProductFocus: ProductType | null;\n  demoMode: boolean;\n  \n  // 📊 数据和内容\n  contentLanguage: 'zh' | 'en';\n  showTechnicalDetails: boolean;\n  personalizedContent: string[];\n  \n  // 🔄 加载状态\n  isLoading: boolean;\n  loadingMessage: string;\n  \n  // 🎵 音效控制\n  soundEnabled: boolean;\n  backgroundMusicEnabled: boolean;\n}\n\n// ⚡ 状态操作接口\nexport interface AppActions {\n  // 🎨 主题操作\n  setTheme: (theme: Theme) => void;\n  toggleDarkMode: () => void;\n  toggleParticles: () => void;\n  toggleAnimations: () => void;\n  \n  // 🧭 导航操作\n  setCurrentMode: (mode: ShowcaseMode) => void;\n  setUserType: (userType: UserType) => void;\n  setPathRecommendation: (recommendation: PathRecommendation) => void;\n  \n  // 📱 用户交互操作\n  recordInteraction: (interaction: string) => void;\n  updateTimeSpent: (page: string, time: number) => void;\n  setBehaviorData: (behavior: Partial<UserBehavior>) => void;\n  \n  // 🎬 展示控制操作\n  startIntro: () => void;\n  stopIntro: () => void;\n  focusProduct: (product: ProductType) => void;\n  toggleDemoMode: () => void;\n  \n  // 📊 内容操作\n  setContentLanguage: (lang: 'zh' | 'en') => void;\n  toggleTechnicalDetails: () => void;\n  setPersonalizedContent: (content: string[]) => void;\n  \n  // 🔄 加载操作\n  setLoading: (loading: boolean, message?: string) => void;\n  \n  // 🎵 音效操作\n  toggleSound: () => void;\n  toggleBackgroundMusic: () => void;\n  \n  // 🔄 重置操作\n  resetUserData: () => void;\n  resetToDefaults: () => void;\n}\n\n// 🏪 完整的Store类型\nexport type AppStore = AppState & AppActions;\n\n// 🌟 默认状态\nconst defaultState: AppState = {\n  // 🎨 主题和外观\n  theme: 'mystical',\n  isDarkMode: true,\n  showParticles: true,\n  enableAnimations: true,\n  \n  // 🧭 导航和路径\n  currentMode: 'intro',\n  userType: 'mixed-track',\n  pathRecommendation: null,\n  \n  // 📱 用户交互\n  userInteraction: {\n    currentPage: '/',\n    timeSpent: 0,\n    interactions: [],\n    preferences: {}\n  },\n  behaviorData: {},\n  \n  // 🎬 展示控制\n  isIntroPlaying: false,\n  currentProductFocus: null,\n  demoMode: false,\n  \n  // 📊 数据和内容\n  contentLanguage: 'zh',\n  showTechnicalDetails: false,\n  personalizedContent: [],\n  \n  // 🔄 加载状态\n  isLoading: false,\n  loadingMessage: '',\n  \n  // 🎵 音效控制\n  soundEnabled: true,\n  backgroundMusicEnabled: false\n};\n\n// 🔮 创建Zustand Store\nexport const useAppStore = create<AppStore>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        ...defaultState,\n        \n        // 🎨 主题操作实现\n        setTheme: (theme) => {\n          set({ theme }, false, 'setTheme');\n          \n          // 根据主题自动调整其他设置\n          if (theme === 'business') {\n            set({ \n              showParticles: false, \n              enableAnimations: false,\n              isDarkMode: false \n            });\n          } else if (theme === 'mystical') {\n            set({ \n              showParticles: true, \n              enableAnimations: true,\n              isDarkMode: true \n            });\n          }\n        },\n        \n        toggleDarkMode: () => {\n          set((state) => ({ isDarkMode: !state.isDarkMode }), false, 'toggleDarkMode');\n        },\n        \n        toggleParticles: () => {\n          set((state) => ({ showParticles: !state.showParticles }), false, 'toggleParticles');\n        },\n        \n        toggleAnimations: () => {\n          set((state) => ({ enableAnimations: !state.enableAnimations }), false, 'toggleAnimations');\n        },\n        \n        // 🧭 导航操作实现\n        setCurrentMode: (mode) => {\n          set({ currentMode: mode }, false, 'setCurrentMode');\n          \n          // 记录页面访问\n          const { recordInteraction } = get();\n          recordInteraction(`navigate_to_${mode}`);\n        },\n        \n        setUserType: (userType) => {\n          set({ userType }, false, 'setUserType');\n          \n          // 根据用户类型自动调整主题\n          if (userType === 'tech-track') {\n            get().setTheme('mystical');\n          } else if (userType === 'business-track') {\n            get().setTheme('business');\n          } else {\n            get().setTheme('adaptive');\n          }\n        },\n        \n        setPathRecommendation: (recommendation) => {\n          set({ \n            pathRecommendation: recommendation,\n            personalizedContent: recommendation.personalizedContent \n          }, false, 'setPathRecommendation');\n        },\n        \n        // 📱 用户交互操作实现\n        recordInteraction: (interaction) => {\n          set((state) => ({\n            userInteraction: {\n              ...state.userInteraction,\n              interactions: [...state.userInteraction.interactions, interaction]\n            }\n          }), false, 'recordInteraction');\n        },\n        \n        updateTimeSpent: (page, time) => {\n          set((state) => ({\n            userInteraction: {\n              ...state.userInteraction,\n              currentPage: page,\n              timeSpent: state.userInteraction.timeSpent + time\n            }\n          }), false, 'updateTimeSpent');\n        },\n        \n        setBehaviorData: (behavior) => {\n          set((state) => ({\n            behaviorData: { ...state.behaviorData, ...behavior }\n          }), false, 'setBehaviorData');\n        },\n        \n        // 🎬 展示控制操作实现\n        startIntro: () => {\n          set({ isIntroPlaying: true, currentMode: 'intro' }, false, 'startIntro');\n        },\n        \n        stopIntro: () => {\n          set({ isIntroPlaying: false }, false, 'stopIntro');\n        },\n        \n        focusProduct: (product) => {\n          set({ \n            currentProductFocus: product,\n            currentMode: 'products' \n          }, false, 'focusProduct');\n        },\n        \n        toggleDemoMode: () => {\n          set((state) => ({ demoMode: !state.demoMode }), false, 'toggleDemoMode');\n        },\n        \n        // 📊 内容操作实现\n        setContentLanguage: (lang) => {\n          set({ contentLanguage: lang }, false, 'setContentLanguage');\n        },\n        \n        toggleTechnicalDetails: () => {\n          set((state) => ({ \n            showTechnicalDetails: !state.showTechnicalDetails \n          }), false, 'toggleTechnicalDetails');\n        },\n        \n        setPersonalizedContent: (content) => {\n          set({ personalizedContent: content }, false, 'setPersonalizedContent');\n        },\n        \n        // 🔄 加载操作实现\n        setLoading: (loading, message = '') => {\n          set({ isLoading: loading, loadingMessage: message }, false, 'setLoading');\n        },\n        \n        // 🎵 音效操作实现\n        toggleSound: () => {\n          set((state) => ({ soundEnabled: !state.soundEnabled }), false, 'toggleSound');\n        },\n        \n        toggleBackgroundMusic: () => {\n          set((state) => ({ \n            backgroundMusicEnabled: !state.backgroundMusicEnabled \n          }), false, 'toggleBackgroundMusic');\n        },\n        \n        // 🔄 重置操作实现\n        resetUserData: () => {\n          set({\n            userInteraction: defaultState.userInteraction,\n            behaviorData: {},\n            pathRecommendation: null,\n            personalizedContent: []\n          }, false, 'resetUserData');\n        },\n        \n        resetToDefaults: () => {\n          set(defaultState, false, 'resetToDefaults');\n        }\n      }),\n      {\n        name: 'cyber-showcase-store',\n        partialize: (state) => ({\n          // 只持久化用户偏好，不持久化临时状态\n          theme: state.theme,\n          isDarkMode: state.isDarkMode,\n          showParticles: state.showParticles,\n          enableAnimations: state.enableAnimations,\n          contentLanguage: state.contentLanguage,\n          soundEnabled: state.soundEnabled,\n          backgroundMusicEnabled: state.backgroundMusicEnabled,\n          userInteraction: state.userInteraction,\n          behaviorData: state.behaviorData\n        })\n      }\n    ),\n    {\n      name: 'cyber-showcase-store'\n    }\n  )\n);\n\n// 🎯 选择器函数 - 用于优化性能\nexport const selectTheme = (state: AppStore) => state.theme;\nexport const selectUserType = (state: AppStore) => state.userType;\nexport const selectCurrentMode = (state: AppStore) => state.currentMode;\nexport const selectIsLoading = (state: AppStore) => state.isLoading;\nexport const selectUserInteraction = (state: AppStore) => state.userInteraction;\nexport const selectPersonalizedContent = (state: AppStore) => state.personalizedContent;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAED;AACA;;;AAoGA,UAAU;AACV,MAAM,eAAyB;IAC7B,WAAW;IACX,OAAO;IACP,YAAY;IACZ,eAAe;IACf,kBAAkB;IAElB,WAAW;IACX,aAAa;IACb,UAAU;IACV,oBAAoB;IAEpB,UAAU;IACV,iBAAiB;QACf,aAAa;QACb,WAAW;QACX,cAAc,EAAE;QAChB,aAAa,CAAC;IAChB;IACA,cAAc,CAAC;IAEf,UAAU;IACV,gBAAgB;IAChB,qBAAqB;IACrB,UAAU;IAEV,WAAW;IACX,iBAAiB;IACjB,sBAAsB;IACtB,qBAAqB,EAAE;IAEvB,UAAU;IACV,WAAW;IACX,gBAAgB;IAEhB,UAAU;IACV,cAAc;IACd,wBAAwB;AAC1B;AAGO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,GAAG,YAAY;QAEf,YAAY;QACZ,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM,GAAG,OAAO;YAEtB,eAAe;YACf,IAAI,UAAU,YAAY;gBACxB,IAAI;oBACF,eAAe;oBACf,kBAAkB;oBAClB,YAAY;gBACd;YACF,OAAO,IAAI,UAAU,YAAY;gBAC/B,IAAI;oBACF,eAAe;oBACf,kBAAkB;oBAClB,YAAY;gBACd;YACF;QACF;QAEA,gBAAgB;YACd,IAAI,CAAC,QAAU,CAAC;oBAAE,YAAY,CAAC,MAAM,UAAU;gBAAC,CAAC,GAAG,OAAO;QAC7D;QAEA,iBAAiB;YACf,IAAI,CAAC,QAAU,CAAC;oBAAE,eAAe,CAAC,MAAM,aAAa;gBAAC,CAAC,GAAG,OAAO;QACnE;QAEA,kBAAkB;YAChB,IAAI,CAAC,QAAU,CAAC;oBAAE,kBAAkB,CAAC,MAAM,gBAAgB;gBAAC,CAAC,GAAG,OAAO;QACzE;QAEA,YAAY;QACZ,gBAAgB,CAAC;YACf,IAAI;gBAAE,aAAa;YAAK,GAAG,OAAO;YAElC,SAAS;YACT,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,kBAAkB,AAAC,eAAmB,OAAL;QACnC;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS,GAAG,OAAO;YAEzB,eAAe;YACf,IAAI,aAAa,cAAc;gBAC7B,MAAM,QAAQ,CAAC;YACjB,OAAO,IAAI,aAAa,kBAAkB;gBACxC,MAAM,QAAQ,CAAC;YACjB,OAAO;gBACL,MAAM,QAAQ,CAAC;YACjB;QACF;QAEA,uBAAuB,CAAC;YACtB,IAAI;gBACF,oBAAoB;gBACpB,qBAAqB,eAAe,mBAAmB;YACzD,GAAG,OAAO;QACZ;QAEA,cAAc;QACd,mBAAmB,CAAC;YAClB,IAAI,CAAC,QAAU,CAAC;oBACd,iBAAiB;wBACf,GAAG,MAAM,eAAe;wBACxB,cAAc;+BAAI,MAAM,eAAe,CAAC,YAAY;4BAAE;yBAAY;oBACpE;gBACF,CAAC,GAAG,OAAO;QACb;QAEA,iBAAiB,CAAC,MAAM;YACtB,IAAI,CAAC,QAAU,CAAC;oBACd,iBAAiB;wBACf,GAAG,MAAM,eAAe;wBACxB,aAAa;wBACb,WAAW,MAAM,eAAe,CAAC,SAAS,GAAG;oBAC/C;gBACF,CAAC,GAAG,OAAO;QACb;QAEA,iBAAiB,CAAC;YAChB,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc;wBAAE,GAAG,MAAM,YAAY;wBAAE,GAAG,QAAQ;oBAAC;gBACrD,CAAC,GAAG,OAAO;QACb;QAEA,cAAc;QACd,YAAY;YACV,IAAI;gBAAE,gBAAgB;gBAAM,aAAa;YAAQ,GAAG,OAAO;QAC7D;QAEA,WAAW;YACT,IAAI;gBAAE,gBAAgB;YAAM,GAAG,OAAO;QACxC;QAEA,cAAc,CAAC;YACb,IAAI;gBACF,qBAAqB;gBACrB,aAAa;YACf,GAAG,OAAO;QACZ;QAEA,gBAAgB;YACd,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU,CAAC,MAAM,QAAQ;gBAAC,CAAC,GAAG,OAAO;QACzD;QAEA,YAAY;QACZ,oBAAoB,CAAC;YACnB,IAAI;gBAAE,iBAAiB;YAAK,GAAG,OAAO;QACxC;QAEA,wBAAwB;YACtB,IAAI,CAAC,QAAU,CAAC;oBACd,sBAAsB,CAAC,MAAM,oBAAoB;gBACnD,CAAC,GAAG,OAAO;QACb;QAEA,wBAAwB,CAAC;YACvB,IAAI;gBAAE,qBAAqB;YAAQ,GAAG,OAAO;QAC/C;QAEA,YAAY;QACZ,YAAY,SAAC;gBAAS,2EAAU;YAC9B,IAAI;gBAAE,WAAW;gBAAS,gBAAgB;YAAQ,GAAG,OAAO;QAC9D;QAEA,YAAY;QACZ,aAAa;YACX,IAAI,CAAC,QAAU,CAAC;oBAAE,cAAc,CAAC,MAAM,YAAY;gBAAC,CAAC,GAAG,OAAO;QACjE;QAEA,uBAAuB;YACrB,IAAI,CAAC,QAAU,CAAC;oBACd,wBAAwB,CAAC,MAAM,sBAAsB;gBACvD,CAAC,GAAG,OAAO;QACb;QAEA,YAAY;QACZ,eAAe;YACb,IAAI;gBACF,iBAAiB,aAAa,eAAe;gBAC7C,cAAc,CAAC;gBACf,oBAAoB;gBACpB,qBAAqB,EAAE;YACzB,GAAG,OAAO;QACZ;QAEA,iBAAiB;YACf,IAAI,cAAc,OAAO;QAC3B;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,oBAAoB;YACpB,OAAO,MAAM,KAAK;YAClB,YAAY,MAAM,UAAU;YAC5B,eAAe,MAAM,aAAa;YAClC,kBAAkB,MAAM,gBAAgB;YACxC,iBAAiB,MAAM,eAAe;YACtC,cAAc,MAAM,YAAY;YAChC,wBAAwB,MAAM,sBAAsB;YACpD,iBAAiB,MAAM,eAAe;YACtC,cAAc,MAAM,YAAY;QAClC,CAAC;AACH,IAEF;IACE,MAAM;AACR;AAKG,MAAM,cAAc,CAAC,QAAoB,MAAM,KAAK;AACpD,MAAM,iBAAiB,CAAC,QAAoB,MAAM,QAAQ;AAC1D,MAAM,oBAAoB,CAAC,QAAoB,MAAM,WAAW;AAChE,MAAM,kBAAkB,CAAC,QAAoB,MAAM,SAAS;AAC5D,MAAM,wBAAwB,CAAC,QAAoB,MAAM,eAAe;AACxE,MAAM,4BAA4B,CAAC,QAAoB,MAAM,mBAAmB", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/lib/path-detector.ts"], "sourcesContent": ["/**\n * 🧠 智能路径检测系统\n * 基于用户行为分析，智能判断用户类型并分发到对应路径\n */\n\nexport interface UserBehavior {\n  // 页面浏览行为\n  viewsCode: boolean;\n  interactsWithDemo: boolean;\n  timeOnTechPages: number;\n  timeOnBusinessPages: number;\n  \n  // 交互行为\n  clicksOnGitHub: boolean;\n  downloadsWhitepaper: boolean;\n  requestsDemo: boolean;\n  contactsForBusiness: boolean;\n  \n  // 技术探索行为\n  usesHackerChallenge: boolean;\n  viewsThreatRadar: boolean;\n  checksSourceCode: boolean;\n  \n  // 商务咨询行为\n  viewsPricing: boolean;\n  requestsQuote: boolean;\n  schedulesCall: boolean;\n  \n  // 设备和环境\n  isMobile: boolean;\n  userAgent: string;\n  referrer: string;\n}\n\nexport type UserType = 'tech-track' | 'business-track' | 'mixed-track';\n\nexport interface PathRecommendation {\n  userType: UserType;\n  confidence: number;\n  recommendedRoute: string;\n  personalizedContent: string[];\n  nextActions: string[];\n}\n\n/**\n * 🎯 用户类型检测算法\n */\nexport class PathDetector {\n  private behaviorHistory: UserBehavior[] = [];\n  private currentBehavior: Partial<UserBehavior> = {};\n  \n  /**\n   * 记录用户行为\n   */\n  recordBehavior(behavior: Partial<UserBehavior>) {\n    this.currentBehavior = { ...this.currentBehavior, ...behavior };\n    \n    // 每30秒保存一次行为快照\n    if (this.shouldSaveBehavior()) {\n      this.saveBehaviorSnapshot();\n    }\n  }\n  \n  /**\n   * 智能检测用户类型\n   */\n  detectUserType(): PathRecommendation {\n    const behavior = this.getCurrentBehavior();\n    let techScore = 0;\n    let businessScore = 0;\n    \n    // 🔧 技术指标评分\n    if (behavior.viewsCode) techScore += 30;\n    if (behavior.interactsWithDemo) techScore += 25;\n    if (behavior.timeOnTechPages > 60) techScore += 20;\n    if (behavior.clicksOnGitHub) techScore += 15;\n    if (behavior.downloadsWhitepaper) techScore += 10;\n    if (behavior.usesHackerChallenge) techScore += 35;\n    if (behavior.viewsThreatRadar) techScore += 20;\n    if (behavior.checksSourceCode) techScore += 25;\n    \n    // 💼 商务指标评分\n    if (behavior.contactsForBusiness) businessScore += 40;\n    if (behavior.requestsDemo) businessScore += 30;\n    if (behavior.viewsPricing) businessScore += 25;\n    if (behavior.requestsQuote) businessScore += 35;\n    if (behavior.schedulesCall) businessScore += 40;\n    if (behavior.timeOnBusinessPages > 60) businessScore += 20;\n    \n    // 📱 设备和环境调整\n    if (behavior.isMobile) {\n      businessScore += 10; // 移动端用户更可能是商务决策者\n    }\n    \n    // 🌐 来源渠道调整\n    if (behavior.referrer?.includes('linkedin')) businessScore += 15;\n    if (behavior.referrer?.includes('github')) techScore += 20;\n    if (behavior.referrer?.includes('google')) {\n      // 搜索关键词分析（如果可获取）\n      techScore += 5;\n      businessScore += 5;\n    }\n    \n    // 🎯 决策逻辑\n    const totalScore = techScore + businessScore;\n    const techRatio = totalScore > 0 ? techScore / totalScore : 0.5;\n    const businessRatio = totalScore > 0 ? businessScore / totalScore : 0.5;\n    \n    let userType: UserType;\n    let confidence: number;\n    let recommendedRoute: string;\n    \n    if (techRatio > 0.7) {\n      userType = 'tech-track';\n      confidence = techRatio;\n      recommendedRoute = '/tech-showcase';\n    } else if (businessRatio > 0.7) {\n      userType = 'business-track';\n      confidence = businessRatio;\n      recommendedRoute = '/business-showcase';\n    } else {\n      userType = 'mixed-track';\n      confidence = Math.max(techRatio, businessRatio);\n      recommendedRoute = '/adaptive-showcase';\n    }\n    \n    return {\n      userType,\n      confidence,\n      recommendedRoute,\n      personalizedContent: this.generatePersonalizedContent(userType, behavior),\n      nextActions: this.generateNextActions(userType, behavior)\n    };\n  }\n  \n  /**\n   * 生成个性化内容推荐\n   */\n  private generatePersonalizedContent(userType: UserType, behavior: UserBehavior): string[] {\n    const content: string[] = [];\n    \n    switch (userType) {\n      case 'tech-track':\n        content.push('深度技术架构解析');\n        content.push('开源代码和API文档');\n        content.push('实战攻防演示');\n        if (behavior.usesHackerChallenge) {\n          content.push('高级渗透测试挑战');\n        }\n        if (behavior.viewsThreatRadar) {\n          content.push('威胁情报深度分析');\n        }\n        break;\n        \n      case 'business-track':\n        content.push('ROI计算和成本分析');\n        content.push('客户成功案例');\n        content.push('合规性和认证信息');\n        if (behavior.requestsDemo) {\n          content.push('定制化演示方案');\n        }\n        if (behavior.viewsPricing) {\n          content.push('企业级服务包装');\n        }\n        break;\n        \n      case 'mixed-track':\n        content.push('技术优势与商业价值结合');\n        content.push('分层次的产品介绍');\n        content.push('灵活的展示路径选择');\n        break;\n    }\n    \n    return content;\n  }\n  \n  /**\n   * 生成下一步行动建议\n   */\n  private generateNextActions(userType: UserType, behavior: UserBehavior): string[] {\n    const actions: string[] = [];\n    \n    switch (userType) {\n      case 'tech-track':\n        actions.push('查看技术白皮书');\n        actions.push('尝试在线演示');\n        actions.push('访问GitHub仓库');\n        if (!behavior.usesHackerChallenge) {\n          actions.push('参与黑客挑战');\n        }\n        break;\n        \n      case 'business-track':\n        actions.push('申请产品演示');\n        actions.push('下载解决方案手册');\n        actions.push('预约专家咨询');\n        if (!behavior.requestsQuote) {\n          actions.push('获取报价方案');\n        }\n        break;\n        \n      case 'mixed-track':\n        actions.push('选择感兴趣的展示路径');\n        actions.push('探索产品功能特性');\n        actions.push('了解技术和商业优势');\n        break;\n    }\n    \n    return actions;\n  }\n  \n  /**\n   * 获取当前行为数据\n   */\n  private getCurrentBehavior(): UserBehavior {\n    const defaultBehavior: UserBehavior = {\n      viewsCode: false,\n      interactsWithDemo: false,\n      timeOnTechPages: 0,\n      timeOnBusinessPages: 0,\n      clicksOnGitHub: false,\n      downloadsWhitepaper: false,\n      requestsDemo: false,\n      contactsForBusiness: false,\n      usesHackerChallenge: false,\n      viewsThreatRadar: false,\n      checksSourceCode: false,\n      viewsPricing: false,\n      requestsQuote: false,\n      schedulesCall: false,\n      isMobile: false,\n      userAgent: '',\n      referrer: ''\n    };\n    \n    return { ...defaultBehavior, ...this.currentBehavior };\n  }\n  \n  /**\n   * 判断是否应该保存行为快照\n   */\n  private shouldSaveBehavior(): boolean {\n    const lastSnapshot = this.behaviorHistory[this.behaviorHistory.length - 1];\n    if (!lastSnapshot) return true;\n    \n    // 简单的时间间隔判断（实际应用中可以更复杂）\n    return Date.now() - (lastSnapshot as any).timestamp > 30000;\n  }\n  \n  /**\n   * 保存行为快照\n   */\n  private saveBehaviorSnapshot() {\n    const snapshot = {\n      ...this.getCurrentBehavior(),\n      timestamp: Date.now()\n    };\n    \n    this.behaviorHistory.push(snapshot as UserBehavior);\n    \n    // 保持历史记录在合理范围内\n    if (this.behaviorHistory.length > 10) {\n      this.behaviorHistory.shift();\n    }\n  }\n  \n  /**\n   * 获取行为历史分析\n   */\n  getBehaviorAnalytics() {\n    return {\n      totalSessions: this.behaviorHistory.length,\n      averageSessionTime: this.calculateAverageSessionTime(),\n      preferredContent: this.analyzeContentPreferences(),\n      conversionFunnel: this.analyzeConversionFunnel()\n    };\n  }\n  \n  private calculateAverageSessionTime(): number {\n    if (this.behaviorHistory.length === 0) return 0;\n    \n    const totalTime = this.behaviorHistory.reduce((sum, behavior) => {\n      return sum + behavior.timeOnTechPages + behavior.timeOnBusinessPages;\n    }, 0);\n    \n    return totalTime / this.behaviorHistory.length;\n  }\n  \n  private analyzeContentPreferences(): Record<string, number> {\n    const preferences: Record<string, number> = {};\n    \n    this.behaviorHistory.forEach(behavior => {\n      if (behavior.viewsCode) preferences['code'] = (preferences['code'] || 0) + 1;\n      if (behavior.interactsWithDemo) preferences['demo'] = (preferences['demo'] || 0) + 1;\n      if (behavior.usesHackerChallenge) preferences['challenge'] = (preferences['challenge'] || 0) + 1;\n      if (behavior.viewsThreatRadar) preferences['threat'] = (preferences['threat'] || 0) + 1;\n    });\n    \n    return preferences;\n  }\n  \n  private analyzeConversionFunnel(): Record<string, number> {\n    const funnel: Record<string, number> = {\n      visited: this.behaviorHistory.length,\n      engaged: 0,\n      interested: 0,\n      converted: 0\n    };\n    \n    this.behaviorHistory.forEach(behavior => {\n      if (behavior.timeOnTechPages > 30 || behavior.timeOnBusinessPages > 30) {\n        funnel.engaged++;\n      }\n      if (behavior.requestsDemo || behavior.downloadsWhitepaper) {\n        funnel.interested++;\n      }\n      if (behavior.contactsForBusiness || behavior.requestsQuote) {\n        funnel.converted++;\n      }\n    });\n    \n    return funnel;\n  }\n}\n\n// 🌟 全局路径检测器实例\nexport const globalPathDetector = new PathDetector();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AA4CM,MAAM;IAIX;;GAEC,GACD,eAAe,QAA+B,EAAE;QAC9C,IAAI,CAAC,eAAe,GAAG;YAAE,GAAG,IAAI,CAAC,eAAe;YAAE,GAAG,QAAQ;QAAC;QAE9D,eAAe;QACf,IAAI,IAAI,CAAC,kBAAkB,IAAI;YAC7B,IAAI,CAAC,oBAAoB;QAC3B;IACF;IAEA;;GAEC,GACD,iBAAqC;YA6B/B,oBACA,qBACA;QA9BJ,MAAM,WAAW,IAAI,CAAC,kBAAkB;QACxC,IAAI,YAAY;QAChB,IAAI,gBAAgB;QAEpB,YAAY;QACZ,IAAI,SAAS,SAAS,EAAE,aAAa;QACrC,IAAI,SAAS,iBAAiB,EAAE,aAAa;QAC7C,IAAI,SAAS,eAAe,GAAG,IAAI,aAAa;QAChD,IAAI,SAAS,cAAc,EAAE,aAAa;QAC1C,IAAI,SAAS,mBAAmB,EAAE,aAAa;QAC/C,IAAI,SAAS,mBAAmB,EAAE,aAAa;QAC/C,IAAI,SAAS,gBAAgB,EAAE,aAAa;QAC5C,IAAI,SAAS,gBAAgB,EAAE,aAAa;QAE5C,YAAY;QACZ,IAAI,SAAS,mBAAmB,EAAE,iBAAiB;QACnD,IAAI,SAAS,YAAY,EAAE,iBAAiB;QAC5C,IAAI,SAAS,YAAY,EAAE,iBAAiB;QAC5C,IAAI,SAAS,aAAa,EAAE,iBAAiB;QAC7C,IAAI,SAAS,aAAa,EAAE,iBAAiB;QAC7C,IAAI,SAAS,mBAAmB,GAAG,IAAI,iBAAiB;QAExD,aAAa;QACb,IAAI,SAAS,QAAQ,EAAE;YACrB,iBAAiB,IAAI,iBAAiB;QACxC;QAEA,YAAY;QACZ,KAAI,qBAAA,SAAS,QAAQ,cAAjB,yCAAA,mBAAmB,QAAQ,CAAC,aAAa,iBAAiB;QAC9D,KAAI,sBAAA,SAAS,QAAQ,cAAjB,0CAAA,oBAAmB,QAAQ,CAAC,WAAW,aAAa;QACxD,KAAI,sBAAA,SAAS,QAAQ,cAAjB,0CAAA,oBAAmB,QAAQ,CAAC,WAAW;YACzC,iBAAiB;YACjB,aAAa;YACb,iBAAiB;QACnB;QAEA,UAAU;QACV,MAAM,aAAa,YAAY;QAC/B,MAAM,YAAY,aAAa,IAAI,YAAY,aAAa;QAC5D,MAAM,gBAAgB,aAAa,IAAI,gBAAgB,aAAa;QAEpE,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,IAAI,YAAY,KAAK;YACnB,WAAW;YACX,aAAa;YACb,mBAAmB;QACrB,OAAO,IAAI,gBAAgB,KAAK;YAC9B,WAAW;YACX,aAAa;YACb,mBAAmB;QACrB,OAAO;YACL,WAAW;YACX,aAAa,KAAK,GAAG,CAAC,WAAW;YACjC,mBAAmB;QACrB;QAEA,OAAO;YACL;YACA;YACA;YACA,qBAAqB,IAAI,CAAC,2BAA2B,CAAC,UAAU;YAChE,aAAa,IAAI,CAAC,mBAAmB,CAAC,UAAU;QAClD;IACF;IAEA;;GAEC,GACD,AAAQ,4BAA4B,QAAkB,EAAE,QAAsB,EAAY;QACxF,MAAM,UAAoB,EAAE;QAE5B,OAAQ;YACN,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,IAAI,SAAS,mBAAmB,EAAE;oBAChC,QAAQ,IAAI,CAAC;gBACf;gBACA,IAAI,SAAS,gBAAgB,EAAE;oBAC7B,QAAQ,IAAI,CAAC;gBACf;gBACA;YAEF,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,IAAI,SAAS,YAAY,EAAE;oBACzB,QAAQ,IAAI,CAAC;gBACf;gBACA,IAAI,SAAS,YAAY,EAAE;oBACzB,QAAQ,IAAI,CAAC;gBACf;gBACA;YAEF,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb;QACJ;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,oBAAoB,QAAkB,EAAE,QAAsB,EAAY;QAChF,MAAM,UAAoB,EAAE;QAE5B,OAAQ;YACN,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,IAAI,CAAC,SAAS,mBAAmB,EAAE;oBACjC,QAAQ,IAAI,CAAC;gBACf;gBACA;YAEF,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,IAAI,CAAC,SAAS,aAAa,EAAE;oBAC3B,QAAQ,IAAI,CAAC;gBACf;gBACA;YAEF,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb;QACJ;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,qBAAmC;QACzC,MAAM,kBAAgC;YACpC,WAAW;YACX,mBAAmB;YACnB,iBAAiB;YACjB,qBAAqB;YACrB,gBAAgB;YAChB,qBAAqB;YACrB,cAAc;YACd,qBAAqB;YACrB,qBAAqB;YACrB,kBAAkB;YAClB,kBAAkB;YAClB,cAAc;YACd,eAAe;YACf,eAAe;YACf,UAAU;YACV,WAAW;YACX,UAAU;QACZ;QAEA,OAAO;YAAE,GAAG,eAAe;YAAE,GAAG,IAAI,CAAC,eAAe;QAAC;IACvD;IAEA;;GAEC,GACD,AAAQ,qBAA8B;QACpC,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,EAAE;QAC1E,IAAI,CAAC,cAAc,OAAO;QAE1B,wBAAwB;QACxB,OAAO,KAAK,GAAG,KAAK,AAAC,aAAqB,SAAS,GAAG;IACxD;IAEA;;GAEC,GACD,AAAQ,uBAAuB;QAC7B,MAAM,WAAW;YACf,GAAG,IAAI,CAAC,kBAAkB,EAAE;YAC5B,WAAW,KAAK,GAAG;QACrB;QAEA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;QAE1B,eAAe;QACf,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI;YACpC,IAAI,CAAC,eAAe,CAAC,KAAK;QAC5B;IACF;IAEA;;GAEC,GACD,uBAAuB;QACrB,OAAO;YACL,eAAe,IAAI,CAAC,eAAe,CAAC,MAAM;YAC1C,oBAAoB,IAAI,CAAC,2BAA2B;YACpD,kBAAkB,IAAI,CAAC,yBAAyB;YAChD,kBAAkB,IAAI,CAAC,uBAAuB;QAChD;IACF;IAEQ,8BAAsC;QAC5C,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,GAAG,OAAO;QAE9C,MAAM,YAAY,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,KAAK;YAClD,OAAO,MAAM,SAAS,eAAe,GAAG,SAAS,mBAAmB;QACtE,GAAG;QAEH,OAAO,YAAY,IAAI,CAAC,eAAe,CAAC,MAAM;IAChD;IAEQ,4BAAoD;QAC1D,MAAM,cAAsC,CAAC;QAE7C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;YAC3B,IAAI,SAAS,SAAS,EAAE,WAAW,CAAC,OAAO,GAAG,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,IAAI;YAC3E,IAAI,SAAS,iBAAiB,EAAE,WAAW,CAAC,OAAO,GAAG,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,IAAI;YACnF,IAAI,SAAS,mBAAmB,EAAE,WAAW,CAAC,YAAY,GAAG,CAAC,WAAW,CAAC,YAAY,IAAI,CAAC,IAAI;YAC/F,IAAI,SAAS,gBAAgB,EAAE,WAAW,CAAC,SAAS,GAAG,CAAC,WAAW,CAAC,SAAS,IAAI,CAAC,IAAI;QACxF;QAEA,OAAO;IACT;IAEQ,0BAAkD;QACxD,MAAM,SAAiC;YACrC,SAAS,IAAI,CAAC,eAAe,CAAC,MAAM;YACpC,SAAS;YACT,YAAY;YACZ,WAAW;QACb;QAEA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;YAC3B,IAAI,SAAS,eAAe,GAAG,MAAM,SAAS,mBAAmB,GAAG,IAAI;gBACtE,OAAO,OAAO;YAChB;YACA,IAAI,SAAS,YAAY,IAAI,SAAS,mBAAmB,EAAE;gBACzD,OAAO,UAAU;YACnB;YACA,IAAI,SAAS,mBAAmB,IAAI,SAAS,aAAa,EAAE;gBAC1D,OAAO,SAAS;YAClB;QACF;QAEA,OAAO;IACT;;QAlRA,+KAAQ,mBAAkC,EAAE;QAC5C,+KAAQ,mBAAyC,CAAC;;AAkRpD;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/components/effects/ParticleBackground.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useEffect, useMemo } from 'react';\nimport { Canvas, useFrame, useThree } from '@react-three/fiber';\nimport { Points, PointMaterial } from '@react-three/drei';\nimport * as THREE from 'three';\n\n/**\n * 🌟 3D粒子系统组件\n */\nconst ParticleSystem: React.FC = () => {\n  const ref = useRef<THREE.Points>(null);\n  const { viewport } = useThree();\n\n  // 🎯 生成粒子位置\n  const particlePositions = useMemo(() => {\n    const positions = new Float32Array(2000 * 3); // 2000个粒子，每个3个坐标\n    \n    for (let i = 0; i < 2000; i++) {\n      const i3 = i * 3;\n      // 在更大的空间中随机分布粒子\n      positions[i3] = (Math.random() - 0.5) * 20; // x\n      positions[i3 + 1] = (Math.random() - 0.5) * 20; // y  \n      positions[i3 + 2] = (Math.random() - 0.5) * 20; // z\n    }\n    \n    return positions;\n  }, []);\n\n  // ⚡ 动画循环\n  useFrame((state) => {\n    if (ref.current) {\n      // 缓慢旋转整个粒子系统\n      ref.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.1;\n      ref.current.rotation.y = state.clock.elapsedTime * 0.05;\n      \n      // 粒子呼吸效果\n      const scale = 1 + Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n      ref.current.scale.setScalar(scale);\n    }\n  });\n\n  return (\n    <Points ref={ref} positions={particlePositions} stride={3} frustumCulled={false}>\n      <PointMaterial\n        transparent\n        color=\"#00ff41\"\n        size={0.02}\n        sizeAttenuation={true}\n        depthWrite={false}\n        blending={THREE.AdditiveBlending}\n      />\n    </Points>\n  );\n};\n\n/**\n * 🌌 连接线系统组件\n */\nconst ConnectionLines: React.FC = () => {\n  const ref = useRef<THREE.LineSegments>(null);\n  \n  const { positions, colors } = useMemo(() => {\n    const particleCount = 100;\n    const maxConnections = 200;\n    const maxDistance = 3;\n    \n    // 生成粒子位置\n    const particles: THREE.Vector3[] = [];\n    for (let i = 0; i < particleCount; i++) {\n      particles.push(new THREE.Vector3(\n        (Math.random() - 0.5) * 15,\n        (Math.random() - 0.5) * 15,\n        (Math.random() - 0.5) * 15\n      ));\n    }\n    \n    const linePositions: number[] = [];\n    const lineColors: number[] = [];\n    \n    // 计算连接\n    let connectionCount = 0;\n    for (let i = 0; i < particleCount && connectionCount < maxConnections; i++) {\n      for (let j = i + 1; j < particleCount && connectionCount < maxConnections; j++) {\n        const distance = particles[i].distanceTo(particles[j]);\n        \n        if (distance < maxDistance) {\n          // 添加线段\n          linePositions.push(\n            particles[i].x, particles[i].y, particles[i].z,\n            particles[j].x, particles[j].y, particles[j].z\n          );\n          \n          // 根据距离计算透明度\n          const alpha = 1 - (distance / maxDistance);\n          lineColors.push(0, 1, 0.25, alpha); // 绿色\n          lineColors.push(0, 1, 0.25, alpha);\n          \n          connectionCount++;\n        }\n      }\n    }\n    \n    return {\n      positions: new Float32Array(linePositions),\n      colors: new Float32Array(lineColors)\n    };\n  }, []);\n\n  useFrame((state) => {\n    if (ref.current) {\n      // 连接线的脉动效果\n      const material = ref.current.material as THREE.LineBasicMaterial;\n      if (material) {\n        material.opacity = 0.3 + Math.sin(state.clock.elapsedTime * 2) * 0.2;\n      }\n    }\n  });\n\n  return (\n    <lineSegments ref={ref}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          array={positions}\n          count={positions.length / 3}\n          itemSize={3}\n        />\n        <bufferAttribute\n          attach=\"attributes-color\"\n          array={colors}\n          count={colors.length / 4}\n          itemSize={4}\n        />\n      </bufferGeometry>\n      <lineBasicMaterial\n        transparent\n        vertexColors\n        blending={THREE.AdditiveBlending}\n        opacity={0.3}\n      />\n    </lineSegments>\n  );\n};\n\n/**\n * 🔮 主粒子背景组件\n */\nconst ParticleBackground: React.FC = () => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  // 🎨 Canvas样式\n  const canvasStyle: React.CSSProperties = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    zIndex: -1,\n    pointerEvents: 'none'\n  };\n\n  // 📱 响应式相机设置\n  const cameraProps = {\n    position: [0, 0, 10] as [number, number, number],\n    fov: 60,\n    near: 0.1,\n    far: 100\n  };\n\n  return (\n    <div className=\"particle-bg\">\n      <Canvas\n        ref={canvasRef}\n        style={canvasStyle}\n        camera={cameraProps}\n        gl={{\n          alpha: true,\n          antialias: true,\n          powerPreference: \"high-performance\"\n        }}\n        dpr={[1, 2]} // 设备像素比\n      >\n        {/* 🌟 环境光 */}\n        <ambientLight intensity={0.1} />\n        \n        {/* 🎯 主粒子系统 */}\n        <ParticleSystem />\n        \n        {/* 🌐 连接线系统 */}\n        <ConnectionLines />\n        \n        {/* 🔮 额外的装饰粒子 */}\n        <Points\n          positions={useMemo(() => {\n            const positions = new Float32Array(500 * 3);\n            for (let i = 0; i < 500; i++) {\n              const i3 = i * 3;\n              positions[i3] = (Math.random() - 0.5) * 30;\n              positions[i3 + 1] = (Math.random() - 0.5) * 30;\n              positions[i3 + 2] = (Math.random() - 0.5) * 30;\n            }\n            return positions;\n          }, [])}\n          stride={3}\n        >\n          <PointMaterial\n            transparent\n            color=\"#ffd700\"\n            size={0.01}\n            sizeAttenuation={true}\n            depthWrite={false}\n            blending={THREE.AdditiveBlending}\n            opacity={0.6}\n          />\n        </Points>\n      </Canvas>\n    </div>\n  );\n};\n\nexport default ParticleBackground;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AACA;;;AALA;;;;;AAOA;;CAEC,GACD,MAAM,iBAA2B;;IAC/B,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAgB;IACjC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;IAE5B,YAAY;IACZ,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE;YAChC,MAAM,YAAY,IAAI,aAAa,OAAO,IAAI,iBAAiB;YAE/D,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gBAC7B,MAAM,KAAK,IAAI;gBACf,gBAAgB;gBAChB,SAAS,CAAC,GAAG,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI,IAAI;gBAChD,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI,MAAM;gBACtD,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI,IAAI;YACtD;YAEA,OAAO;QACT;oDAAG,EAAE;IAEL,SAAS;IACT,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;mCAAE,CAAC;YACR,IAAI,IAAI,OAAO,EAAE;gBACf,aAAa;gBACb,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;gBACnE,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;gBAEnD,SAAS;gBACT,MAAM,QAAQ,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;gBAC5D,IAAI,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;YAC9B;QACF;;IAEA,qBACE,6LAAC,6JAAA,CAAA,SAAM;QAAC,KAAK;QAAK,WAAW;QAAmB,QAAQ;QAAG,eAAe;kBACxE,cAAA,6LAAC,oKAAA,CAAA,gBAAa;YACZ,WAAW;YACX,OAAM;YACN,MAAM;YACN,iBAAiB;YACjB,YAAY;YACZ,UAAU,kJAAA,CAAA,mBAAsB;;;;;;;;;;;AAIxC;GA5CM;;QAEiB,kNAAA,CAAA,WAAQ;QAkB7B,kNAAA,CAAA,WAAQ;;;KApBJ;AA8CN;;CAEC,GACD,MAAM,kBAA4B;;IAChC,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAsB;IAEvC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mCAAE;YACpC,MAAM,gBAAgB;YACtB,MAAM,iBAAiB;YACvB,MAAM,cAAc;YAEpB,SAAS;YACT,MAAM,YAA6B,EAAE;YACrC,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACtC,UAAU,IAAI,CAAC,IAAI,kJAAA,CAAA,UAAa,CAC9B,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAE5B;YAEA,MAAM,gBAA0B,EAAE;YAClC,MAAM,aAAuB,EAAE;YAE/B,OAAO;YACP,IAAI,kBAAkB;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,kBAAkB,gBAAgB,IAAK;gBAC1E,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,iBAAiB,kBAAkB,gBAAgB,IAAK;oBAC9E,MAAM,WAAW,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;oBAErD,IAAI,WAAW,aAAa;wBAC1B,OAAO;wBACP,cAAc,IAAI,CAChB,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,EAC9C,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;wBAGhD,YAAY;wBACZ,MAAM,QAAQ,IAAK,WAAW;wBAC9B,WAAW,IAAI,CAAC,GAAG,GAAG,MAAM,QAAQ,KAAK;wBACzC,WAAW,IAAI,CAAC,GAAG,GAAG,MAAM;wBAE5B;oBACF;gBACF;YACF;YAEA,OAAO;gBACL,WAAW,IAAI,aAAa;gBAC5B,QAAQ,IAAI,aAAa;YAC3B;QACF;kCAAG,EAAE;IAEL,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;oCAAE,CAAC;YACR,IAAI,IAAI,OAAO,EAAE;gBACf,WAAW;gBACX,MAAM,WAAW,IAAI,OAAO,CAAC,QAAQ;gBACrC,IAAI,UAAU;oBACZ,SAAS,OAAO,GAAG,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;gBACnE;YACF;QACF;;IAEA,qBACE,6LAAC;QAAa,KAAK;;0BACjB,6LAAC;;kCACC,6LAAC;wBACC,QAAO;wBACP,OAAO;wBACP,OAAO,UAAU,MAAM,GAAG;wBAC1B,UAAU;;;;;;kCAEZ,6LAAC;wBACC,QAAO;wBACP,OAAO;wBACP,OAAO,OAAO,MAAM,GAAG;wBACvB,UAAU;;;;;;;;;;;;0BAGd,6LAAC;gBACC,WAAW;gBACX,YAAY;gBACZ,UAAU,kJAAA,CAAA,mBAAsB;gBAChC,SAAS;;;;;;;;;;;;AAIjB;IApFM;;QAkDJ,kNAAA,CAAA,WAAQ;;;MAlDJ;AAsFN;;CAEC,GACD,MAAM,qBAA+B;;IACnC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,cAAc;IACd,MAAM,cAAmC;QACvC,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ,CAAC;QACT,eAAe;IACjB;IAEA,aAAa;IACb,MAAM,cAAc;QAClB,UAAU;YAAC;YAAG;YAAG;SAAG;QACpB,KAAK;QACL,MAAM;QACN,KAAK;IACP;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sMAAA,CAAA,SAAM;YACL,KAAK;YACL,OAAO;YACP,QAAQ;YACR,IAAI;gBACF,OAAO;gBACP,WAAW;gBACX,iBAAiB;YACnB;YACA,KAAK;gBAAC;gBAAG;aAAE;;8BAGX,6LAAC;oBAAa,WAAW;;;;;;8BAGzB,6LAAC;;;;;8BAGD,6LAAC;;;;;8BAGD,6LAAC,6JAAA,CAAA,SAAM;oBACL,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sDAAE;4BACjB,MAAM,YAAY,IAAI,aAAa,MAAM;4BACzC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;gCAC5B,MAAM,KAAK,IAAI;gCACf,SAAS,CAAC,GAAG,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCACxC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCAC5C,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC9C;4BACA,OAAO;wBACT;qDAAG,EAAE;oBACL,QAAQ;8BAER,cAAA,6LAAC,oKAAA,CAAA,gBAAa;wBACZ,WAAW;wBACX,OAAM;wBACN,MAAM;wBACN,iBAAiB;wBACjB,YAAY;wBACZ,UAAU,kJAAA,CAAA,mBAAsB;wBAChC,SAAS;;;;;;;;;;;;;;;;;;;;;;AAMrB;IAvEM;MAAA;uCAyES", "debugId": null}}, {"offset": {"line": 816, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/components/effects/MatrixRain.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface MatrixColumn {\n  x: number;\n  y: number;\n  speed: number;\n  chars: string[];\n  opacity: number;\n}\n\n/**\n * 🌧️ Matrix代码雨效果组件\n */\nconst MatrixRain: React.FC = () => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const animationRef = useRef<number>();\n  const columnsRef = useRef<MatrixColumn[]>([]);\n  const [isVisible, setIsVisible] = useState(true);\n\n  // 🔤 Matrix字符集\n  const matrixChars = [\n    // 数字\n    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n    // 英文字母\n    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',\n    'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',\n    // 特殊符号\n    '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '-', '+', '=',\n    '[', ']', '{', '}', '|', '\\\\', ':', ';', '\"', \"'\", '<', '>', ',', '.', '?', '/',\n    // 日文片假名（Matrix经典）\n    'ア', 'イ', 'ウ', 'エ', 'オ', 'カ', 'キ', 'ク', 'ケ', 'コ',\n    'サ', 'シ', 'ス', 'セ', 'ソ', 'タ', 'チ', 'ツ', 'テ', 'ト',\n    'ナ', 'ニ', 'ヌ', 'ネ', 'ノ', 'ハ', 'ヒ', 'フ', 'ヘ', 'ホ',\n    'マ', 'ミ', 'ム', 'メ', 'モ', 'ヤ', 'ユ', 'ヨ', 'ラ', 'リ',\n    'ル', 'レ', 'ロ', 'ワ', 'ヲ', 'ン'\n  ];\n\n  // 🎨 初始化Canvas和列\n  const initializeMatrix = () => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // 设置Canvas尺寸\n    canvas.width = window.innerWidth;\n    canvas.height = window.innerHeight;\n\n    // 计算列数\n    const fontSize = 14;\n    const columnWidth = fontSize;\n    const columnCount = Math.floor(canvas.width / columnWidth);\n\n    // 初始化列\n    columnsRef.current = [];\n    for (let i = 0; i < columnCount; i++) {\n      columnsRef.current.push({\n        x: i * columnWidth,\n        y: Math.random() * canvas.height,\n        speed: Math.random() * 3 + 1, // 1-4的随机速度\n        chars: [],\n        opacity: Math.random() * 0.5 + 0.5 // 0.5-1的随机透明度\n      });\n    }\n\n    // 为每列生成字符\n    columnsRef.current.forEach(column => {\n      const charCount = Math.floor(Math.random() * 20) + 10; // 10-30个字符\n      for (let j = 0; j < charCount; j++) {\n        column.chars.push(\n          matrixChars[Math.floor(Math.random() * matrixChars.length)]\n        );\n      }\n    });\n  };\n\n  // 🎬 动画循环\n  const animate = () => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // 清除画布（带有轻微的拖尾效果）\n    ctx.fillStyle = 'rgba(10, 10, 35, 0.05)';\n    ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n    // 设置字体\n    ctx.font = '14px JetBrains Mono, monospace';\n    ctx.textAlign = 'center';\n\n    // 绘制每一列\n    columnsRef.current.forEach((column, columnIndex) => {\n      column.chars.forEach((char, charIndex) => {\n        const y = column.y + charIndex * 16;\n        \n        // 如果字符超出屏幕底部，重置列\n        if (y > canvas.height + 100) {\n          if (charIndex === 0) {\n            column.y = -column.chars.length * 16;\n            column.speed = Math.random() * 3 + 1;\n            column.opacity = Math.random() * 0.5 + 0.5;\n            \n            // 随机更换一些字符\n            if (Math.random() < 0.1) {\n              column.chars[Math.floor(Math.random() * column.chars.length)] = \n                matrixChars[Math.floor(Math.random() * matrixChars.length)];\n            }\n          }\n          return;\n        }\n\n        // 计算字符的透明度（头部更亮）\n        const distanceFromHead = charIndex;\n        const maxDistance = column.chars.length;\n        const alpha = Math.max(0, (maxDistance - distanceFromHead) / maxDistance) * column.opacity;\n\n        // 头部字符使用白色，其他使用绿色\n        if (charIndex === 0) {\n          ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;\n        } else if (charIndex < 3) {\n          ctx.fillStyle = `rgba(180, 255, 180, ${alpha})`;\n        } else {\n          ctx.fillStyle = `rgba(0, 255, 65, ${alpha})`;\n        }\n\n        // 绘制字符\n        ctx.fillText(char, column.x + 7, y);\n\n        // 随机闪烁效果\n        if (Math.random() < 0.01) {\n          ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.8})`;\n          ctx.fillText(char, column.x + 7, y);\n        }\n      });\n\n      // 更新列位置\n      column.y += column.speed;\n    });\n\n    // 继续动画\n    if (isVisible) {\n      animationRef.current = requestAnimationFrame(animate);\n    }\n  };\n\n  // 🚀 组件挂载\n  useEffect(() => {\n    initializeMatrix();\n    animate();\n\n    // 窗口大小变化处理\n    const handleResize = () => {\n      initializeMatrix();\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [isVisible]);\n\n  // 🎯 可见性控制\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      setIsVisible(!document.hidden);\n    };\n\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);\n  }, []);\n\n  return (\n    <motion.div\n      className=\"fixed inset-0 pointer-events-none\"\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 0.6 }}\n      exit={{ opacity: 0 }}\n      transition={{ duration: 2 }}\n    >\n      <canvas\n        ref={canvasRef}\n        className=\"w-full h-full\"\n        style={{\n          background: 'transparent',\n          mixBlendMode: 'screen'\n        }}\n      />\n      \n      {/* 🔮 顶部渐变遮罩 */}\n      <div \n        className=\"absolute top-0 left-0 w-full h-32 pointer-events-none\"\n        style={{\n          background: 'linear-gradient(to bottom, rgba(10, 10, 35, 0.8) 0%, transparent 100%)'\n        }}\n      />\n      \n      {/* 🔮 底部渐变遮罩 */}\n      <div \n        className=\"absolute bottom-0 left-0 w-full h-32 pointer-events-none\"\n        style={{\n          background: 'linear-gradient(to top, rgba(10, 10, 35, 0.8) 0%, transparent 100%)'\n        }}\n      />\n    </motion.div>\n  );\n};\n\nexport default MatrixRain;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAaA;;CAEC,GACD,MAAM,aAAuB;;IAC3B,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB,EAAE;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,eAAe;IACf,MAAM,cAAc;QAClB,KAAK;QACL;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC7C,OAAO;QACP;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC5D;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC5D,OAAO;QACP;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC5D;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC5E,kBAAkB;QAClB;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC7C;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC7C;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC7C;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAC7C;QAAK;QAAK;QAAK;QAAK;QAAK;KAC1B;IAED,iBAAiB;IACjB,MAAM,mBAAmB;QACvB,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,aAAa;QACb,OAAO,KAAK,GAAG,OAAO,UAAU;QAChC,OAAO,MAAM,GAAG,OAAO,WAAW;QAElC,OAAO;QACP,MAAM,WAAW;QACjB,MAAM,cAAc;QACpB,MAAM,cAAc,KAAK,KAAK,CAAC,OAAO,KAAK,GAAG;QAE9C,OAAO;QACP,WAAW,OAAO,GAAG,EAAE;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YACpC,WAAW,OAAO,CAAC,IAAI,CAAC;gBACtB,GAAG,IAAI;gBACP,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;gBAChC,OAAO,KAAK,MAAM,KAAK,IAAI;gBAC3B,OAAO,EAAE;gBACT,SAAS,KAAK,MAAM,KAAK,MAAM,IAAI,cAAc;YACnD;QACF;QAEA,UAAU;QACV,WAAW,OAAO,CAAC,OAAO,CAAC,CAAA;YACzB,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI,WAAW;YAClE,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;gBAClC,OAAO,KAAK,CAAC,IAAI,CACf,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;YAE/D;QACF;IACF;IAEA,UAAU;IACV,MAAM,UAAU;QACd,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,kBAAkB;QAClB,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE9C,OAAO;QACP,IAAI,IAAI,GAAG;QACX,IAAI,SAAS,GAAG;QAEhB,QAAQ;QACR,WAAW,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ;YAClC,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM;gBAC1B,MAAM,IAAI,OAAO,CAAC,GAAG,YAAY;gBAEjC,iBAAiB;gBACjB,IAAI,IAAI,OAAO,MAAM,GAAG,KAAK;oBAC3B,IAAI,cAAc,GAAG;wBACnB,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,MAAM,GAAG;wBAClC,OAAO,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI;wBACnC,OAAO,OAAO,GAAG,KAAK,MAAM,KAAK,MAAM;wBAEvC,WAAW;wBACX,IAAI,KAAK,MAAM,KAAK,KAAK;4BACvB,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,GAC3D,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;wBAC/D;oBACF;oBACA;gBACF;gBAEA,iBAAiB;gBACjB,MAAM,mBAAmB;gBACzB,MAAM,cAAc,OAAO,KAAK,CAAC,MAAM;gBACvC,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,CAAC,cAAc,gBAAgB,IAAI,eAAe,OAAO,OAAO;gBAE1F,kBAAkB;gBAClB,IAAI,cAAc,GAAG;oBACnB,IAAI,SAAS,GAAG,AAAC,uBAA4B,OAAN,OAAM;gBAC/C,OAAO,IAAI,YAAY,GAAG;oBACxB,IAAI,SAAS,GAAG,AAAC,uBAA4B,OAAN,OAAM;gBAC/C,OAAO;oBACL,IAAI,SAAS,GAAG,AAAC,oBAAyB,OAAN,OAAM;gBAC5C;gBAEA,OAAO;gBACP,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,GAAG,GAAG;gBAEjC,SAAS;gBACT,IAAI,KAAK,MAAM,KAAK,MAAM;oBACxB,IAAI,SAAS,GAAG,AAAC,uBAAkC,OAAZ,QAAQ,KAAI;oBACnD,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,GAAG,GAAG;gBACnC;YACF;YAEA,QAAQ;YACR,OAAO,CAAC,IAAI,OAAO,KAAK;QAC1B;QAEA,OAAO;QACP,IAAI,WAAW;YACb,aAAa,OAAO,GAAG,sBAAsB;QAC/C;IACF;IAEA,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;YACA;YAEA,WAAW;YACX,MAAM;qDAAe;oBACnB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC;wCAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,IAAI,aAAa,OAAO,EAAE;wBACxB,qBAAqB,aAAa,OAAO;oBAC3C;gBACF;;QACF;+BAAG;QAAC;KAAU;IAEd,WAAW;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;+DAAyB;oBAC7B,aAAa,CAAC,SAAS,MAAM;gBAC/B;;YAEA,SAAS,gBAAgB,CAAC,oBAAoB;YAC9C;wCAAO,IAAM,SAAS,mBAAmB,CAAC,oBAAoB;;QAChE;+BAAG,EAAE;IAEL,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAI;QACxB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;QAAE;;0BAE1B,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,YAAY;oBACZ,cAAc;gBAChB;;;;;;0BAIF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY;gBACd;;;;;;0BAIF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY;gBACd;;;;;;;;;;;;AAIR;GAvMM;KAAA;uCAyMS", "debugId": null}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/components/navigation/CyberNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAppStore } from '@/store/app-store';\nimport { Menu, X, Zap, Shield, Users, Phone, Settings } from 'lucide-react';\n\n/**\n * 🧭 赛博朋克导航组件\n */\nconst CyberNavigation: React.FC = () => {\n  const {\n    theme,\n    currentMode,\n    userType,\n    setCurrentMode,\n    setTheme,\n    recordInteraction\n  } = useAppStore();\n\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  // 🎯 滚动检测\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // 🎨 导航项配置\n  const navigationItems = [\n    {\n      id: 'intro',\n      label: '序章',\n      icon: Zap,\n      description: '神秘开场',\n      techLabel: '系统初始化',\n      businessLabel: '公司介绍'\n    },\n    {\n      id: 'products',\n      label: '产品',\n      icon: Shield,\n      description: '核心产品矩阵',\n      techLabel: '技术架构',\n      businessLabel: '解决方案'\n    },\n    {\n      id: 'capabilities',\n      label: '能力',\n      icon: Zap,\n      description: '攻防实力展示',\n      techLabel: '技术能力',\n      businessLabel: '服务能力'\n    },\n    {\n      id: 'cases',\n      label: '案例',\n      icon: Users,\n      description: '成功案例',\n      techLabel: '技术案例',\n      businessLabel: '商业案例'\n    },\n    {\n      id: 'contact',\n      label: '联系',\n      icon: Phone,\n      description: '加入我们',\n      techLabel: '技术交流',\n      businessLabel: '商务合作'\n    }\n  ];\n\n  // 🎬 动画变体\n  const navVariants = {\n    hidden: { y: -100, opacity: 0 },\n    visible: { \n      y: 0, \n      opacity: 1,\n      transition: { \n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  const menuVariants = {\n    closed: { \n      opacity: 0,\n      scale: 0.95,\n      y: -20\n    },\n    open: { \n      opacity: 1,\n      scale: 1,\n      y: 0,\n      transition: {\n        duration: 0.2,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  const itemVariants = {\n    closed: { opacity: 0, x: -20 },\n    open: (i: number) => ({\n      opacity: 1,\n      x: 0,\n      transition: {\n        delay: i * 0.1,\n        duration: 0.3\n      }\n    })\n  };\n\n  // 🎯 处理导航点击\n  const handleNavClick = (mode: string) => {\n    setCurrentMode(mode as any);\n    setIsMenuOpen(false);\n    recordInteraction(`navigate_${mode}`);\n  };\n\n  // 🎨 获取主题样式\n  const getNavStyles = () => {\n    const baseStyles = \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\";\n    \n    if (isScrolled) {\n      switch (theme) {\n        case 'mystical':\n          return `${baseStyles} bg-cyber-dark/90 backdrop-blur-md border-b border-cyber-green/30`;\n        case 'business':\n          return `${baseStyles} bg-white/90 backdrop-blur-md border-b border-gray-200`;\n        case 'adaptive':\n          return `${baseStyles} bg-slate-900/90 backdrop-blur-md border-b border-cyber-green/20`;\n        default:\n          return `${baseStyles} bg-cyber-dark/90 backdrop-blur-md border-b border-cyber-green/30`;\n      }\n    }\n    \n    return `${baseStyles} bg-transparent`;\n  };\n\n  const getTextColor = () => {\n    switch (theme) {\n      case 'mystical':\n        return 'text-cyber-green';\n      case 'business':\n        return 'text-slate-800';\n      case 'adaptive':\n        return 'text-cyber-green';\n      default:\n        return 'text-cyber-green';\n    }\n  };\n\n  const getAccentColor = () => {\n    switch (theme) {\n      case 'mystical':\n        return 'text-cyber-gold';\n      case 'business':\n        return 'text-blue-600';\n      case 'adaptive':\n        return 'text-cyber-gold';\n      default:\n        return 'text-cyber-gold';\n    }\n  };\n\n  return (\n    <motion.nav\n      className={getNavStyles()}\n      variants={navVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* 🔮 Logo区域 */}\n          <motion.div \n            className=\"flex items-center space-x-3 cursor-pointer\"\n            onClick={() => handleNavClick('intro')}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <div className=\"relative\">\n              <div className={`w-8 h-8 rounded-full border-2 ${theme === 'mystical' ? 'border-cyber-green' : 'border-current'} flex items-center justify-center`}>\n                <span className=\"text-sm font-bold\">☸️</span>\n              </div>\n              {theme === 'mystical' && (\n                <div className=\"absolute inset-0 rounded-full border-2 border-cyber-green animate-cyber-pulse opacity-50\" />\n              )}\n            </div>\n            <div>\n              <h1 className={`text-lg font-bold font-mono ${getTextColor()}`}>\n                神秘技术组织\n              </h1>\n              <p className={`text-xs ${getAccentColor()}`}>\n                技术驱动，安全至上，代码无界\n              </p>\n            </div>\n          </motion.div>\n\n          {/* 🖥️ 桌面端导航 */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = currentMode === item.id;\n              const label = userType === 'tech-track' ? item.techLabel : \n                           userType === 'business-track' ? item.businessLabel : \n                           item.label;\n\n              return (\n                <motion.button\n                  key={item.id}\n                  onClick={() => handleNavClick(item.id)}\n                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                    isActive \n                      ? `${theme === 'mystical' ? 'bg-cyber-green/20 text-cyber-green' : 'bg-blue-100 text-blue-600'} cyber-glow` \n                      : `${getTextColor()} hover:${theme === 'mystical' ? 'text-cyber-gold' : 'text-blue-600'}`\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Icon size={16} />\n                  <span className=\"font-mono text-sm\">{label}</span>\n                  {isActive && theme === 'mystical' && (\n                    <motion.div\n                      className=\"w-1 h-1 bg-cyber-green rounded-full\"\n                      animate={{ scale: [1, 1.5, 1] }}\n                      transition={{ duration: 1, repeat: Infinity }}\n                    />\n                  )}\n                </motion.button>\n              );\n            })}\n          </div>\n\n          {/* 🎨 主题切换和设置 */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {/* 主题切换 */}\n            <div className=\"flex items-center space-x-1 bg-black/20 rounded-lg p-1\">\n              {(['mystical', 'business', 'adaptive'] as const).map((themeOption) => (\n                <button\n                  key={themeOption}\n                  onClick={() => setTheme(themeOption)}\n                  className={`px-2 py-1 rounded text-xs font-mono transition-all ${\n                    theme === themeOption \n                      ? 'bg-cyber-green text-black' \n                      : `${getTextColor()} hover:bg-white/10`\n                  }`}\n                  title={`切换到${themeOption}主题`}\n                >\n                  {themeOption === 'mystical' ? '🔮' : \n                   themeOption === 'business' ? '💼' : '🌐'}\n                </button>\n              ))}\n            </div>\n\n            {/* 设置按钮 */}\n            <motion.button\n              className={`p-2 rounded-lg ${getTextColor()} hover:bg-white/10 transition-colors`}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              <Settings size={16} />\n            </motion.button>\n          </div>\n\n          {/* 📱 移动端菜单按钮 */}\n          <motion.button\n            className={`md:hidden p-2 rounded-lg ${getTextColor()}`}\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n          >\n            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </motion.button>\n        </div>\n      </div>\n\n      {/* 📱 移动端菜单 */}\n      <AnimatePresence>\n        {isMenuOpen && (\n          <motion.div\n            className={`md:hidden absolute top-full left-0 right-0 ${\n              theme === 'mystical' \n                ? 'bg-cyber-dark/95 border-cyber-green/30' \n                : 'bg-white/95 border-gray-200'\n            } backdrop-blur-md border-b`}\n            variants={menuVariants}\n            initial=\"closed\"\n            animate=\"open\"\n            exit=\"closed\"\n          >\n            <div className=\"px-4 py-6 space-y-4\">\n              {navigationItems.map((item, index) => {\n                const Icon = item.icon;\n                const isActive = currentMode === item.id;\n                const label = userType === 'tech-track' ? item.techLabel : \n                             userType === 'business-track' ? item.businessLabel : \n                             item.label;\n\n                return (\n                  <motion.button\n                    key={item.id}\n                    custom={index}\n                    variants={itemVariants}\n                    onClick={() => handleNavClick(item.id)}\n                    className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all ${\n                      isActive \n                        ? `${theme === 'mystical' ? 'bg-cyber-green/20 text-cyber-green' : 'bg-blue-100 text-blue-600'}` \n                        : `${getTextColor()} hover:bg-white/10`\n                    }`}\n                  >\n                    <Icon size={20} />\n                    <div className=\"text-left\">\n                      <div className=\"font-mono font-medium\">{label}</div>\n                      <div className={`text-xs ${getAccentColor()}`}>{item.description}</div>\n                    </div>\n                  </motion.button>\n                );\n              })}\n              \n              {/* 移动端主题切换 */}\n              <div className=\"pt-4 border-t border-current/20\">\n                <p className={`text-sm font-mono mb-2 ${getTextColor()}`}>主题选择</p>\n                <div className=\"flex space-x-2\">\n                  {(['mystical', 'business', 'adaptive'] as const).map((themeOption) => (\n                    <button\n                      key={themeOption}\n                      onClick={() => setTheme(themeOption)}\n                      className={`flex-1 py-2 rounded text-xs font-mono transition-all ${\n                        theme === themeOption \n                          ? 'bg-cyber-green text-black' \n                          : `${getTextColor()} border border-current/30`\n                      }`}\n                    >\n                      {themeOption === 'mystical' ? '🔮 神秘' : \n                       themeOption === 'business' ? '💼 商务' : '🌐 自适应'}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n};\n\nexport default CyberNavigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOA;;CAEC,GACD,MAAM,kBAA4B;;IAChC,MAAM,EACJ,KAAK,EACL,WAAW,EACX,QAAQ,EACR,cAAc,EACd,QAAQ,EACR,iBAAiB,EAClB,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;0DAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;oCAAG,EAAE;IAEL,WAAW;IACX,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,OAAO;YACP,MAAM,mMAAA,CAAA,MAAG;YACT,aAAa;YACb,WAAW;YACX,eAAe;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,yMAAA,CAAA,SAAM;YACZ,aAAa;YACb,WAAW;YACX,eAAe;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,mMAAA,CAAA,MAAG;YACT,aAAa;YACb,WAAW;YACX,eAAe;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,aAAa;YACb,WAAW;YACX,eAAe;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,aAAa;YACb,WAAW;YACX,eAAe;QACjB;KACD;IAED,UAAU;IACV,MAAM,cAAc;QAClB,QAAQ;YAAE,GAAG,CAAC;YAAK,SAAS;QAAE;QAC9B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,OAAO;YACP,GAAG,CAAC;QACN;QACA,MAAM;YACJ,SAAS;YACT,OAAO;YACP,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,MAAM,CAAC,IAAc,CAAC;gBACpB,SAAS;gBACT,GAAG;gBACH,YAAY;oBACV,OAAO,IAAI;oBACX,UAAU;gBACZ;YACF,CAAC;IACH;IAEA,YAAY;IACZ,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,cAAc;QACd,kBAAkB,AAAC,YAAgB,OAAL;IAChC;IAEA,YAAY;IACZ,MAAM,eAAe;QACnB,MAAM,aAAa;QAEnB,IAAI,YAAY;YACd,OAAQ;gBACN,KAAK;oBACH,OAAO,AAAC,GAAa,OAAX,YAAW;gBACvB,KAAK;oBACH,OAAO,AAAC,GAAa,OAAX,YAAW;gBACvB,KAAK;oBACH,OAAO,AAAC,GAAa,OAAX,YAAW;gBACvB;oBACE,OAAO,AAAC,GAAa,OAAX,YAAW;YACzB;QACF;QAEA,OAAO,AAAC,GAAa,OAAX,YAAW;IACvB;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;;0BAER,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS,IAAM,eAAe;4BAC9B,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,AAAC,iCAA+F,OAA/D,UAAU,aAAa,uBAAuB,kBAAiB;sDAC9G,cAAA,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;wCAErC,UAAU,4BACT,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGnB,6LAAC;;sDACC,6LAAC;4CAAG,WAAW,AAAC,+BAA6C,OAAf;sDAAkB;;;;;;sDAGhE,6LAAC;4CAAE,WAAW,AAAC,WAA2B,OAAjB;sDAAoB;;;;;;;;;;;;;;;;;;sCAOjD,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,gBAAgB,KAAK,EAAE;gCACxC,MAAM,QAAQ,aAAa,eAAe,KAAK,SAAS,GAC3C,aAAa,mBAAmB,KAAK,aAAa,GAClD,KAAK,KAAK;gCAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS,IAAM,eAAe,KAAK,EAAE;oCACrC,WAAW,AAAC,gFAIX,OAHC,WACI,AAAC,GAA4F,OAA1F,UAAU,aAAa,uCAAuC,6BAA4B,iBAC7F,AAAC,GAA0B,OAAxB,gBAAe,WAAoE,OAA3D,UAAU,aAAa,oBAAoB;oCAE5E,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,6LAAC;4CAAK,MAAM;;;;;;sDACZ,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;wCACpC,YAAY,UAAU,4BACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;;;;;;;mCAhB3C,KAAK,EAAE;;;;;4BAqBlB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,AAAC;wCAAC;wCAAY;wCAAY;qCAAW,CAAW,GAAG,CAAC,CAAC,4BACpD,6LAAC;4CAEC,SAAS,IAAM,SAAS;4CACxB,WAAW,AAAC,sDAIX,OAHC,UAAU,cACN,8BACA,AAAC,GAAiB,OAAf,gBAAe;4CAExB,OAAO,AAAC,MAAiB,OAAZ,aAAY;sDAExB,gBAAgB,aAAa,OAC7B,gBAAgB,aAAa,OAAO;2CAVhC;;;;;;;;;;8CAgBX,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAW,AAAC,kBAAgC,OAAf,gBAAe;oCAC5C,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;8CAEvB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAKpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAW,AAAC,4BAA0C,OAAf;4BACvC,SAAS,IAAM,cAAc,CAAC;4BAC9B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,UAAU;gCAAE,OAAO;4BAAI;sCAEtB,2BAAa,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;yFAAS,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAMlD,6LAAC,4LAAA,CAAA,kBAAe;0BACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,AAAC,8CAIX,OAHC,UAAU,aACN,2CACA,+BACL;oBACD,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;8BAEL,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM;gCAC1B,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,gBAAgB,KAAK,EAAE;gCACxC,MAAM,QAAQ,aAAa,eAAe,KAAK,SAAS,GAC3C,aAAa,mBAAmB,KAAK,aAAa,GAClD,KAAK,KAAK;gCAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,QAAQ;oCACR,UAAU;oCACV,SAAS,IAAM,eAAe,KAAK,EAAE;oCACrC,WAAW,AAAC,oEAIX,OAHC,WACI,AAAC,GAA4F,OAA1F,UAAU,aAAa,uCAAuC,+BACjE,AAAC,GAAiB,OAAf,gBAAe;;sDAGxB,6LAAC;4CAAK,MAAM;;;;;;sDACZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAyB;;;;;;8DACxC,6LAAC;oDAAI,WAAW,AAAC,WAA2B,OAAjB;8DAAqB,KAAK,WAAW;;;;;;;;;;;;;mCAb7D,KAAK,EAAE;;;;;4BAiBlB;0CAGA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAW,AAAC,0BAAwC,OAAf;kDAAkB;;;;;;kDAC1D,6LAAC;wCAAI,WAAU;kDACZ,AAAC;4CAAC;4CAAY;4CAAY;yCAAW,CAAW,GAAG,CAAC,CAAC,4BACpD,6LAAC;gDAEC,SAAS,IAAM,SAAS;gDACxB,WAAW,AAAC,wDAIX,OAHC,UAAU,cACN,8BACA,AAAC,GAAiB,OAAf,gBAAe;0DAGvB,gBAAgB,aAAa,UAC7B,gBAAgB,aAAa,UAAU;+CATnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoB3B;GAvVM;;QAQA,+HAAA,CAAA,cAAW;;;KARX;uCAyVS", "debugId": null}}, {"offset": {"line": 1694, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/components/ui/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface LoadingScreenProps {\n  message?: string;\n  progress?: number;\n  showProgress?: boolean;\n}\n\n/**\n * 🔮 赛博朋克加载屏幕组件\n */\nconst LoadingScreen: React.FC<LoadingScreenProps> = ({ \n  message = \"初始化赛博空间...\", \n  progress,\n  showProgress = false \n}) => {\n  const [displayText, setDisplayText] = useState('');\n  const [currentProgress, setCurrentProgress] = useState(0);\n  const [loadingSteps, setLoadingSteps] = useState([\n    { text: '连接赛博空间...', completed: false },\n    { text: '加载神秘协议...', completed: false },\n    { text: '初始化量子矩阵...', completed: false },\n    { text: '激活防护系统...', completed: false },\n    { text: '准备展示界面...', completed: false }\n  ]);\n\n  // 🎬 打字机效果\n  useEffect(() => {\n    let index = 0;\n    const timer = setInterval(() => {\n      if (index < message.length) {\n        setDisplayText(message.slice(0, index + 1));\n        index++;\n      } else {\n        clearInterval(timer);\n      }\n    }, 50);\n\n    return () => clearInterval(timer);\n  }, [message]);\n\n  // 📊 进度模拟\n  useEffect(() => {\n    if (progress !== undefined) {\n      setCurrentProgress(progress);\n      return;\n    }\n\n    // 自动进度模拟\n    const progressTimer = setInterval(() => {\n      setCurrentProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(progressTimer);\n          return 100;\n        }\n        return prev + Math.random() * 3;\n      });\n    }, 100);\n\n    return () => clearInterval(progressTimer);\n  }, [progress]);\n\n  // 🎯 步骤完成模拟\n  useEffect(() => {\n    const stepTimer = setInterval(() => {\n      setLoadingSteps(prev => {\n        const nextIncompleteIndex = prev.findIndex(step => !step.completed);\n        if (nextIncompleteIndex === -1) {\n          clearInterval(stepTimer);\n          return prev;\n        }\n\n        const newSteps = [...prev];\n        newSteps[nextIncompleteIndex].completed = true;\n        return newSteps;\n      });\n    }, 800);\n\n    return () => clearInterval(stepTimer);\n  }, []);\n\n  // 🎨 动画变体\n  const containerVariants = {\n    initial: { opacity: 0 },\n    animate: { \n      opacity: 1,\n      transition: { duration: 0.5 }\n    },\n    exit: { \n      opacity: 0,\n      transition: { duration: 0.5 }\n    }\n  };\n\n  const logoVariants = {\n    initial: { scale: 0, rotate: -180 },\n    animate: { \n      scale: 1, \n      rotate: 0,\n      transition: { \n        duration: 1,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  const pulseVariants = {\n    animate: {\n      scale: [1, 1.2, 1],\n      opacity: [0.5, 1, 0.5],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }\n    }\n  };\n\n  const progressBarVariants = {\n    initial: { width: 0 },\n    animate: { \n      width: `${currentProgress}%`,\n      transition: { \n        duration: 0.5,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className=\"fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-cyber-dark via-cyber-black to-cyber-dark\"\n      variants={containerVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      exit=\"exit\"\n    >\n      {/* 🌟 背景粒子效果 */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        {[...Array(50)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-1 h-1 bg-cyber-green rounded-full\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n            }}\n            animate={{\n              opacity: [0, 1, 0],\n              scale: [0, 1, 0],\n            }}\n            transition={{\n              duration: Math.random() * 3 + 2,\n              repeat: Infinity,\n              delay: Math.random() * 2,\n            }}\n          />\n        ))}\n      </div>\n\n      {/* 🔮 主要内容 */}\n      <div className=\"relative z-10 text-center max-w-md mx-auto px-6\">\n        {/* Logo和符号 */}\n        <motion.div\n          className=\"mb-8\"\n          variants={logoVariants}\n          initial=\"initial\"\n          animate=\"animate\"\n        >\n          <div className=\"relative inline-block\">\n            <motion.div\n              className=\"w-20 h-20 mx-auto mb-4 rounded-full border-2 border-cyber-green flex items-center justify-center text-3xl\"\n              variants={pulseVariants}\n              animate=\"animate\"\n            >\n              ☸️\n            </motion.div>\n            \n            {/* 外圈脉冲效果 */}\n            <motion.div\n              className=\"absolute inset-0 rounded-full border-2 border-cyber-green opacity-30\"\n              animate={{\n                scale: [1, 1.5, 2],\n                opacity: [0.3, 0.1, 0],\n              }}\n              transition={{\n                duration: 2,\n                repeat: Infinity,\n                ease: \"easeOut\"\n              }}\n            />\n          </div>\n\n          <h1 className=\"text-2xl font-bold font-mono text-cyber-green cyber-glow mb-2\">\n            神秘技术组织\n          </h1>\n          <p className=\"text-cyber-gold text-sm font-mono\">\n            众生即我，我即众生，宇宙之卵\n          </p>\n        </motion.div>\n\n        {/* 加载消息 */}\n        <div className=\"mb-8\">\n          <motion.p \n            className=\"text-cyber-green font-mono text-lg mb-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.5 }}\n          >\n            {displayText}\n            <motion.span\n              className=\"inline-block w-2 h-5 bg-cyber-green ml-1\"\n              animate={{ opacity: [1, 0] }}\n              transition={{ duration: 0.8, repeat: Infinity }}\n            />\n          </motion.p>\n\n          {/* 进度条 */}\n          {showProgress && (\n            <div className=\"mb-6\">\n              <div className=\"w-full h-2 bg-cyber-dark border border-cyber-green/30 rounded-full overflow-hidden\">\n                <motion.div\n                  className=\"h-full bg-gradient-to-r from-cyber-green to-cyber-gold\"\n                  variants={progressBarVariants}\n                  initial=\"initial\"\n                  animate=\"animate\"\n                />\n              </div>\n              <p className=\"text-cyber-gold text-sm font-mono mt-2\">\n                {Math.round(currentProgress)}%\n              </p>\n            </div>\n          )}\n        </div>\n\n        {/* 加载步骤 */}\n        <div className=\"space-y-3\">\n          {loadingSteps.map((step, index) => (\n            <motion.div\n              key={index}\n              className=\"flex items-center space-x-3 text-sm font-mono\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.2 }}\n            >\n              <div className=\"relative\">\n                {step.completed ? (\n                  <motion.div\n                    className=\"w-3 h-3 bg-cyber-green rounded-full\"\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    transition={{ duration: 0.3 }}\n                  />\n                ) : (\n                  <motion.div\n                    className=\"w-3 h-3 border border-cyber-green rounded-full\"\n                    animate={{ \n                      borderColor: ['#00ff41', '#ffd700', '#00ff41'],\n                    }}\n                    transition={{ \n                      duration: 1.5, \n                      repeat: Infinity \n                    }}\n                  />\n                )}\n              </div>\n              <span className={step.completed ? 'text-cyber-green' : 'text-cyber-gold'}>\n                {step.text}\n              </span>\n              {step.completed && (\n                <motion.span\n                  className=\"text-cyber-green\"\n                  initial={{ opacity: 0, scale: 0 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  ✓\n                </motion.span>\n              )}\n            </motion.div>\n          ))}\n        </div>\n\n        {/* 底部提示 */}\n        <motion.div\n          className=\"mt-8 text-xs text-cyber-gold/70 font-mono\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 2 }}\n        >\n          <p>正在建立安全连接...</p>\n          <p className=\"mt-1\">请稍候，即将进入赛博朋克世界</p>\n        </motion.div>\n      </div>\n\n      {/* 🌐 底部装饰线条 */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-cyber-green to-transparent\">\n        <motion.div\n          className=\"h-full bg-cyber-gold\"\n          animate={{\n            x: ['-100%', '100%'],\n          }}\n          transition={{\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n          style={{ width: '20%' }}\n        />\n      </div>\n    </motion.div>\n  );\n};\n\nexport default LoadingScreen;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAWA;;CAEC,GACD,MAAM,gBAA8C;QAAC,EACnD,UAAU,YAAY,EACtB,QAAQ,EACR,eAAe,KAAK,EACrB;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C;YAAE,MAAM;YAAa,WAAW;QAAM;QACtC;YAAE,MAAM;YAAa,WAAW;QAAM;QACtC;YAAE,MAAM;YAAc,WAAW;QAAM;QACvC;YAAE,MAAM;YAAa,WAAW;QAAM;QACtC;YAAE,MAAM;YAAa,WAAW;QAAM;KACvC;IAED,WAAW;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,QAAQ;YACZ,MAAM,QAAQ;iDAAY;oBACxB,IAAI,QAAQ,QAAQ,MAAM,EAAE;wBAC1B,eAAe,QAAQ,KAAK,CAAC,GAAG,QAAQ;wBACxC;oBACF,OAAO;wBACL,cAAc;oBAChB;gBACF;gDAAG;YAEH;2CAAO,IAAM,cAAc;;QAC7B;kCAAG;QAAC;KAAQ;IAEZ,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,aAAa,WAAW;gBAC1B,mBAAmB;gBACnB;YACF;YAEA,SAAS;YACT,MAAM,gBAAgB;yDAAY;oBAChC;iEAAmB,CAAA;4BACjB,IAAI,QAAQ,KAAK;gCACf,cAAc;gCACd,OAAO;4BACT;4BACA,OAAO,OAAO,KAAK,MAAM,KAAK;wBAChC;;gBACF;wDAAG;YAEH;2CAAO,IAAM,cAAc;;QAC7B;kCAAG;QAAC;KAAS;IAEb,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,YAAY;qDAAY;oBAC5B;6DAAgB,CAAA;4BACd,MAAM,sBAAsB,KAAK,SAAS;yFAAC,CAAA,OAAQ,CAAC,KAAK,SAAS;;4BAClE,IAAI,wBAAwB,CAAC,GAAG;gCAC9B,cAAc;gCACd,OAAO;4BACT;4BAEA,MAAM,WAAW;mCAAI;6BAAK;4BAC1B,QAAQ,CAAC,oBAAoB,CAAC,SAAS,GAAG;4BAC1C,OAAO;wBACT;;gBACF;oDAAG;YAEH;2CAAO,IAAM,cAAc;;QAC7B;kCAAG,EAAE;IAEL,UAAU;IACV,MAAM,oBAAoB;QACxB,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YACP,SAAS;YACT,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,MAAM;YACJ,SAAS;YACT,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,OAAO;YAAG,QAAQ,CAAC;QAAI;QAClC,SAAS;YACP,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,SAAS;YACP,OAAO;gBAAC;gBAAG;gBAAK;aAAE;YAClB,SAAS;gBAAC;gBAAK;gBAAG;aAAI;YACtB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,MAAM,sBAAsB;QAC1B,SAAS;YAAE,OAAO;QAAE;QACpB,SAAS;YACP,OAAO,AAAC,GAAkB,OAAhB,iBAAgB;YAC1B,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,MAAK;;0BAGL,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,OAAO;4BACL,MAAM,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;4BAC7B,KAAK,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;wBAC9B;wBACA,SAAS;4BACP,SAAS;gCAAC;gCAAG;gCAAG;6BAAE;4BAClB,OAAO;gCAAC;gCAAG;gCAAG;6BAAE;wBAClB;wBACA,YAAY;4BACV,UAAU,KAAK,MAAM,KAAK,IAAI;4BAC9B,QAAQ;4BACR,OAAO,KAAK,MAAM,KAAK;wBACzB;uBAdK;;;;;;;;;;0BAoBX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;wBACV,SAAQ;wBACR,SAAQ;;0CAER,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAU;wCACV,SAAQ;kDACT;;;;;;kDAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;4CAClB,SAAS;gDAAC;gDAAK;gDAAK;6CAAE;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,MAAM;wCACR;;;;;;;;;;;;0CAIJ,6LAAC;gCAAG,WAAU;0CAAgE;;;;;;0CAG9E,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;kCAMnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;;oCAExB;kDACD,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,WAAU;wCACV,SAAS;4CAAE,SAAS;gDAAC;gDAAG;6CAAE;wCAAC;wCAC3B,YAAY;4CAAE,UAAU;4CAAK,QAAQ;wCAAS;;;;;;;;;;;;4BAKjD,8BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,UAAU;4CACV,SAAQ;4CACR,SAAQ;;;;;;;;;;;kDAGZ,6LAAC;wCAAE,WAAU;;4CACV,KAAK,KAAK,CAAC;4CAAiB;;;;;;;;;;;;;;;;;;;kCAOrC,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;;kDAEjC,6LAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,iBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,YAAY;gDAAE,UAAU;4CAAI;;;;;qGAG9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,aAAa;oDAAC;oDAAW;oDAAW;iDAAU;4CAChD;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;4CACV;;;;;;;;;;;kDAIN,6LAAC;wCAAK,WAAW,KAAK,SAAS,GAAG,qBAAqB;kDACpD,KAAK,IAAI;;;;;;oCAEX,KAAK,SAAS,kBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;kDAC7B;;;;;;;+BApCE;;;;;;;;;;kCA6CX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAE;;0CAEvB,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAE,WAAU;0CAAO;;;;;;;;;;;;;;;;;;0BAKxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBACP,GAAG;4BAAC;4BAAS;yBAAO;oBACtB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,MAAM;oBACR;oBACA,OAAO;wBAAE,OAAO;oBAAM;;;;;;;;;;;;;;;;;AAKhC;GA7SM;KAAA;uCA+SS", "debugId": null}}, {"offset": {"line": 2258, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/components/layout/CyberLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAppStore } from '@/store/app-store';\nimport { globalPathDetector } from '@/lib/path-detector';\nimport ParticleBackground from '@/components/effects/ParticleBackground';\nimport MatrixRain from '@/components/effects/MatrixRain';\nimport CyberNavigation from '@/components/navigation/CyberNavigation';\nimport LoadingScreen from '@/components/ui/LoadingScreen';\n\ninterface CyberLayoutProps {\n  children: React.ReactNode;\n}\n\n/**\n * 🔮 赛博朋克主布局组件\n * 提供全局的视觉效果、导航和状态管理\n */\nconst CyberLayout: React.FC<CyberLayoutProps> = ({ children }) => {\n  const {\n    theme,\n    showParticles,\n    enableAnimations,\n    isLoading,\n    loadingMessage,\n    userType,\n    setUserType,\n    setPathRecommendation,\n    recordInteraction,\n    setBehaviorData\n  } = useAppStore();\n\n  const [mounted, setMounted] = useState(false);\n  const [userDetected, setUserDetected] = useState(false);\n\n  // 🚀 组件挂载和用户检测\n  useEffect(() => {\n    setMounted(true);\n    \n    // 记录初始访问\n    recordInteraction('page_load');\n    \n    // 检测用户环境\n    const detectUserEnvironment = () => {\n      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n      const userAgent = navigator.userAgent;\n      const referrer = document.referrer;\n      \n      setBehaviorData({\n        isMobile,\n        userAgent,\n        referrer\n      });\n      \n      // 初始路径检测\n      globalPathDetector.recordBehavior({\n        isMobile,\n        userAgent,\n        referrer\n      });\n      \n      // 延迟进行用户类型检测\n      setTimeout(() => {\n        const recommendation = globalPathDetector.detectUserType();\n        setPathRecommendation(recommendation);\n        setUserType(recommendation.userType);\n        setUserDetected(true);\n      }, 2000);\n    };\n\n    detectUserEnvironment();\n  }, [recordInteraction, setBehaviorData, setPathRecommendation, setUserType]);\n\n  // 🎯 页面可见性检测\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      if (document.hidden) {\n        recordInteraction('page_hidden');\n      } else {\n        recordInteraction('page_visible');\n      }\n    };\n\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);\n  }, [recordInteraction]);\n\n  // 🎨 主题样式计算\n  const getThemeClasses = () => {\n    const baseClasses = 'min-h-screen relative overflow-hidden';\n    \n    switch (theme) {\n      case 'mystical':\n        return `${baseClasses} bg-gradient-to-br from-cyber-dark via-cyber-black to-cyber-dark text-cyber-green`;\n      case 'business':\n        return `${baseClasses} bg-gradient-to-br from-slate-50 via-white to-slate-100 text-slate-800`;\n      case 'adaptive':\n        return `${baseClasses} bg-gradient-to-br from-cyber-dark via-slate-900 to-cyber-black text-cyber-green`;\n      default:\n        return `${baseClasses} matrix-bg text-cyber-green`;\n    }\n  };\n\n  // 🎬 动画变体\n  const layoutVariants = {\n    initial: { opacity: 0 },\n    animate: { \n      opacity: 1,\n      transition: { \n        duration: 1,\n        ease: \"easeOut\"\n      }\n    },\n    exit: { \n      opacity: 0,\n      transition: { \n        duration: 0.5\n      }\n    }\n  };\n\n  const contentVariants = {\n    initial: { y: 20, opacity: 0 },\n    animate: { \n      y: 0, \n      opacity: 1,\n      transition: { \n        delay: 0.3,\n        duration: 0.8,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  // 🔄 加载状态处理\n  if (!mounted) {\n    return <LoadingScreen message=\"初始化赛博空间...\" />;\n  }\n\n  if (isLoading) {\n    return <LoadingScreen message={loadingMessage || \"加载中...\"} />;\n  }\n\n  return (\n    <motion.div\n      className={getThemeClasses()}\n      variants={enableAnimations ? layoutVariants : undefined}\n      initial=\"initial\"\n      animate=\"animate\"\n      exit=\"exit\"\n    >\n      {/* 🌟 背景效果层 */}\n      <div className=\"fixed inset-0 z-0\">\n        {/* 粒子背景 */}\n        <AnimatePresence>\n          {showParticles && theme === 'mystical' && (\n            <ParticleBackground key=\"particles\" />\n          )}\n        </AnimatePresence>\n\n        {/* Matrix雨效果 */}\n        <AnimatePresence>\n          {enableAnimations && theme === 'mystical' && (\n            <MatrixRain key=\"matrix\" />\n          )}\n        </AnimatePresence>\n\n        {/* 商务主题背景 */}\n        {theme === 'business' && (\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-slate-50 opacity-90\" />\n        )}\n\n        {/* 自适应主题背景 */}\n        {theme === 'adaptive' && (\n          <div className=\"absolute inset-0\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-cyber-dark via-slate-900 to-cyber-black opacity-80\" />\n            <div className=\"absolute inset-0 bg-gradient-to-tr from-transparent via-cyber-green/5 to-transparent\" />\n          </div>\n        )}\n      </div>\n\n      {/* 🧭 导航层 */}\n      <div className=\"relative z-20\">\n        <CyberNavigation />\n      </div>\n\n      {/* 📱 主内容层 */}\n      <motion.main\n        className=\"relative z-10 min-h-screen\"\n        variants={enableAnimations ? contentVariants : undefined}\n        initial=\"initial\"\n        animate=\"animate\"\n      >\n        {children}\n      </motion.main>\n\n      {/* 🎯 用户类型检测提示 */}\n      <AnimatePresence>\n        {userDetected && userType !== 'mixed-track' && (\n          <motion.div\n            className=\"fixed bottom-4 right-4 z-30 max-w-sm\"\n            initial={{ opacity: 0, x: 100 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: 100 }}\n            transition={{ duration: 0.5 }}\n          >\n            <div className=\"cyber-bg cyber-border rounded-lg p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-cyber-green rounded-full animate-cyber-pulse\" />\n                <span className=\"text-sm font-mono\">\n                  {userType === 'tech-track' ? '🔧 技术路径已激活' : '💼 商务路径已激活'}\n                </span>\n              </div>\n              <p className=\"text-xs text-cyber-gold mt-1\">\n                基于您的行为模式，我们为您定制了专属展示内容\n              </p>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* 🎵 音效控制 */}\n      <div className=\"fixed bottom-4 left-4 z-30\">\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => useAppStore.getState().toggleSound()}\n            className=\"cyber-border rounded-full p-2 cyber-bg hover:bg-cyber-green/20 transition-colors\"\n            title=\"切换音效\"\n          >\n            <span className=\"text-xs\">🔊</span>\n          </button>\n          <button\n            onClick={() => useAppStore.getState().toggleBackgroundMusic()}\n            className=\"cyber-border rounded-full p-2 cyber-bg hover:bg-cyber-green/20 transition-colors\"\n            title=\"切换背景音乐\"\n          >\n            <span className=\"text-xs\">🎵</span>\n          </button>\n        </div>\n      </div>\n\n      {/* 🔮 神秘元素 - 地藏王菩萨符号 */}\n      {theme === 'mystical' && (\n        <div className=\"fixed top-4 left-4 z-30 opacity-30\">\n          <motion.div\n            className=\"text-cyber-gold text-2xl\"\n            animate={{ \n              rotate: [0, 360],\n              scale: [1, 1.1, 1]\n            }}\n            transition={{ \n              duration: 20,\n              repeat: Infinity,\n              ease: \"linear\"\n            }}\n          >\n            ☸️\n          </motion.div>\n        </div>\n      )}\n\n      {/* 📊 开发调试信息 */}\n      {process.env.NODE_ENV === 'development' && (\n        <div className=\"fixed top-4 right-4 z-50 text-xs font-mono bg-black/80 text-green-400 p-2 rounded\">\n          <div>主题: {theme}</div>\n          <div>用户类型: {userType}</div>\n          <div>动画: {enableAnimations ? '开启' : '关闭'}</div>\n          <div>粒子: {showParticles ? '开启' : '关闭'}</div>\n        </div>\n      )}\n    </motion.div>\n  );\n};\n\nexport default CyberLayout;\n"], "names": [], "mappings": ";;;AAuQO;;AArQP;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAeA;;;CAGC,GACD,MAAM,cAA0C;QAAC,EAAE,QAAQ,EAAE;;IAC3D,MAAM,EACJ,KAAK,EACL,aAAa,EACb,gBAAgB,EAChB,SAAS,EACT,cAAc,EACd,QAAQ,EACR,WAAW,EACX,qBAAqB,EACrB,iBAAiB,EACjB,eAAe,EAChB,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;YAEX,SAAS;YACT,kBAAkB;YAElB,SAAS;YACT,MAAM;+DAAwB;oBAC5B,MAAM,WAAW,iEAAiE,IAAI,CAAC,UAAU,SAAS;oBAC1G,MAAM,YAAY,UAAU,SAAS;oBACrC,MAAM,WAAW,SAAS,QAAQ;oBAElC,gBAAgB;wBACd;wBACA;wBACA;oBACF;oBAEA,SAAS;oBACT,iIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;wBAChC;wBACA;wBACA;oBACF;oBAEA,aAAa;oBACb;uEAAW;4BACT,MAAM,iBAAiB,iIAAA,CAAA,qBAAkB,CAAC,cAAc;4BACxD,sBAAsB;4BACtB,YAAY,eAAe,QAAQ;4BACnC,gBAAgB;wBAClB;sEAAG;gBACL;;YAEA;QACF;gCAAG;QAAC;QAAmB;QAAiB;QAAuB;KAAY;IAE3E,aAAa;IACb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;gEAAyB;oBAC7B,IAAI,SAAS,MAAM,EAAE;wBACnB,kBAAkB;oBACpB,OAAO;wBACL,kBAAkB;oBACpB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,oBAAoB;YAC9C;yCAAO,IAAM,SAAS,mBAAmB,CAAC,oBAAoB;;QAChE;gCAAG;QAAC;KAAkB;IAEtB,YAAY;IACZ,MAAM,kBAAkB;QACtB,MAAM,cAAc;QAEpB,OAAQ;YACN,KAAK;gBACH,OAAO,AAAC,GAAc,OAAZ,aAAY;YACxB,KAAK;gBACH,OAAO,AAAC,GAAc,OAAZ,aAAY;YACxB,KAAK;gBACH,OAAO,AAAC,GAAc,OAAZ,aAAY;YACxB;gBACE,OAAO,AAAC,GAAc,OAAZ,aAAY;QAC1B;IACF;IAEA,UAAU;IACV,MAAM,iBAAiB;QACrB,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,MAAM;YACJ,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,SAAS;YAAE,GAAG;YAAI,SAAS;QAAE;QAC7B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,OAAO;gBACP,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,YAAY;IACZ,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC,4IAAA,CAAA,UAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,IAAI,WAAW;QACb,qBAAO,6LAAC,4IAAA,CAAA,UAAa;YAAC,SAAS,kBAAkB;;;;;;IACnD;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU,mBAAmB,iBAAiB;QAC9C,SAAQ;QACR,SAAQ;QACR,MAAK;;0BAGL,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,4LAAA,CAAA,kBAAe;kCACb,iBAAiB,UAAU,4BAC1B,6LAAC,sJAAA,CAAA,UAAkB,MAAK;;;;;;;;;;kCAK5B,6LAAC,4LAAA,CAAA,kBAAe;kCACb,oBAAoB,UAAU,4BAC7B,6LAAC,8IAAA,CAAA,UAAU,MAAK;;;;;;;;;;oBAKnB,UAAU,4BACT,6LAAC;wBAAI,WAAU;;;;;;oBAIhB,UAAU,4BACT,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAMrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sJAAA,CAAA,UAAe;;;;;;;;;;0BAIlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,WAAU;gBACV,UAAU,mBAAmB,kBAAkB;gBAC/C,SAAQ;gBACR,SAAQ;0BAEP;;;;;;0BAIH,6LAAC,4LAAA,CAAA,kBAAe;0BACb,gBAAgB,aAAa,+BAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAI;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG;oBAAI;oBAC3B,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDACb,aAAa,eAAe,eAAe;;;;;;;;;;;;0CAGhD,6LAAC;gCAAE,WAAU;0CAA+B;;;;;;;;;;;;;;;;;;;;;;0BASpD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,+HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,WAAW;4BACjD,WAAU;4BACV,OAAM;sCAEN,cAAA,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;sCAE5B,6LAAC;4BACC,SAAS,IAAM,+HAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,qBAAqB;4BAC3D,WAAU;4BACV,OAAM;sCAEN,cAAA,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;YAM/B,UAAU,4BACT,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBACP,QAAQ;4BAAC;4BAAG;yBAAI;wBAChB,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;oBACpB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,MAAM;oBACR;8BACD;;;;;;;;;;;YAOJ,oDAAyB,+BACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAI;4BAAK;;;;;;;kCACV,6LAAC;;4BAAI;4BAAO;;;;;;;kCACZ,6LAAC;;4BAAI;4BAAK,mBAAmB,OAAO;;;;;;;kCACpC,6LAAC;;4BAAI;4BAAK,gBAAgB,OAAO;;;;;;;;;;;;;;;;;;;AAK3C;GA9PM;;QAYA,+HAAA,CAAA,cAAW;;;KAZX;uCAgQS", "debugId": null}}, {"offset": {"line": 2736, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/components/effects/HologramText.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface HologramTextProps {\n  children: React.ReactNode;\n  className?: string;\n  glowColor?: string;\n  scanlineSpeed?: number;\n}\n\n/**\n * 🌟 全息文字效果组件\n * 创建赛博朋克风格的全息投影文字效果\n */\nconst HologramText: React.FC<HologramTextProps> = ({\n  children,\n  className = '',\n  glowColor = '#00ff41',\n  scanlineSpeed = 2\n}) => {\n  return (\n    <div className={`relative inline-block ${className}`}>\n      {/* 主文字 */}\n      <motion.div\n        className=\"relative z-10\"\n        style={{\n          color: glowColor,\n          textShadow: `\n            0 0 5px ${glowColor},\n            0 0 10px ${glowColor},\n            0 0 15px ${glowColor},\n            0 0 20px ${glowColor}\n          `,\n          filter: 'brightness(1.2)'\n        }}\n        initial={{ opacity: 0.8 }}\n        animate={{ \n          opacity: [0.8, 1, 0.9, 1],\n          filter: [\n            'brightness(1.2) hue-rotate(0deg)',\n            'brightness(1.4) hue-rotate(5deg)',\n            'brightness(1.1) hue-rotate(-3deg)',\n            'brightness(1.2) hue-rotate(0deg)'\n          ]\n        }}\n        transition={{\n          duration: 3,\n          repeat: Infinity,\n          ease: \"easeInOut\"\n        }}\n      >\n        {children}\n      </motion.div>\n\n      {/* 扫描线效果 */}\n      <motion.div\n        className=\"absolute inset-0 z-20 pointer-events-none\"\n        style={{\n          background: `linear-gradient(\n            90deg,\n            transparent 0%,\n            transparent 45%,\n            ${glowColor}40 50%,\n            transparent 55%,\n            transparent 100%\n          )`,\n          mixBlendMode: 'screen'\n        }}\n        initial={{ x: '-100%' }}\n        animate={{ x: '200%' }}\n        transition={{\n          duration: scanlineSpeed,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n      />\n\n      {/* 故障效果 */}\n      <motion.div\n        className=\"absolute inset-0 z-5\"\n        style={{\n          color: glowColor,\n          textShadow: `2px 0 ${glowColor}80, -2px 0 #ff004080`,\n          opacity: 0\n        }}\n        animate={{\n          opacity: [0, 0, 0, 0.3, 0, 0, 0, 0.2, 0],\n          x: [0, 2, -1, 1, 0, -2, 1, 0, 0],\n          y: [0, -1, 1, 0, 1, -1, 0, 1, 0]\n        }}\n        transition={{\n          duration: 4,\n          repeat: Infinity,\n          times: [0, 0.1, 0.2, 0.25, 0.3, 0.4, 0.5, 0.55, 1]\n        }}\n      >\n        {children}\n      </motion.div>\n\n      {/* 边缘发光 */}\n      <div\n        className=\"absolute inset-0 z-0 blur-sm\"\n        style={{\n          color: glowColor,\n          textShadow: `\n            0 0 10px ${glowColor}80,\n            0 0 20px ${glowColor}60,\n            0 0 30px ${glowColor}40\n          `,\n          opacity: 0.6\n        }}\n      >\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default HologramText;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAYA;;;CAGC,GACD,MAAM,eAA4C;QAAC,EACjD,QAAQ,EACR,YAAY,EAAE,EACd,YAAY,SAAS,EACrB,gBAAgB,CAAC,EAClB;IACC,qBACE,6LAAC;QAAI,WAAW,AAAC,yBAAkC,OAAV;;0BAEvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,OAAO;oBACP,YAAY,AAAC,yBAEA,OADD,WAAU,4BAET,OADA,WAAU,4BAEV,OADA,WAAU,4BACA,OAAV,WAAU;oBAEvB,QAAQ;gBACV;gBACA,SAAS;oBAAE,SAAS;gBAAI;gBACxB,SAAS;oBACP,SAAS;wBAAC;wBAAK;wBAAG;wBAAK;qBAAE;oBACzB,QAAQ;wBACN;wBACA;wBACA;wBACA;qBACD;gBACH;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BAEC;;;;;;0BAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,YAAY,AAAC,gHAIC,OAAV,WAAU;oBAId,cAAc;gBAChB;gBACA,SAAS;oBAAE,GAAG;gBAAQ;gBACtB,SAAS;oBAAE,GAAG;gBAAO;gBACrB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,OAAO;oBACP,YAAY,AAAC,SAAkB,OAAV,WAAU;oBAC/B,SAAS;gBACX;gBACA,SAAS;oBACP,SAAS;wBAAC;wBAAG;wBAAG;wBAAG;wBAAK;wBAAG;wBAAG;wBAAG;wBAAK;qBAAE;oBACxC,GAAG;wBAAC;wBAAG;wBAAG,CAAC;wBAAG;wBAAG;wBAAG,CAAC;wBAAG;wBAAG;wBAAG;qBAAE;oBAChC,GAAG;wBAAC;wBAAG,CAAC;wBAAG;wBAAG;wBAAG;wBAAG,CAAC;wBAAG;wBAAG;wBAAG;qBAAE;gBAClC;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,OAAO;wBAAC;wBAAG;wBAAK;wBAAK;wBAAM;wBAAK;wBAAK;wBAAK;wBAAM;qBAAE;gBACpD;0BAEC;;;;;;0BAIH,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,OAAO;oBACP,YAAY,AAAC,0BAEA,OADA,WAAU,8BAEV,OADA,WAAU,8BACA,OAAV,WAAU;oBAEvB,SAAS;gBACX;0BAEC;;;;;;;;;;;;AAIT;KAtGM;uCAwGS", "debugId": null}}, {"offset": {"line": 2905, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * 🎨 样式类合并工具函数\n * 结合clsx和tailwind-merge，智能合并CSS类名\n */\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * 🔧 延迟执行工具函数\n */\nexport function delay(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n/**\n * 🎯 防抖函数\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * 🚀 节流函数\n */\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n/**\n * 🎲 随机数生成器\n */\nexport function random(min: number, max: number): number {\n  return Math.random() * (max - min) + min;\n}\n\n/**\n * 🎲 随机整数生成器\n */\nexport function randomInt(min: number, max: number): number {\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n}\n\n/**\n * 🎨 随机颜色生成器\n */\nexport function randomColor(): string {\n  const colors = [\n    '#00ff41', // cyber-green\n    '#ffd700', // cyber-gold\n    '#ff0080', // cyber-pink\n    '#00ffff', // cyber-cyan\n    '#ff4500', // cyber-orange\n  ];\n  return colors[randomInt(0, colors.length - 1)];\n}\n\n/**\n * 📱 设备检测\n */\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false;\n  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n}\n\n/**\n * 🖥️ 浏览器检测\n */\nexport function getBrowser(): string {\n  if (typeof window === 'undefined') return 'unknown';\n  \n  const userAgent = navigator.userAgent;\n  \n  if (userAgent.includes('Chrome')) return 'chrome';\n  if (userAgent.includes('Firefox')) return 'firefox';\n  if (userAgent.includes('Safari')) return 'safari';\n  if (userAgent.includes('Edge')) return 'edge';\n  if (userAgent.includes('Opera')) return 'opera';\n  \n  return 'unknown';\n}\n\n/**\n * 🔐 简单哈希函数\n */\nexport function simpleHash(str: string): number {\n  let hash = 0;\n  for (let i = 0; i < str.length; i++) {\n    const char = str.charCodeAt(i);\n    hash = ((hash << 5) - hash) + char;\n    hash = hash & hash; // Convert to 32bit integer\n  }\n  return Math.abs(hash);\n}\n\n/**\n * 📊 格式化文件大小\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n/**\n * ⏰ 格式化时间\n */\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor((seconds % 3600) / 60);\n  const secs = Math.floor(seconds % 60);\n  \n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  }\n  return `${minutes}:${secs.toString().padStart(2, '0')}`;\n}\n\n/**\n * 🎯 深拷贝函数\n */\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') return obj;\n  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;\n  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;\n  if (typeof obj === 'object') {\n    const clonedObj = {} as T;\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key]);\n      }\n    }\n    return clonedObj;\n  }\n  return obj;\n}\n\n/**\n * 🔍 对象路径获取\n */\nexport function getObjectPath(obj: any, path: string): any {\n  return path.split('.').reduce((current, key) => current?.[key], obj);\n}\n\n/**\n * 🎨 CSS变量获取\n */\nexport function getCSSVariable(name: string): string {\n  if (typeof window === 'undefined') return '';\n  return getComputedStyle(document.documentElement).getPropertyValue(name).trim();\n}\n\n/**\n * 🎨 CSS变量设置\n */\nexport function setCSSVariable(name: string, value: string): void {\n  if (typeof window === 'undefined') return;\n  document.documentElement.style.setProperty(name, value);\n}\n\n/**\n * 📋 复制到剪贴板\n */\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    if (navigator.clipboard && window.isSecureContext) {\n      await navigator.clipboard.writeText(text);\n      return true;\n    } else {\n      // 降级方案\n      const textArea = document.createElement('textarea');\n      textArea.value = text;\n      textArea.style.position = 'fixed';\n      textArea.style.left = '-999999px';\n      textArea.style.top = '-999999px';\n      document.body.appendChild(textArea);\n      textArea.focus();\n      textArea.select();\n      const result = document.execCommand('copy');\n      textArea.remove();\n      return result;\n    }\n  } catch (error) {\n    console.error('Failed to copy text: ', error);\n    return false;\n  }\n}\n\n/**\n * 🎵 音效播放\n */\nexport function playSound(soundId: string, volume: number = 0.5): void {\n  try {\n    const audio = document.getElementById(soundId) as HTMLAudioElement;\n    if (audio) {\n      audio.volume = Math.max(0, Math.min(1, volume));\n      audio.currentTime = 0;\n      audio.play().catch(() => {\n        // 静默处理音频播放失败\n      });\n    }\n  } catch (error) {\n    // 静默处理错误\n  }\n}\n\n/**\n * 🌟 生成唯一ID\n */\nexport function generateId(prefix: string = 'id'): string {\n  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n}\n\n/**\n * 🎯 数组随机打乱\n */\nexport function shuffleArray<T>(array: T[]): T[] {\n  const shuffled = [...array];\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n}\n\n/**\n * 📊 数值映射\n */\nexport function mapRange(\n  value: number,\n  inMin: number,\n  inMax: number,\n  outMin: number,\n  outMax: number\n): number {\n  return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;\n}\n\n/**\n * 🎨 颜色转换 - HEX to RGB\n */\nexport function hexToRgb(hex: string): { r: number; g: number; b: number } | null {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  return result ? {\n    r: parseInt(result[1], 16),\n    g: parseInt(result[2], 16),\n    b: parseInt(result[3], 16)\n  } : null;\n}\n\n/**\n * 🎨 颜色转换 - RGB to HEX\n */\nexport function rgbToHex(r: number, g: number, b: number): string {\n  return \"#\" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAMO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAKO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAKO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAKO,SAAS,OAAO,GAAW,EAAE,GAAW;IAC7C,OAAO,KAAK,MAAM,KAAK,CAAC,MAAM,GAAG,IAAI;AACvC;AAKO,SAAS,UAAU,GAAW,EAAE,GAAW;IAChD,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,MAAM,MAAM,CAAC,KAAK;AACvD;AAKO,SAAS;IACd,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;KACD;IACD,OAAO,MAAM,CAAC,UAAU,GAAG,OAAO,MAAM,GAAG,GAAG;AAChD;AAKO,SAAS;IACd;;IACA,OAAO,iEAAiE,IAAI,CAAC,UAAU,SAAS;AAClG;AAKO,SAAS;IACd;;IAEA,MAAM,YAAY,UAAU,SAAS;IAErC,IAAI,UAAU,QAAQ,CAAC,WAAW,OAAO;IACzC,IAAI,UAAU,QAAQ,CAAC,YAAY,OAAO;IAC1C,IAAI,UAAU,QAAQ,CAAC,WAAW,OAAO;IACzC,IAAI,UAAU,QAAQ,CAAC,SAAS,OAAO;IACvC,IAAI,UAAU,QAAQ,CAAC,UAAU,OAAO;IAExC,OAAO;AACT;AAKO,SAAS,WAAW,GAAW;IACpC,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,MAAM,OAAO,IAAI,UAAU,CAAC;QAC5B,OAAO,AAAC,CAAC,QAAQ,CAAC,IAAI,OAAQ;QAC9B,OAAO,OAAO,MAAM,2BAA2B;IACjD;IACA,OAAO,KAAK,GAAG,CAAC;AAClB;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;IAElC,IAAI,QAAQ,GAAG;QACb,OAAO,AAAC,GAAW,OAAT,OAAM,KAA0C,OAAvC,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACxF;IACA,OAAO,AAAC,GAAa,OAAX,SAAQ,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;AACnD;AAKO,SAAS,UAAa,GAAM;IACjC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,OAAO;IACpD,IAAI,eAAe,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO;IACpD,IAAI,eAAe,OAAO,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,UAAU;IAC3D,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,YAAY,CAAC;QACnB,IAAK,MAAM,OAAO,IAAK;YACrB,IAAI,IAAI,cAAc,CAAC,MAAM;gBAC3B,SAAS,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,IAAI;YACrC;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AAKO,SAAS,cAAc,GAAQ,EAAE,IAAY;IAClD,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,SAAS,MAAQ,oBAAA,8BAAA,OAAS,CAAC,IAAI,EAAE;AAClE;AAKO,SAAS,eAAe,IAAY;IACzC;;IACA,OAAO,iBAAiB,SAAS,eAAe,EAAE,gBAAgB,CAAC,MAAM,IAAI;AAC/E;AAKO,SAAS,eAAe,IAAY,EAAE,KAAa;IACxD;;IACA,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM;AACnD;AAKO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;YACjD,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,OAAO;QACT,OAAO;YACL,OAAO;YACP,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,GAAG;YACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;YAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;YACtB,SAAS,KAAK,CAAC,GAAG,GAAG;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,SAAS,KAAK;YACd,SAAS,MAAM;YACf,MAAM,SAAS,SAAS,WAAW,CAAC;YACpC,SAAS,MAAM;YACf,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAKO,SAAS,UAAU,OAAe;QAAE,SAAA,iEAAiB;IAC1D,IAAI;QACF,MAAM,QAAQ,SAAS,cAAc,CAAC;QACtC,IAAI,OAAO;YACT,MAAM,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;YACvC,MAAM,WAAW,GAAG;YACpB,MAAM,IAAI,GAAG,KAAK,CAAC;YACjB,aAAa;YACf;QACF;IACF,EAAE,OAAO,OAAO;IACd,SAAS;IACX;AACF;AAKO,SAAS;QAAW,SAAA,iEAAiB;IAC1C,OAAO,AAAC,GAAY,OAAV,QAAO,KAAiB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AACzE;AAKO,SAAS,aAAgB,KAAU;IACxC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AAKO,SAAS,SACd,KAAa,EACb,KAAa,EACb,KAAa,EACb,MAAc,EACd,MAAc;IAEd,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,QAAQ,KAAK,IAAI;AACjE;AAKO,SAAS,SAAS,GAAW;IAClC,MAAM,SAAS,4CAA4C,IAAI,CAAC;IAChE,OAAO,SAAS;QACd,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;IACzB,IAAI;AACN;AAKO,SAAS,SAAS,CAAS,EAAE,CAAS,EAAE,CAAS;IACtD,OAAO,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AACzE", "debugId": null}}, {"offset": {"line": 3136, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/components/ui/CyberCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef, useState } from 'react';\nimport { motion, HTMLMotionProps } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\n// 🎨 卡片变体类型\nexport type CyberCardVariant = \n  | 'default'     // 默认样式\n  | 'elevated'    // 悬浮效果\n  | 'outlined'    // 边框样式\n  | 'glass'       // 玻璃态\n  | 'matrix'      // Matrix风格\n  | 'hologram'    // 全息投影\n  | 'neon';       // 霓虹灯效果\n\n// 🎯 卡片尺寸类型\nexport type CyberCardSize = 'sm' | 'md' | 'lg' | 'xl';\n\n// 🔧 卡片属性接口\nexport interface CyberCardProps extends Omit<HTMLMotionProps<'div'>, 'size'> {\n  variant?: CyberCardVariant;\n  size?: CyberCardSize;\n  interactive?: boolean;\n  glowEffect?: boolean;\n  scanlineEffect?: boolean;\n  borderAnimation?: boolean;\n  header?: React.ReactNode;\n  footer?: React.ReactNode;\n  children: React.ReactNode;\n}\n\n/**\n * 🔮 赛博朋克风格卡片组件\n */\nconst CyberCard = forwardRef<HTMLDivElement, CyberCardProps>(({\n  variant = 'default',\n  size = 'md',\n  interactive = false,\n  glowEffect = false,\n  scanlineEffect = false,\n  borderAnimation = false,\n  header,\n  footer,\n  className,\n  children,\n  ...props\n}, ref) => {\n  \n  const [isHovered, setIsHovered] = useState(false);\n\n  // 🎨 获取变体样式\n  const getVariantStyles = (variant: CyberCardVariant) => {\n    const styles = {\n      default: 'bg-cyber-dark/80 border-cyber-green/30 backdrop-blur-sm',\n      elevated: 'bg-cyber-dark/90 border-cyber-green/50 shadow-lg shadow-cyber-green/20',\n      outlined: 'bg-transparent border-cyber-green border-2',\n      glass: 'bg-white/5 border-white/10 backdrop-blur-md',\n      matrix: 'bg-cyber-dark/95 border-cyber-green matrix-bg',\n      hologram: 'bg-gradient-to-br from-cyber-green/10 to-cyber-gold/10 border-cyber-green hologram-effect',\n      neon: 'bg-cyber-dark/80 border-cyber-green neon-glow'\n    };\n    return styles[variant];\n  };\n\n  // 📏 获取尺寸样式\n  const getSizeStyles = (size: CyberCardSize) => {\n    const styles = {\n      sm: 'p-3 rounded-md',\n      md: 'p-4 rounded-lg',\n      lg: 'p-6 rounded-xl',\n      xl: 'p-8 rounded-2xl'\n    };\n    return styles[size];\n  };\n\n  // 🎬 动画变体\n  const cardVariants = {\n    initial: { \n      scale: 1,\n      rotateX: 0,\n      rotateY: 0\n    },\n    hover: interactive ? {\n      scale: 1.02,\n      rotateX: 2,\n      rotateY: 2,\n      transition: { \n        duration: 0.3,\n        ease: \"easeOut\"\n      }\n    } : {},\n    tap: interactive ? {\n      scale: 0.98,\n      transition: { \n        duration: 0.1\n      }\n    } : {}\n  };\n\n  const glowVariants = {\n    animate: glowEffect ? {\n      boxShadow: [\n        '0 0 10px rgba(0, 255, 65, 0.3)',\n        '0 0 30px rgba(0, 255, 65, 0.5)',\n        '0 0 10px rgba(0, 255, 65, 0.3)'\n      ],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: 'easeInOut'\n      }\n    } : {}\n  };\n\n  const borderVariants = {\n    animate: borderAnimation ? {\n      borderColor: [\n        'rgba(0, 255, 65, 0.3)',\n        'rgba(255, 215, 0, 0.5)',\n        'rgba(0, 255, 65, 0.3)'\n      ],\n      transition: {\n        duration: 3,\n        repeat: Infinity,\n        ease: 'easeInOut'\n      }\n    } : {}\n  };\n\n  // 🎨 组合样式类\n  const cardClasses = cn(\n    // 基础样式\n    'relative border transition-all duration-300',\n    'overflow-hidden',\n    \n    // 变体样式\n    getVariantStyles(variant),\n    \n    // 尺寸样式\n    getSizeStyles(size),\n    \n    // 交互样式\n    interactive && 'cursor-pointer select-none',\n    \n    // 自定义样式\n    className\n  );\n\n  return (\n    <motion.div\n      ref={ref}\n      className={cardClasses}\n      variants={cardVariants}\n      initial=\"initial\"\n      whileHover=\"hover\"\n      whileTap=\"tap\"\n      animate={[\n        glowEffect ? 'animate' : '',\n        borderAnimation ? 'animate' : ''\n      ].filter(Boolean)}\n      onHoverStart={() => setIsHovered(true)}\n      onHoverEnd={() => setIsHovered(false)}\n      style={{ perspective: '1000px' }}\n      {...props}\n    >\n      {/* 🌟 背景效果层 */}\n      {variant === 'matrix' && (\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"matrix-rain-card\" />\n        </div>\n      )}\n\n      {variant === 'hologram' && (\n        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-cyber-green/5 to-transparent animate-pulse\" />\n      )}\n\n      {/* 📺 扫描线效果 */}\n      {scanlineEffect && (\n        <div className=\"absolute inset-0 pointer-events-none\">\n          <motion.div\n            className=\"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-green/10 to-transparent\"\n            animate={{\n              y: ['-100%', '100%'],\n            }}\n            transition={{\n              duration: 2,\n              repeat: Infinity,\n              ease: 'linear'\n            }}\n            style={{ height: '20%' }}\n          />\n        </div>\n      )}\n\n      {/* 🔥 发光边框效果 */}\n      {glowEffect && isHovered && (\n        <motion.div\n          className=\"absolute inset-0 border-2 border-cyber-green rounded-lg opacity-0\"\n          variants={glowVariants}\n          animate=\"animate\"\n        />\n      )}\n\n      {/* 🎯 边框动画效果 */}\n      {borderAnimation && (\n        <>\n          {/* 顶部边框 */}\n          <motion.div\n            className=\"absolute top-0 left-0 h-0.5 bg-cyber-green\"\n            animate={{\n              width: ['0%', '100%', '0%'],\n            }}\n            transition={{\n              duration: 2,\n              repeat: Infinity,\n              ease: 'easeInOut'\n            }}\n          />\n          \n          {/* 右侧边框 */}\n          <motion.div\n            className=\"absolute top-0 right-0 w-0.5 bg-cyber-gold\"\n            animate={{\n              height: ['0%', '100%', '0%'],\n            }}\n            transition={{\n              duration: 2,\n              repeat: Infinity,\n              ease: 'easeInOut',\n              delay: 0.5\n            }}\n          />\n          \n          {/* 底部边框 */}\n          <motion.div\n            className=\"absolute bottom-0 right-0 h-0.5 bg-cyber-green\"\n            animate={{\n              width: ['0%', '100%', '0%'],\n            }}\n            transition={{\n              duration: 2,\n              repeat: Infinity,\n              ease: 'easeInOut',\n              delay: 1\n            }}\n          />\n          \n          {/* 左侧边框 */}\n          <motion.div\n            className=\"absolute bottom-0 left-0 w-0.5 bg-cyber-gold\"\n            animate={{\n              height: ['0%', '100%', '0%'],\n            }}\n            transition={{\n              duration: 2,\n              repeat: Infinity,\n              ease: 'easeInOut',\n              delay: 1.5\n            }}\n          />\n        </>\n      )}\n\n      {/* 📱 内容区域 */}\n      <div className=\"relative z-10 h-full flex flex-col\">\n        {/* 头部区域 */}\n        {header && (\n          <div className=\"mb-4 pb-4 border-b border-current/20\">\n            {header}\n          </div>\n        )}\n\n        {/* 主要内容 */}\n        <div className=\"flex-1\">\n          {children}\n        </div>\n\n        {/* 底部区域 */}\n        {footer && (\n          <div className=\"mt-4 pt-4 border-t border-current/20\">\n            {footer}\n          </div>\n        )}\n      </div>\n\n      {/* 🎯 交互反馈 */}\n      {interactive && (\n        <motion.div\n          className=\"absolute inset-0 bg-cyber-green/5 opacity-0 rounded-lg\"\n          animate={{\n            opacity: isHovered ? 1 : 0\n          }}\n          transition={{ duration: 0.2 }}\n        />\n      )}\n\n      {/* 🌐 霓虹灯效果 */}\n      {variant === 'neon' && (\n        <div className=\"absolute inset-0 rounded-lg\">\n          <div className=\"absolute inset-0 rounded-lg border-2 border-cyber-green animate-pulse opacity-50\" />\n          <div className=\"absolute inset-0 rounded-lg border border-cyber-gold animate-pulse opacity-30\" \n               style={{ animationDelay: '0.5s' }} />\n        </div>\n      )}\n    </motion.div>\n  );\n});\n\nCyberCard.displayName = 'CyberCard';\n\nexport default CyberCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAgCA;;CAEC,GACD,MAAM,0BAAY,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,UAAkC,QAY1D;QAZ2D,EAC5D,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,cAAc,KAAK,EACnB,aAAa,KAAK,EAClB,iBAAiB,KAAK,EACtB,kBAAkB,KAAK,EACvB,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;;IAEC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,YAAY;IACZ,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,SAAS;YACT,UAAU;YACV,UAAU;YACV,OAAO;YACP,QAAQ;YACR,UAAU;YACV,MAAM;QACR;QACA,OAAO,MAAM,CAAC,QAAQ;IACxB;IAEA,YAAY;IACZ,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS;YACb,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO,MAAM,CAAC,KAAK;IACrB;IAEA,UAAU;IACV,MAAM,eAAe;QACnB,SAAS;YACP,OAAO;YACP,SAAS;YACT,SAAS;QACX;QACA,OAAO,cAAc;YACnB,OAAO;YACP,SAAS;YACT,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF,IAAI,CAAC;QACL,KAAK,cAAc;YACjB,OAAO;YACP,YAAY;gBACV,UAAU;YACZ;QACF,IAAI,CAAC;IACP;IAEA,MAAM,eAAe;QACnB,SAAS,aAAa;YACpB,WAAW;gBACT;gBACA;gBACA;aACD;YACD,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF,IAAI,CAAC;IACP;IAEA,MAAM,iBAAiB;QACrB,SAAS,kBAAkB;YACzB,aAAa;gBACX;gBACA;gBACA;aACD;YACD,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF,IAAI,CAAC;IACP;IAEA,WAAW;IACX,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,OAAO;IACP,+CACA,mBAEA,OAAO;IACP,iBAAiB,UAEjB,OAAO;IACP,cAAc,OAEd,OAAO;IACP,eAAe,8BAEf,QAAQ;IACR;IAGF,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,UAAU;QACV,SAAQ;QACR,YAAW;QACX,UAAS;QACT,SAAS;YACP,aAAa,YAAY;YACzB,kBAAkB,YAAY;SAC/B,CAAC,MAAM,CAAC;QACT,cAAc,IAAM,aAAa;QACjC,YAAY,IAAM,aAAa;QAC/B,OAAO;YAAE,aAAa;QAAS;QAC9B,GAAG,KAAK;;YAGR,YAAY,0BACX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;YAIlB,YAAY,4BACX,6LAAC;gBAAI,WAAU;;;;;;YAIhB,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBACP,GAAG;4BAAC;4BAAS;yBAAO;oBACtB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,MAAM;oBACR;oBACA,OAAO;wBAAE,QAAQ;oBAAM;;;;;;;;;;;YAM5B,cAAc,2BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;;;;;;YAKX,iCACC;;kCAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAM;gCAAQ;6BAAK;wBAC7B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ;gCAAC;gCAAM;gCAAQ;6BAAK;wBAC9B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;;;;;;kCAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAM;gCAAQ;6BAAK;wBAC7B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;;;;;;kCAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ;gCAAC;gCAAM;gCAAQ;6BAAK;wBAC9B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;oBAEZ,wBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAKL,6LAAC;wBAAI,WAAU;kCACZ;;;;;;oBAIF,wBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAMN,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,SAAS,YAAY,IAAI;gBAC3B;gBACA,YAAY;oBAAE,UAAU;gBAAI;;;;;;YAK/B,YAAY,wBACX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBACV,OAAO;4BAAE,gBAAgB;wBAAO;;;;;;;;;;;;;;;;;;AAK/C;;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 3489, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/components/internal/PhilosophyModule.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport HologramText from '@/components/effects/HologramText';\nimport CyberCard from '@/components/ui/CyberCard';\n\ninterface PhilosophyTopic {\n  id: string;\n  title: string;\n  subtitle: string;\n  content: string;\n  meditation: string;\n  practice: string;\n  wisdom: string;\n  icon: string;\n}\n\nconst PhilosophyModule: React.FC = () => {\n  const [activeTopicIndex, setActiveTopicIndex] = useState(0);\n  const [isMediating, setIsMediating] = useState(false);\n  const [meditationTimer, setMeditationTimer] = useState(0);\n\n  const philosophyTopics: PhilosophyTopic[] = [\n    {\n      id: 'infinite-code',\n      title: '无限代码',\n      subtitle: 'The Infinite Possibilities of Code',\n      content: '在数字世界的深处，每一行代码都承载着无限的可能性。我们编写的不仅仅是程序，而是在创造新的数字现实。每个函数调用都是一次技术的突破，每个变量都承载着创新的潜能。',\n      meditation: '闭上眼睛，想象你的意识融入代码的海洋。感受每个字符的逻辑，每个算法的美感。在这个数字世界中，你不再是普通的程序员，而是技术的创造者。',\n      practice: '每天编写代码前，花5分钟思考。思考你即将创造的代码将如何影响数字世界的发展。让技术理想指导你的每一次键盘敲击。',\n      wisdom: '代码即是逻辑，逻辑即是美。在0和1的二元世界中，我们寻找技术的真谛。',\n      icon: '🥚'\n    },\n    {\n      id: 'digital-guardian',\n      title: '数字守护',\n      subtitle: 'Digital Guardian in Cyber Space',\n      content: '我们是数字世界的守护者，发下技术誓愿：漏洞不尽，誓不休息。每一个被修复的漏洞，都是对数字世界的守护贡献。我们用技术和智慧，为网络空间带来安全与和谐。',\n      meditation: '想象自己化身为数字守护者，在网络空间中巡行。看见每一个脆弱的系统，都需要我们的技术保护。用你的专业知识，为它们带来安全与稳定。',\n      practice: '在发现安全漏洞时，不要急于利用，而要思考如何以最专业的方式帮助修复。记住，每个系统背后都有无数用户的信任。',\n      wisdom: '真正的黑客不是破坏者，而是数字世界的医者。我们的使命是治愈，而非伤害。',\n      icon: '🙏'\n    },\n    {\n      id: 'code-zen',\n      title: '代码禅境',\n      subtitle: 'The Zen of Code',\n      content: '优雅的代码如同禅诗，简洁而深刻。每一个函数都应该像一句偈语，直指问题的本质。重构代码的过程，就是修行的过程，去除冗余，保留精华。',\n      meditation: '在编写代码时，保持正念。每敲击一个键，都要清楚地知道自己在做什么。让代码的结构反映你内心的清明。',\n      practice: '定期重构你的代码，就像定期清理你的心灵。删除不必要的复杂性，追求简洁和优雅。记住：最好的代码是不需要注释就能理解的代码。',\n      wisdom: '代码的美不在于复杂，而在于简洁。真正的大师能用最少的代码解决最复杂的问题。',\n      icon: '🧘'\n    },\n    {\n      id: 'network-karma',\n      title: '网络因果',\n      subtitle: 'Network Karma and Digital Ethics',\n      content: '在网络世界中，因果律同样适用。每一次恶意攻击都会种下恶果的种子，而每一次善意的帮助都会带来善报。我们的数字行为塑造着整个网络生态系统。',\n      meditation: '思考你在网络中的每一个行为。发送的每一个数据包，编写的每一行代码，都在影响着整个数字宇宙的和谐。让善意成为你的网络协议。',\n      practice: '在进行任何网络活动前，问自己三个问题：这是否有益？这是否必要？这是否慈悲？只有三个答案都是肯定的，才继续行动。',\n      wisdom: '网络空间是我们共同的家园。我们每个人都有责任维护它的和谐与安全。',\n      icon: '🌐'\n    },\n    {\n      id: 'binary-middle-way',\n      title: '二进制中道',\n      subtitle: 'The Middle Way in Binary World',\n      content: '在0和1的二元世界中，我们寻找中道的智慧。不执着于绝对的安全，也不放任绝对的自由。在攻击与防御之间，在开放与封闭之间，寻找平衡的艺术。',\n      meditation: '观想0和1不是对立的，而是互补的。就像阴阳一样，它们共同构成了数字世界的完整性。在这种理解中，找到你的平衡点。',\n      practice: '在设计安全系统时，不要走极端。既要保护用户的安全，也要保护他们的便利性和隐私。寻找技术与人性的平衡点。',\n      wisdom: '真正的智慧不在于选择0或1，而在于理解它们的统一性。',\n      icon: '☯️'\n    }\n  ];\n\n  const activeTopic = philosophyTopics[activeTopicIndex];\n\n  // 冥想计时器\n  useEffect(() => {\n    let interval: NodeJS.Timeout;\n    if (isMediating) {\n      interval = setInterval(() => {\n        setMeditationTimer(prev => prev + 1);\n      }, 1000);\n    } else {\n      setMeditationTimer(0);\n    }\n    return () => clearInterval(interval);\n  }, [isMediating]);\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const startMeditation = () => {\n    setIsMediating(true);\n  };\n\n  const stopMeditation = () => {\n    setIsMediating(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-b from-purple-900/20 via-black to-green-900/20 text-white p-6\">\n      <div className=\"max-w-6xl mx-auto\">\n        {/* 标题区域 */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center mb-12\"\n        >\n          <HologramText \n            text=\"数字禅境 · 技术修行\"\n            className=\"text-4xl md:text-6xl font-bold mb-4\"\n            glitchIntensity=\"low\"\n          />\n          <p className=\"text-xl text-green-300 font-mono\">\n            Digital Philosophy & Technical Enlightenment\n          </p>\n          <div className=\"mt-4 text-gold text-lg\">\n            技术驱动，安全至上，代码无界\n          </div>\n        </motion.div>\n\n        {/* 主题选择器 */}\n        <div className=\"flex flex-wrap justify-center gap-4 mb-8\">\n          {philosophyTopics.map((topic, index) => (\n            <motion.button\n              key={topic.id}\n              onClick={() => setActiveTopicIndex(index)}\n              className={`px-4 py-2 rounded-lg border font-mono transition-all ${\n                activeTopicIndex === index\n                  ? 'border-green-500 bg-green-900/30 text-green-300'\n                  : 'border-gray-600 bg-gray-900/30 text-gray-400 hover:border-green-400 hover:text-green-300'\n              }`}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <span className=\"mr-2\">{topic.icon}</span>\n              {topic.title}\n            </motion.button>\n          ))}\n        </div>\n\n        {/* 主要内容区域 */}\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={activeTopic.id}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            transition={{ duration: 0.5 }}\n            className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\"\n          >\n            {/* 左侧：哲学内容 */}\n            <div className=\"space-y-6\">\n              <CyberCard className=\"border-green-500/30\">\n                <div className=\"p-6\">\n                  <div className=\"flex items-center mb-4\">\n                    <span className=\"text-3xl mr-3\">{activeTopic.icon}</span>\n                    <div>\n                      <h2 className=\"text-2xl font-bold text-green-300\">{activeTopic.title}</h2>\n                      <p className=\"text-sm text-gray-400 font-mono\">{activeTopic.subtitle}</p>\n                    </div>\n                  </div>\n                  <p className=\"text-gray-200 leading-relaxed mb-4\">\n                    {activeTopic.content}\n                  </p>\n                  <div className=\"border-t border-green-500/20 pt-4\">\n                    <h4 className=\"text-gold font-bold mb-2\">💎 智慧箴言</h4>\n                    <p className=\"text-green-200 italic\">\"{activeTopic.wisdom}\"</p>\n                  </div>\n                </div>\n              </CyberCard>\n\n              <CyberCard className=\"border-purple-500/30\">\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-purple-300 mb-3\">🧘‍♂️ 冥想指导</h3>\n                  <p className=\"text-gray-200 leading-relaxed\">\n                    {activeTopic.meditation}\n                  </p>\n                </div>\n              </CyberCard>\n            </div>\n\n            {/* 右侧：实践与冥想 */}\n            <div className=\"space-y-6\">\n              <CyberCard className=\"border-blue-500/30\">\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-blue-300 mb-3\">⚡ 修行实践</h3>\n                  <p className=\"text-gray-200 leading-relaxed\">\n                    {activeTopic.practice}\n                  </p>\n                </div>\n              </CyberCard>\n\n              {/* 冥想计时器 */}\n              <CyberCard className=\"border-gold/30\">\n                <div className=\"p-6 text-center\">\n                  <h3 className=\"text-xl font-bold text-gold mb-4\">🕉️ 数字冥想计时器</h3>\n                  \n                  <div className=\"mb-6\">\n                    <div className=\"text-4xl font-mono text-green-400 mb-2\">\n                      {formatTime(meditationTimer)}\n                    </div>\n                    <div className=\"text-sm text-gray-400\">\n                      {isMediating ? '正在冥想中...' : '准备开始冥想'}\n                    </div>\n                  </div>\n\n                  <div className=\"flex justify-center gap-4\">\n                    {!isMediating ? (\n                      <motion.button\n                        onClick={startMeditation}\n                        className=\"px-6 py-3 bg-green-600 hover:bg-green-500 rounded-lg font-mono transition-colors\"\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        开始冥想\n                      </motion.button>\n                    ) : (\n                      <motion.button\n                        onClick={stopMeditation}\n                        className=\"px-6 py-3 bg-red-600 hover:bg-red-500 rounded-lg font-mono transition-colors\"\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        结束冥想\n                      </motion.button>\n                    )}\n                  </div>\n\n                  {isMediating && (\n                    <motion.div\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      className=\"mt-4 p-4 bg-green-900/20 rounded-lg\"\n                    >\n                      <p className=\"text-sm text-green-300\">\n                        专注于呼吸，让心灵与代码的韵律同步...\n                      </p>\n                      <motion.div\n                        animate={{ scale: [1, 1.1, 1] }}\n                        transition={{ repeat: Infinity, duration: 4 }}\n                        className=\"mt-2 text-2xl\"\n                      >\n                        🧘‍♂️\n                      </motion.div>\n                    </motion.div>\n                  )}\n                </div>\n              </CyberCard>\n\n              {/* 今日修行记录 */}\n              <CyberCard className=\"border-orange-500/30\">\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-orange-300 mb-3\">📊 修行统计</h3>\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-green-400\">7</div>\n                      <div className=\"text-sm text-gray-400\">今日冥想次数</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-blue-400\">42</div>\n                      <div className=\"text-sm text-gray-400\">总冥想分钟</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-purple-400\">108</div>\n                      <div className=\"text-sm text-gray-400\">修复漏洞数</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-gold\">∞</div>\n                      <div className=\"text-sm text-gray-400\">慈悲心指数</div>\n                    </div>\n                  </div>\n                </div>\n              </CyberCard>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n\n        {/* 底部引言 */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.8 }}\n          className=\"mt-12 text-center\"\n        >\n          <div className=\"p-6 border border-green-500/20 rounded-lg bg-black/50 backdrop-blur-sm\">\n            <p className=\"text-lg text-green-200 italic mb-2\">\n              \"在代码的海洋中，我们不仅是程序员，更是数字世界的修行者。\"\n            </p>\n            <p className=\"text-sm text-gray-400\">\n              —— 神秘技术组织内部修行指南\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default PhilosophyModule;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAkBA,MAAM,mBAA6B;;IACjC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,mBAAsC;QAC1C;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;YACT,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;YACT,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;YACT,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;YACT,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,SAAS;YACT,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,MAAM;QACR;KACD;IAED,MAAM,cAAc,gBAAgB,CAAC,iBAAiB;IAEtD,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI;YACJ,IAAI,aAAa;gBACf,WAAW;kDAAY;wBACrB;0DAAmB,CAAA,OAAQ,OAAO;;oBACpC;iDAAG;YACL,OAAO;gBACL,mBAAmB;YACrB;YACA;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;KAAY;IAEhB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,AAAC,GAAsC,OAApC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAC5E;IAEA,MAAM,kBAAkB;QACtB,eAAe;IACjB;IAEA,MAAM,iBAAiB;QACrB,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,WAAU;;sCAEV,6LAAC,gJAAA,CAAA,UAAY;4BACX,MAAK;4BACL,WAAU;4BACV,iBAAgB;;;;;;sCAElB,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;sCAGhD,6LAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;;8BAM1C,6LAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BAEZ,SAAS,IAAM,oBAAoB;4BACnC,WAAW,AAAC,wDAIX,OAHC,qBAAqB,QACjB,oDACA;4BAEN,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,6LAAC;oCAAK,WAAU;8CAAQ,MAAM,IAAI;;;;;;gCACjC,MAAM,KAAK;;2BAXP,MAAM,EAAE;;;;;;;;;;8BAiBnB,6LAAC,4LAAA,CAAA,kBAAe;oBAAC,MAAK;8BACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wIAAA,CAAA,UAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAiB,YAAY,IAAI;;;;;;sEACjD,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAqC,YAAY,KAAK;;;;;;8EACpE,6LAAC;oEAAE,WAAU;8EAAmC,YAAY,QAAQ;;;;;;;;;;;;;;;;;;8DAGxE,6LAAC;oDAAE,WAAU;8DACV,YAAY,OAAO;;;;;;8DAEtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2B;;;;;;sEACzC,6LAAC;4DAAE,WAAU;;gEAAwB;gEAAE,YAAY,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAKhE,6LAAC,wIAAA,CAAA,UAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DACV,YAAY,UAAU;;;;;;;;;;;;;;;;;;;;;;;0CAO/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wIAAA,CAAA,UAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,6LAAC;oDAAE,WAAU;8DACV,YAAY,QAAQ;;;;;;;;;;;;;;;;;kDAM3B,6LAAC,wIAAA,CAAA,UAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,WAAW;;;;;;sEAEd,6LAAC;4DAAI,WAAU;sEACZ,cAAc,aAAa;;;;;;;;;;;;8DAIhC,6LAAC;oDAAI,WAAU;8DACZ,CAAC,4BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,SAAS;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;kEACzB;;;;;iHAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,SAAS;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;kEACzB;;;;;;;;;;;gDAMJ,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;oDAAE;oDACtB,SAAS;wDAAE,SAAS;oDAAE;oDACtB,WAAU;;sEAEV,6LAAC;4DAAE,WAAU;sEAAyB;;;;;;sEAGtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,OAAO;oEAAC;oEAAG;oEAAK;iEAAE;4DAAC;4DAC9B,YAAY;gEAAE,QAAQ;gEAAU,UAAU;4DAAE;4DAC5C,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;kDAST,6LAAC,wIAAA,CAAA,UAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;8EACnD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAmC;;;;;;8EAClD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA+B;;;;;;8EAC9C,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA7H5C,YAAY,EAAE;;;;;;;;;;8BAuIvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAGlD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GA7RM;KAAA;uCA+RS", "debugId": null}}, {"offset": {"line": 4211, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/me/company/20250730%E4%BB%8B%E7%BB%8D%E4%BC%9A/cyber-showcase-new/src/app/internal/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport CyberLayout from '@/components/layout/CyberLayout';\nimport PhilosophyModule from '@/components/internal/PhilosophyModule';\nimport MatrixRain from '@/components/effects/MatrixRain';\n\n/**\n * 🟢 内部宇宙版本页面\n * 展示深度哲学思考和技术修行内容\n */\nexport default function InternalPage() {\n  return (\n    <CyberLayout>\n      {/* 背景效果 */}\n      <div className=\"fixed inset-0 z-0\">\n        <MatrixRain \n          color=\"#00ff41\" \n          speed={0.5}\n          density={0.2}\n        />\n      </div>\n\n      {/* 主要内容 */}\n      <div className=\"relative z-10\">\n        {/* 页面标题 */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center py-8 bg-black/80 backdrop-blur-sm border-b border-green-500/30\"\n        >\n          <h1 className=\"text-3xl md:text-5xl font-bold font-mono text-green-400 mb-2\">\n            内部宇宙版本\n          </h1>\n          <p className=\"text-lg text-purple-300 font-mono\">\n            Digital Philosophy & Technical Enlightenment\n          </p>\n          <div className=\"mt-2 text-sm text-gray-400\">\n            技术驱动，安全至上，代码无界 | 数字世界技术探索\n          </div>\n        </motion.div>\n\n        {/* 哲学模块 */}\n        <PhilosophyModule />\n\n        {/* 底部导航 */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1 }}\n          className=\"fixed bottom-4 left-4 right-4 z-20\"\n        >\n          <div className=\"flex justify-center space-x-4\">\n            <motion.button\n              onClick={() => window.location.href = '/'}\n              className=\"px-4 py-2 bg-green-600/80 hover:bg-green-500/80 border border-green-400 rounded-lg font-mono text-sm transition-colors backdrop-blur-sm\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              ← 返回主页\n            </motion.button>\n            \n            {/* 版本切换按钮 - 可通过开关控制显示 */}\n            {false && (\n              <motion.button\n                onClick={() => window.location.href = '/commercial'}\n                className=\"px-4 py-2 bg-red-600/80 hover:bg-red-500/80 border border-red-400 rounded-lg font-mono text-sm transition-colors backdrop-blur-sm\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                切换到商业版本 →\n              </motion.button>\n            )}\n          </div>\n        </motion.div>\n      </div>\n    </CyberLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAYe,SAAS;IACtB,qBACE,6LAAC,8IAAA,CAAA,UAAW;;0BAEV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8IAAA,CAAA,UAAU;oBACT,OAAM;oBACN,OAAO;oBACP,SAAS;;;;;;;;;;;0BAKb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAG7E,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;0CAGjD,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;;;;;;;kCAM9C,6LAAC,qJAAA,CAAA,UAAgB;;;;;kCAGjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAE;wBACvB,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CACzB;;;;;;gCAKA,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAnEwB", "debugId": null}}]}