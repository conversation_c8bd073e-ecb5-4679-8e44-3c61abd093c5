{"version": 3, "file": "TrackballControls.js", "sources": ["../../src/controls/TrackballControls.ts"], "sourcesContent": ["import { MOUS<PERSON>, Quatern<PERSON>, Vector2, Vector3, PerspectiveCamera, OrthographicCamera } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nclass TrackballControls extends EventDispatcher<StandardControlsEventMap> {\n  public enabled = true\n\n  public screen = { left: 0, top: 0, width: 0, height: 0 }\n\n  public rotateSpeed = 1.0\n  public zoomSpeed = 1.2\n  public panSpeed = 0.3\n\n  public noRotate = false\n  public noZoom = false\n  public noPan = false\n\n  public staticMoving = false\n  public dynamicDampingFactor = 0.2\n\n  public minDistance = 0\n  public maxDistance = Infinity\n\n  public keys: [string, string, string] = ['KeyA' /*A*/, 'KeyS' /*S*/, 'KeyD' /*D*/]\n\n  public mouseButtons = {\n    LEFT: MOUSE.ROTATE,\n    MIDDLE: MOUSE.DOLLY,\n    RIGHT: MOUSE.PAN,\n  }\n\n  public object: PerspectiveCamera | OrthographicCamera\n  public domElement: HTMLElement | undefined\n  public cursorZoom: boolean = false\n\n  readonly target = new Vector3()\n  private mousePosition = new Vector2()\n\n  // internals\n  private STATE = {\n    NONE: -1,\n    ROTATE: 0,\n    ZOOM: 1,\n    PAN: 2,\n    TOUCH_ROTATE: 3,\n    TOUCH_ZOOM_PAN: 4,\n  }\n\n  private EPS = 0.000001\n  private lastZoom = 1\n\n  private lastPosition = new Vector3()\n  private cursorVector = new Vector3()\n  private targetVector = new Vector3()\n\n  private _state = this.STATE.NONE\n  private _keyState = this.STATE.NONE\n  private _eye = new Vector3()\n  private _movePrev = new Vector2()\n  private _moveCurr = new Vector2()\n  private _lastAxis = new Vector3()\n  private _lastAngle = 0\n  private _zoomStart = new Vector2()\n  private _zoomEnd = new Vector2()\n  private _touchZoomDistanceStart = 0\n  private _touchZoomDistanceEnd = 0\n  private _panStart = new Vector2()\n  private _panEnd = new Vector2()\n\n  private target0: Vector3\n  private position0: Vector3\n  private up0: Vector3\n  private zoom0: number\n\n  // events\n\n  private changeEvent = { type: 'change' }\n  private startEvent = { type: 'start' }\n  private endEvent = { type: 'end' }\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super()\n    this.object = object\n\n    // for reset\n\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.up0 = this.object.up.clone()\n    this.zoom0 = this.object.zoom\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n\n    // force an update at start\n    this.update()\n  }\n\n  private onScreenVector = new Vector2()\n\n  private getMouseOnScreen = (pageX: number, pageY: number): Vector2 => {\n    this.onScreenVector.set(\n      (pageX - this.screen.left) / this.screen.width,\n      (pageY - this.screen.top) / this.screen.height,\n    )\n\n    return this.onScreenVector\n  }\n\n  private onCircleVector = new Vector2()\n\n  private getMouseOnCircle = (pageX: number, pageY: number): Vector2 => {\n    this.onCircleVector.set(\n      (pageX - this.screen.width * 0.5 - this.screen.left) / (this.screen.width * 0.5),\n      (this.screen.height + 2 * (this.screen.top - pageY)) / this.screen.width, // screen.width intentional\n    )\n\n    return this.onCircleVector\n  }\n\n  private axis = new Vector3()\n  private quaternion = new Quaternion()\n  private eyeDirection = new Vector3()\n  private objectUpDirection = new Vector3()\n  private objectSidewaysDirection = new Vector3()\n  private moveDirection = new Vector3()\n  private angle: number = 0\n\n  private rotateCamera = (): void => {\n    this.moveDirection.set(this._moveCurr.x - this._movePrev.x, this._moveCurr.y - this._movePrev.y, 0)\n    this.angle = this.moveDirection.length()\n\n    if (this.angle) {\n      this._eye.copy(this.object.position).sub(this.target)\n\n      this.eyeDirection.copy(this._eye).normalize()\n      this.objectUpDirection.copy(this.object.up).normalize()\n      this.objectSidewaysDirection.crossVectors(this.objectUpDirection, this.eyeDirection).normalize()\n\n      this.objectUpDirection.setLength(this._moveCurr.y - this._movePrev.y)\n      this.objectSidewaysDirection.setLength(this._moveCurr.x - this._movePrev.x)\n\n      this.moveDirection.copy(this.objectUpDirection.add(this.objectSidewaysDirection))\n\n      this.axis.crossVectors(this.moveDirection, this._eye).normalize()\n\n      this.angle *= this.rotateSpeed\n      this.quaternion.setFromAxisAngle(this.axis, this.angle)\n\n      this._eye.applyQuaternion(this.quaternion)\n      this.object.up.applyQuaternion(this.quaternion)\n\n      this._lastAxis.copy(this.axis)\n      this._lastAngle = this.angle\n    } else if (!this.staticMoving && this._lastAngle) {\n      this._lastAngle *= Math.sqrt(1.0 - this.dynamicDampingFactor)\n      this._eye.copy(this.object.position).sub(this.target)\n      this.quaternion.setFromAxisAngle(this._lastAxis, this._lastAngle)\n      this._eye.applyQuaternion(this.quaternion)\n      this.object.up.applyQuaternion(this.quaternion)\n    }\n\n    this._movePrev.copy(this._moveCurr)\n  }\n\n  private zoomCamera = (): void => {\n    let factor\n\n    if (this._state === this.STATE.TOUCH_ZOOM_PAN) {\n      factor = this._touchZoomDistanceStart / this._touchZoomDistanceEnd\n      this._touchZoomDistanceStart = this._touchZoomDistanceEnd\n\n      if ((this.object as PerspectiveCamera).isPerspectiveCamera) {\n        this._eye.multiplyScalar(factor)\n      } else if ((this.object as OrthographicCamera).isOrthographicCamera) {\n        this.object.zoom /= factor\n        this.object.updateProjectionMatrix()\n      } else {\n        console.warn('THREE.TrackballControls: Unsupported camera type')\n      }\n    } else {\n      factor = 1.0 + (this._zoomEnd.y - this._zoomStart.y) * this.zoomSpeed\n\n      if (Math.abs(factor - 1.0) > this.EPS && factor > 0.0) {\n        if ((this.object as PerspectiveCamera).isPerspectiveCamera) {\n          if (factor > 1.0 && this._eye.length() >= this.maxDistance - this.EPS) {\n            factor = 1.0\n          }\n          this._eye.multiplyScalar(factor)\n        } else if ((this.object as OrthographicCamera).isOrthographicCamera) {\n          if (factor > 1.0 && this.object.zoom < this.maxDistance * this.maxDistance) {\n            factor = 1.0\n          }\n          this.object.zoom /= factor\n        } else {\n          console.warn('THREE.TrackballControls: Unsupported camera type')\n        }\n      }\n\n      if (this.staticMoving) {\n        this._zoomStart.copy(this._zoomEnd)\n      } else {\n        this._zoomStart.y += (this._zoomEnd.y - this._zoomStart.y) * this.dynamicDampingFactor\n      }\n\n      if (this.cursorZoom) {\n        //determine 3D position of mouse cursor (on target plane)\n        this.targetVector.copy(this.target).project(this.object)\n        let worldPos = this.cursorVector\n          .set(this.mousePosition.x, this.mousePosition.y, this.targetVector.z)\n          .unproject(this.object)\n\n        //adjust target point so that \"point\" stays in place\n        this.target.lerpVectors(worldPos, this.target, factor)\n      }\n\n      // Update the projection matrix after all properties are changed\n      if ((this.object as OrthographicCamera).isOrthographicCamera) {\n        this.object.updateProjectionMatrix()\n      }\n    }\n  }\n\n  private mouseChange = new Vector2()\n  private objectUp = new Vector3()\n  private pan = new Vector3()\n\n  private panCamera = (): void => {\n    if (!this.domElement) return\n    this.mouseChange.copy(this._panEnd).sub(this._panStart)\n\n    if (this.mouseChange.lengthSq() > this.EPS) {\n      if ((this.object as OrthographicCamera).isOrthographicCamera) {\n        const orthoObject = this.object as OrthographicCamera\n        const scale_x = (orthoObject.right - orthoObject.left) / this.object.zoom\n        const scale_y = (orthoObject.top - orthoObject.bottom) / this.object.zoom\n\n        this.mouseChange.x *= scale_x\n        this.mouseChange.y *= scale_y\n      } else {\n        this.mouseChange.multiplyScalar(this._eye.length() * this.panSpeed)\n      }\n\n      this.pan.copy(this._eye).cross(this.object.up).setLength(this.mouseChange.x)\n      this.pan.add(this.objectUp.copy(this.object.up).setLength(this.mouseChange.y))\n\n      this.object.position.add(this.pan)\n      this.target.add(this.pan)\n\n      if (this.staticMoving) {\n        this._panStart.copy(this._panEnd)\n      } else {\n        this._panStart.add(\n          this.mouseChange.subVectors(this._panEnd, this._panStart).multiplyScalar(this.dynamicDampingFactor),\n        )\n      }\n    }\n  }\n\n  private checkDistances = (): void => {\n    if (!this.noZoom || !this.noPan) {\n      if (this._eye.lengthSq() > this.maxDistance * this.maxDistance) {\n        this.object.position.addVectors(this.target, this._eye.setLength(this.maxDistance))\n        this._zoomStart.copy(this._zoomEnd)\n      }\n\n      if (this._eye.lengthSq() < this.minDistance * this.minDistance) {\n        this.object.position.addVectors(this.target, this._eye.setLength(this.minDistance))\n        this._zoomStart.copy(this._zoomEnd)\n      }\n    }\n  }\n\n  public handleResize = (): void => {\n    if (!this.domElement) return\n    const box = this.domElement.getBoundingClientRect()\n    // adjustments come from similar code in the jquery offset() function\n    const d = this.domElement.ownerDocument.documentElement\n    this.screen.left = box.left + window.pageXOffset - d.clientLeft\n    this.screen.top = box.top + window.pageYOffset - d.clientTop\n    this.screen.width = box.width\n    this.screen.height = box.height\n  }\n\n  public update = (): void => {\n    this._eye.subVectors(this.object.position, this.target)\n\n    if (!this.noRotate) {\n      this.rotateCamera()\n    }\n\n    if (!this.noZoom) {\n      this.zoomCamera()\n    }\n\n    if (!this.noPan) {\n      this.panCamera()\n    }\n\n    this.object.position.addVectors(this.target, this._eye)\n\n    if ((this.object as PerspectiveCamera).isPerspectiveCamera) {\n      this.checkDistances()\n\n      this.object.lookAt(this.target)\n\n      if (this.lastPosition.distanceToSquared(this.object.position) > this.EPS) {\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n\n        this.lastPosition.copy(this.object.position)\n      }\n    } else if ((this.object as OrthographicCamera).isOrthographicCamera) {\n      this.object.lookAt(this.target)\n\n      if (this.lastPosition.distanceToSquared(this.object.position) > this.EPS || this.lastZoom !== this.object.zoom) {\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n\n        this.lastPosition.copy(this.object.position)\n        this.lastZoom = this.object.zoom\n      }\n    } else {\n      console.warn('THREE.TrackballControls: Unsupported camera type')\n    }\n  }\n\n  public reset = (): void => {\n    this._state = this.STATE.NONE\n    this._keyState = this.STATE.NONE\n\n    this.target.copy(this.target0)\n    this.object.position.copy(this.position0)\n    this.object.up.copy(this.up0)\n    this.object.zoom = this.zoom0\n\n    this.object.updateProjectionMatrix()\n\n    this._eye.subVectors(this.object.position, this.target)\n\n    this.object.lookAt(this.target)\n\n    // @ts-ignore\n    this.dispatchEvent(this.changeEvent)\n\n    this.lastPosition.copy(this.object.position)\n    this.lastZoom = this.object.zoom\n  }\n\n  private keydown = (event: KeyboardEvent): void => {\n    if (this.enabled === false) return\n\n    window.removeEventListener('keydown', this.keydown)\n\n    if (this._keyState !== this.STATE.NONE) {\n      return\n    } else if (event.code === this.keys[this.STATE.ROTATE] && !this.noRotate) {\n      this._keyState = this.STATE.ROTATE\n    } else if (event.code === this.keys[this.STATE.ZOOM] && !this.noZoom) {\n      this._keyState = this.STATE.ZOOM\n    } else if (event.code === this.keys[this.STATE.PAN] && !this.noPan) {\n      this._keyState = this.STATE.PAN\n    }\n  }\n\n  private onPointerDown = (event: PointerEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseDown(event)\n        break\n\n      // TODO touch\n    }\n  }\n\n  private onPointerMove = (event: PointerEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseMove(event)\n        break\n\n      // TODO touch\n    }\n  }\n\n  private onPointerUp = (event: PointerEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseUp()\n        break\n\n      // TODO touch\n    }\n  }\n\n  private keyup = (): void => {\n    if (this.enabled === false) return\n\n    this._keyState = this.STATE.NONE\n\n    window.addEventListener('keydown', this.keydown)\n  }\n\n  private onMouseDown = (event: MouseEvent): void => {\n    if (!this.domElement) return\n    if (this._state === this.STATE.NONE) {\n      switch (event.button) {\n        case this.mouseButtons.LEFT:\n          this._state = this.STATE.ROTATE\n          break\n\n        case this.mouseButtons.MIDDLE:\n          this._state = this.STATE.ZOOM\n          break\n\n        case this.mouseButtons.RIGHT:\n          this._state = this.STATE.PAN\n          break\n      }\n    }\n\n    const state = this._keyState !== this.STATE.NONE ? this._keyState : this._state\n\n    if (state === this.STATE.ROTATE && !this.noRotate) {\n      this._moveCurr.copy(this.getMouseOnCircle(event.pageX, event.pageY))\n      this._movePrev.copy(this._moveCurr)\n    } else if (state === this.STATE.ZOOM && !this.noZoom) {\n      this._zoomStart.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n      this._zoomEnd.copy(this._zoomStart)\n    } else if (state === this.STATE.PAN && !this.noPan) {\n      this._panStart.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n      this._panEnd.copy(this._panStart)\n    }\n\n    this.domElement.ownerDocument.addEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.addEventListener('pointerup', this.onPointerUp)\n\n    // @ts-ignore\n    this.dispatchEvent(this.startEvent)\n  }\n\n  private onMouseMove = (event: MouseEvent): void => {\n    if (this.enabled === false) return\n\n    const state = this._keyState !== this.STATE.NONE ? this._keyState : this._state\n\n    if (state === this.STATE.ROTATE && !this.noRotate) {\n      this._movePrev.copy(this._moveCurr)\n      this._moveCurr.copy(this.getMouseOnCircle(event.pageX, event.pageY))\n    } else if (state === this.STATE.ZOOM && !this.noZoom) {\n      this._zoomEnd.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n    } else if (state === this.STATE.PAN && !this.noPan) {\n      this._panEnd.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n    }\n  }\n\n  private onMouseUp = (): void => {\n    if (!this.domElement) return\n    if (this.enabled === false) return\n\n    this._state = this.STATE.NONE\n\n    this.domElement.ownerDocument.removeEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.removeEventListener('pointerup', this.onPointerUp)\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n  }\n\n  private mousewheel = (event: WheelEvent): void => {\n    if (this.enabled === false) return\n\n    if (this.noZoom === true) return\n\n    event.preventDefault()\n\n    switch (event.deltaMode) {\n      case 2:\n        // Zoom in pages\n        this._zoomStart.y -= event.deltaY * 0.025\n        break\n\n      case 1:\n        // Zoom in lines\n        this._zoomStart.y -= event.deltaY * 0.01\n        break\n\n      default:\n        // undefined, 0, assume pixels\n        this._zoomStart.y -= event.deltaY * 0.00025\n        break\n    }\n\n    this.mousePosition.x = (event.offsetX / this.screen.width) * 2 - 1\n    this.mousePosition.y = -(event.offsetY / this.screen.height) * 2 + 1\n\n    // @ts-ignore\n    this.dispatchEvent(this.startEvent)\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n  }\n\n  private touchstart = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (event.touches.length) {\n      case 1:\n        this._state = this.STATE.TOUCH_ROTATE\n        this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY))\n        this._movePrev.copy(this._moveCurr)\n        break\n\n      default:\n        // 2 or more\n        this._state = this.STATE.TOUCH_ZOOM_PAN\n        const dx = event.touches[0].pageX - event.touches[1].pageX\n        const dy = event.touches[0].pageY - event.touches[1].pageY\n        this._touchZoomDistanceEnd = this._touchZoomDistanceStart = Math.sqrt(dx * dx + dy * dy)\n\n        const x = (event.touches[0].pageX + event.touches[1].pageX) / 2\n        const y = (event.touches[0].pageY + event.touches[1].pageY) / 2\n        this._panStart.copy(this.getMouseOnScreen(x, y))\n        this._panEnd.copy(this._panStart)\n        break\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(this.startEvent)\n  }\n\n  private touchmove = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (event.touches.length) {\n      case 1:\n        this._movePrev.copy(this._moveCurr)\n        this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY))\n        break\n\n      default:\n        // 2 or more\n        const dx = event.touches[0].pageX - event.touches[1].pageX\n        const dy = event.touches[0].pageY - event.touches[1].pageY\n        this._touchZoomDistanceEnd = Math.sqrt(dx * dx + dy * dy)\n\n        const x = (event.touches[0].pageX + event.touches[1].pageX) / 2\n        const y = (event.touches[0].pageY + event.touches[1].pageY) / 2\n        this._panEnd.copy(this.getMouseOnScreen(x, y))\n        break\n    }\n  }\n\n  private touchend = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.touches.length) {\n      case 0:\n        this._state = this.STATE.NONE\n        break\n\n      case 1:\n        this._state = this.STATE.TOUCH_ROTATE\n        this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY))\n        this._movePrev.copy(this._moveCurr)\n        break\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n  }\n\n  private contextmenu = (event: MouseEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n  }\n\n  // https://github.com/mrdoob/three.js/issues/20575\n  public connect = (domElement: HTMLElement): void => {\n    if ((domElement as any) === document) {\n      console.error(\n        'THREE.OrbitControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.',\n      )\n    }\n    this.domElement = domElement\n    this.domElement.addEventListener('contextmenu', this.contextmenu)\n\n    this.domElement.addEventListener('pointerdown', this.onPointerDown)\n    this.domElement.addEventListener('wheel', this.mousewheel)\n\n    this.domElement.addEventListener('touchstart', this.touchstart)\n    this.domElement.addEventListener('touchend', this.touchend)\n    this.domElement.addEventListener('touchmove', this.touchmove)\n\n    this.domElement.ownerDocument.addEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.addEventListener('pointerup', this.onPointerUp)\n\n    window.addEventListener('keydown', this.keydown)\n    window.addEventListener('keyup', this.keyup)\n\n    this.handleResize()\n  }\n\n  public dispose = (): void => {\n    if (!this.domElement) return\n    this.domElement.removeEventListener('contextmenu', this.contextmenu)\n\n    this.domElement.removeEventListener('pointerdown', this.onPointerDown)\n    this.domElement.removeEventListener('wheel', this.mousewheel)\n\n    this.domElement.removeEventListener('touchstart', this.touchstart)\n    this.domElement.removeEventListener('touchend', this.touchend)\n    this.domElement.removeEventListener('touchmove', this.touchmove)\n\n    this.domElement.ownerDocument.removeEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.removeEventListener('pointerup', this.onPointerUp)\n\n    window.removeEventListener('keydown', this.keydown)\n    window.removeEventListener('keyup', this.keyup)\n  }\n}\n\nexport { TrackballControls }\n"], "names": [], "mappings": ";;;;;;;;AAIA,MAAM,0BAA0B,gBAA0C;AAAA,EA4ExE,YAAY,QAAgD,YAA0B;AAC9E;AA5ED,mCAAU;AAEV,kCAAS,EAAE,MAAM,GAAG,KAAK,GAAG,OAAO,GAAG,QAAQ;AAE9C,uCAAc;AACd,qCAAY;AACZ,oCAAW;AAEX,oCAAW;AACX,kCAAS;AACT,iCAAQ;AAER,wCAAe;AACf,gDAAuB;AAEvB,uCAAc;AACd,uCAAc;AAEd,gCAAiC;AAAA,MAAC;AAAA,MAAc;AAAA,MAAc;AAAA;AAAA,IAAA;AAE9D,wCAAe;AAAA,MACpB,MAAM,MAAM;AAAA,MACZ,QAAQ,MAAM;AAAA,MACd,OAAO,MAAM;AAAA,IAAA;AAGR;AACA;AACA,sCAAsB;AAEpB,kCAAS,IAAI;AACd,yCAAgB,IAAI;AAGpB;AAAA,iCAAQ;AAAA,MACd,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,KAAK;AAAA,MACL,cAAc;AAAA,MACd,gBAAgB;AAAA,IAAA;AAGV,+BAAM;AACN,oCAAW;AAEX,wCAAe,IAAI;AACnB,wCAAe,IAAI;AACnB,wCAAe,IAAI;AAEnB,kCAAS,KAAK,MAAM;AACpB,qCAAY,KAAK,MAAM;AACvB,gCAAO,IAAI;AACX,qCAAY,IAAI;AAChB,qCAAY,IAAI;AAChB,qCAAY,IAAI;AAChB,sCAAa;AACb,sCAAa,IAAI;AACjB,oCAAW,IAAI;AACf,mDAA0B;AAC1B,iDAAwB;AACxB,qCAAY,IAAI;AAChB,mCAAU,IAAI;AAEd;AACA;AACA;AACA;AAIA;AAAA,uCAAc,EAAE,MAAM;AACtB,sCAAa,EAAE,MAAM;AACrB,oCAAW,EAAE,MAAM;AAoBnB,0CAAiB,IAAI;AAErB,4CAAmB,CAAC,OAAe,UAA2B;AACpE,WAAK,eAAe;AAAA,SACjB,QAAQ,KAAK,OAAO,QAAQ,KAAK,OAAO;AAAA,SACxC,QAAQ,KAAK,OAAO,OAAO,KAAK,OAAO;AAAA,MAAA;AAG1C,aAAO,KAAK;AAAA,IAAA;AAGN,0CAAiB,IAAI;AAErB,4CAAmB,CAAC,OAAe,UAA2B;AACpE,WAAK,eAAe;AAAA,SACjB,QAAQ,KAAK,OAAO,QAAQ,MAAM,KAAK,OAAO,SAAS,KAAK,OAAO,QAAQ;AAAA,SAC3E,KAAK,OAAO,SAAS,KAAK,KAAK,OAAO,MAAM,UAAU,KAAK,OAAO;AAAA;AAAA,MAAA;AAGrE,aAAO,KAAK;AAAA,IAAA;AAGN,gCAAO,IAAI;AACX,sCAAa,IAAI;AACjB,wCAAe,IAAI;AACnB,6CAAoB,IAAI;AACxB,mDAA0B,IAAI;AAC9B,yCAAgB,IAAI;AACpB,iCAAgB;AAEhB,wCAAe,MAAY;AACjC,WAAK,cAAc,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU,GAAG,KAAK,UAAU,IAAI,KAAK,UAAU,GAAG,CAAC;AAC7F,WAAA,QAAQ,KAAK,cAAc,OAAO;AAEvC,UAAI,KAAK,OAAO;AACT,aAAA,KAAK,KAAK,KAAK,OAAO,QAAQ,EAAE,IAAI,KAAK,MAAM;AAEpD,aAAK,aAAa,KAAK,KAAK,IAAI,EAAE;AAClC,aAAK,kBAAkB,KAAK,KAAK,OAAO,EAAE,EAAE;AAC5C,aAAK,wBAAwB,aAAa,KAAK,mBAAmB,KAAK,YAAY,EAAE;AAErF,aAAK,kBAAkB,UAAU,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC;AACpE,aAAK,wBAAwB,UAAU,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC;AAE1E,aAAK,cAAc,KAAK,KAAK,kBAAkB,IAAI,KAAK,uBAAuB,CAAC;AAEhF,aAAK,KAAK,aAAa,KAAK,eAAe,KAAK,IAAI,EAAE;AAEtD,aAAK,SAAS,KAAK;AACnB,aAAK,WAAW,iBAAiB,KAAK,MAAM,KAAK,KAAK;AAEjD,aAAA,KAAK,gBAAgB,KAAK,UAAU;AACzC,aAAK,OAAO,GAAG,gBAAgB,KAAK,UAAU;AAEzC,aAAA,UAAU,KAAK,KAAK,IAAI;AAC7B,aAAK,aAAa,KAAK;AAAA,MACd,WAAA,CAAC,KAAK,gBAAgB,KAAK,YAAY;AAChD,aAAK,cAAc,KAAK,KAAK,IAAM,KAAK,oBAAoB;AACvD,aAAA,KAAK,KAAK,KAAK,OAAO,QAAQ,EAAE,IAAI,KAAK,MAAM;AACpD,aAAK,WAAW,iBAAiB,KAAK,WAAW,KAAK,UAAU;AAC3D,aAAA,KAAK,gBAAgB,KAAK,UAAU;AACzC,aAAK,OAAO,GAAG,gBAAgB,KAAK,UAAU;AAAA,MAChD;AAEK,WAAA,UAAU,KAAK,KAAK,SAAS;AAAA,IAAA;AAG5B,sCAAa,MAAY;AAC3B,UAAA;AAEJ,UAAI,KAAK,WAAW,KAAK,MAAM,gBAAgB;AACpC,iBAAA,KAAK,0BAA0B,KAAK;AAC7C,aAAK,0BAA0B,KAAK;AAE/B,YAAA,KAAK,OAA6B,qBAAqB;AACrD,eAAA,KAAK,eAAe,MAAM;AAAA,QAAA,WACrB,KAAK,OAA8B,sBAAsB;AACnE,eAAK,OAAO,QAAQ;AACpB,eAAK,OAAO;QAAuB,OAC9B;AACL,kBAAQ,KAAK,kDAAkD;AAAA,QACjE;AAAA,MAAA,OACK;AACL,iBAAS,KAAO,KAAK,SAAS,IAAI,KAAK,WAAW,KAAK,KAAK;AAExD,YAAA,KAAK,IAAI,SAAS,CAAG,IAAI,KAAK,OAAO,SAAS,GAAK;AAChD,cAAA,KAAK,OAA6B,qBAAqB;AACtD,gBAAA,SAAS,KAAO,KAAK,KAAK,OAAY,KAAA,KAAK,cAAc,KAAK,KAAK;AAC5D,uBAAA;AAAA,YACX;AACK,iBAAA,KAAK,eAAe,MAAM;AAAA,UAAA,WACrB,KAAK,OAA8B,sBAAsB;AAC/D,gBAAA,SAAS,KAAO,KAAK,OAAO,OAAO,KAAK,cAAc,KAAK,aAAa;AACjE,uBAAA;AAAA,YACX;AACA,iBAAK,OAAO,QAAQ;AAAA,UAAA,OACf;AACL,oBAAQ,KAAK,kDAAkD;AAAA,UACjE;AAAA,QACF;AAEA,YAAI,KAAK,cAAc;AAChB,eAAA,WAAW,KAAK,KAAK,QAAQ;AAAA,QAAA,OAC7B;AACA,eAAA,WAAW,MAAM,KAAK,SAAS,IAAI,KAAK,WAAW,KAAK,KAAK;AAAA,QACpE;AAEA,YAAI,KAAK,YAAY;AAEnB,eAAK,aAAa,KAAK,KAAK,MAAM,EAAE,QAAQ,KAAK,MAAM;AACvD,cAAI,WAAW,KAAK,aACjB,IAAI,KAAK,cAAc,GAAG,KAAK,cAAc,GAAG,KAAK,aAAa,CAAC,EACnE,UAAU,KAAK,MAAM;AAGxB,eAAK,OAAO,YAAY,UAAU,KAAK,QAAQ,MAAM;AAAA,QACvD;AAGK,YAAA,KAAK,OAA8B,sBAAsB;AAC5D,eAAK,OAAO;QACd;AAAA,MACF;AAAA,IAAA;AAGM,uCAAc,IAAI;AAClB,oCAAW,IAAI;AACf,+BAAM,IAAI;AAEV,qCAAY,MAAY;AAC9B,UAAI,CAAC,KAAK;AAAY;AACtB,WAAK,YAAY,KAAK,KAAK,OAAO,EAAE,IAAI,KAAK,SAAS;AAEtD,UAAI,KAAK,YAAY,SAAS,IAAI,KAAK,KAAK;AACrC,YAAA,KAAK,OAA8B,sBAAsB;AAC5D,gBAAM,cAAc,KAAK;AACzB,gBAAM,WAAW,YAAY,QAAQ,YAAY,QAAQ,KAAK,OAAO;AACrE,gBAAM,WAAW,YAAY,MAAM,YAAY,UAAU,KAAK,OAAO;AAErE,eAAK,YAAY,KAAK;AACtB,eAAK,YAAY,KAAK;AAAA,QAAA,OACjB;AACL,eAAK,YAAY,eAAe,KAAK,KAAK,OAAO,IAAI,KAAK,QAAQ;AAAA,QACpE;AAEA,aAAK,IAAI,KAAK,KAAK,IAAI,EAAE,MAAM,KAAK,OAAO,EAAE,EAAE,UAAU,KAAK,YAAY,CAAC;AAC3E,aAAK,IAAI,IAAI,KAAK,SAAS,KAAK,KAAK,OAAO,EAAE,EAAE,UAAU,KAAK,YAAY,CAAC,CAAC;AAE7E,aAAK,OAAO,SAAS,IAAI,KAAK,GAAG;AAC5B,aAAA,OAAO,IAAI,KAAK,GAAG;AAExB,YAAI,KAAK,cAAc;AAChB,eAAA,UAAU,KAAK,KAAK,OAAO;AAAA,QAAA,OAC3B;AACL,eAAK,UAAU;AAAA,YACb,KAAK,YAAY,WAAW,KAAK,SAAS,KAAK,SAAS,EAAE,eAAe,KAAK,oBAAoB;AAAA,UAAA;AAAA,QAEtG;AAAA,MACF;AAAA,IAAA;AAGM,0CAAiB,MAAY;AACnC,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAC/B,YAAI,KAAK,KAAK,SAAA,IAAa,KAAK,cAAc,KAAK,aAAa;AACzD,eAAA,OAAO,SAAS,WAAW,KAAK,QAAQ,KAAK,KAAK,UAAU,KAAK,WAAW,CAAC;AAC7E,eAAA,WAAW,KAAK,KAAK,QAAQ;AAAA,QACpC;AAEA,YAAI,KAAK,KAAK,SAAA,IAAa,KAAK,cAAc,KAAK,aAAa;AACzD,eAAA,OAAO,SAAS,WAAW,KAAK,QAAQ,KAAK,KAAK,UAAU,KAAK,WAAW,CAAC;AAC7E,eAAA,WAAW,KAAK,KAAK,QAAQ;AAAA,QACpC;AAAA,MACF;AAAA,IAAA;AAGK,wCAAe,MAAY;AAChC,UAAI,CAAC,KAAK;AAAY;AAChB,YAAA,MAAM,KAAK,WAAW,sBAAsB;AAE5C,YAAA,IAAI,KAAK,WAAW,cAAc;AACxC,WAAK,OAAO,OAAO,IAAI,OAAO,OAAO,cAAc,EAAE;AACrD,WAAK,OAAO,MAAM,IAAI,MAAM,OAAO,cAAc,EAAE;AAC9C,WAAA,OAAO,QAAQ,IAAI;AACnB,WAAA,OAAO,SAAS,IAAI;AAAA,IAAA;AAGpB,kCAAS,MAAY;AAC1B,WAAK,KAAK,WAAW,KAAK,OAAO,UAAU,KAAK,MAAM;AAElD,UAAA,CAAC,KAAK,UAAU;AAClB,aAAK,aAAa;AAAA,MACpB;AAEI,UAAA,CAAC,KAAK,QAAQ;AAChB,aAAK,WAAW;AAAA,MAClB;AAEI,UAAA,CAAC,KAAK,OAAO;AACf,aAAK,UAAU;AAAA,MACjB;AAEA,WAAK,OAAO,SAAS,WAAW,KAAK,QAAQ,KAAK,IAAI;AAEjD,UAAA,KAAK,OAA6B,qBAAqB;AAC1D,aAAK,eAAe;AAEf,aAAA,OAAO,OAAO,KAAK,MAAM;AAE1B,YAAA,KAAK,aAAa,kBAAkB,KAAK,OAAO,QAAQ,IAAI,KAAK,KAAK;AAEnE,eAAA,cAAc,KAAK,WAAW;AAEnC,eAAK,aAAa,KAAK,KAAK,OAAO,QAAQ;AAAA,QAC7C;AAAA,MAAA,WACU,KAAK,OAA8B,sBAAsB;AAC9D,aAAA,OAAO,OAAO,KAAK,MAAM;AAE9B,YAAI,KAAK,aAAa,kBAAkB,KAAK,OAAO,QAAQ,IAAI,KAAK,OAAO,KAAK,aAAa,KAAK,OAAO,MAAM;AAEzG,eAAA,cAAc,KAAK,WAAW;AAEnC,eAAK,aAAa,KAAK,KAAK,OAAO,QAAQ;AACtC,eAAA,WAAW,KAAK,OAAO;AAAA,QAC9B;AAAA,MAAA,OACK;AACL,gBAAQ,KAAK,kDAAkD;AAAA,MACjE;AAAA,IAAA;AAGK,iCAAQ,MAAY;AACpB,WAAA,SAAS,KAAK,MAAM;AACpB,WAAA,YAAY,KAAK,MAAM;AAEvB,WAAA,OAAO,KAAK,KAAK,OAAO;AAC7B,WAAK,OAAO,SAAS,KAAK,KAAK,SAAS;AACxC,WAAK,OAAO,GAAG,KAAK,KAAK,GAAG;AACvB,WAAA,OAAO,OAAO,KAAK;AAExB,WAAK,OAAO;AAEZ,WAAK,KAAK,WAAW,KAAK,OAAO,UAAU,KAAK,MAAM;AAEjD,WAAA,OAAO,OAAO,KAAK,MAAM;AAGzB,WAAA,cAAc,KAAK,WAAW;AAEnC,WAAK,aAAa,KAAK,KAAK,OAAO,QAAQ;AACtC,WAAA,WAAW,KAAK,OAAO;AAAA,IAAA;AAGtB,mCAAU,CAAC,UAA+B;AAChD,UAAI,KAAK,YAAY;AAAO;AAErB,aAAA,oBAAoB,WAAW,KAAK,OAAO;AAElD,UAAI,KAAK,cAAc,KAAK,MAAM,MAAM;AACtC;AAAA,MACF,WAAW,MAAM,SAAS,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,CAAC,KAAK,UAAU;AACnE,aAAA,YAAY,KAAK,MAAM;AAAA,MAC9B,WAAW,MAAM,SAAS,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,CAAC,KAAK,QAAQ;AAC/D,aAAA,YAAY,KAAK,MAAM;AAAA,MAC9B,WAAW,MAAM,SAAS,KAAK,KAAK,KAAK,MAAM,GAAG,KAAK,CAAC,KAAK,OAAO;AAC7D,aAAA,YAAY,KAAK,MAAM;AAAA,MAC9B;AAAA,IAAA;AAGM,yCAAgB,CAAC,UAA8B;AACrD,UAAI,KAAK,YAAY;AAAO;AAE5B,cAAQ,MAAM,aAAa;AAAA,QACzB,KAAK;AAAA,QACL,KAAK;AACH,eAAK,YAAY,KAAK;AACtB;AAAA,MAGJ;AAAA,IAAA;AAGM,yCAAgB,CAAC,UAA8B;AACrD,UAAI,KAAK,YAAY;AAAO;AAE5B,cAAQ,MAAM,aAAa;AAAA,QACzB,KAAK;AAAA,QACL,KAAK;AACH,eAAK,YAAY,KAAK;AACtB;AAAA,MAGJ;AAAA,IAAA;AAGM,uCAAc,CAAC,UAA8B;AACnD,UAAI,KAAK,YAAY;AAAO;AAE5B,cAAQ,MAAM,aAAa;AAAA,QACzB,KAAK;AAAA,QACL,KAAK;AACH,eAAK,UAAU;AACf;AAAA,MAGJ;AAAA,IAAA;AAGM,iCAAQ,MAAY;AAC1B,UAAI,KAAK,YAAY;AAAO;AAEvB,WAAA,YAAY,KAAK,MAAM;AAErB,aAAA,iBAAiB,WAAW,KAAK,OAAO;AAAA,IAAA;AAGzC,uCAAc,CAAC,UAA4B;AACjD,UAAI,CAAC,KAAK;AAAY;AACtB,UAAI,KAAK,WAAW,KAAK,MAAM,MAAM;AACnC,gBAAQ,MAAM,QAAQ;AAAA,UACpB,KAAK,KAAK,aAAa;AAChB,iBAAA,SAAS,KAAK,MAAM;AACzB;AAAA,UAEF,KAAK,KAAK,aAAa;AAChB,iBAAA,SAAS,KAAK,MAAM;AACzB;AAAA,UAEF,KAAK,KAAK,aAAa;AAChB,iBAAA,SAAS,KAAK,MAAM;AACzB;AAAA,QACJ;AAAA,MACF;AAEM,YAAA,QAAQ,KAAK,cAAc,KAAK,MAAM,OAAO,KAAK,YAAY,KAAK;AAEzE,UAAI,UAAU,KAAK,MAAM,UAAU,CAAC,KAAK,UAAU;AAC5C,aAAA,UAAU,KAAK,KAAK,iBAAiB,MAAM,OAAO,MAAM,KAAK,CAAC;AAC9D,aAAA,UAAU,KAAK,KAAK,SAAS;AAAA,MAAA,WACzB,UAAU,KAAK,MAAM,QAAQ,CAAC,KAAK,QAAQ;AAC/C,aAAA,WAAW,KAAK,KAAK,iBAAiB,MAAM,OAAO,MAAM,KAAK,CAAC;AAC/D,aAAA,SAAS,KAAK,KAAK,UAAU;AAAA,MAAA,WACzB,UAAU,KAAK,MAAM,OAAO,CAAC,KAAK,OAAO;AAC7C,aAAA,UAAU,KAAK,KAAK,iBAAiB,MAAM,OAAO,MAAM,KAAK,CAAC;AAC9D,aAAA,QAAQ,KAAK,KAAK,SAAS;AAAA,MAClC;AAEA,WAAK,WAAW,cAAc,iBAAiB,eAAe,KAAK,aAAa;AAChF,WAAK,WAAW,cAAc,iBAAiB,aAAa,KAAK,WAAW;AAGvE,WAAA,cAAc,KAAK,UAAU;AAAA,IAAA;AAG5B,uCAAc,CAAC,UAA4B;AACjD,UAAI,KAAK,YAAY;AAAO;AAEtB,YAAA,QAAQ,KAAK,cAAc,KAAK,MAAM,OAAO,KAAK,YAAY,KAAK;AAEzE,UAAI,UAAU,KAAK,MAAM,UAAU,CAAC,KAAK,UAAU;AAC5C,aAAA,UAAU,KAAK,KAAK,SAAS;AAC7B,aAAA,UAAU,KAAK,KAAK,iBAAiB,MAAM,OAAO,MAAM,KAAK,CAAC;AAAA,MAAA,WAC1D,UAAU,KAAK,MAAM,QAAQ,CAAC,KAAK,QAAQ;AAC/C,aAAA,SAAS,KAAK,KAAK,iBAAiB,MAAM,OAAO,MAAM,KAAK,CAAC;AAAA,MAAA,WACzD,UAAU,KAAK,MAAM,OAAO,CAAC,KAAK,OAAO;AAC7C,aAAA,QAAQ,KAAK,KAAK,iBAAiB,MAAM,OAAO,MAAM,KAAK,CAAC;AAAA,MACnE;AAAA,IAAA;AAGM,qCAAY,MAAY;AAC9B,UAAI,CAAC,KAAK;AAAY;AACtB,UAAI,KAAK,YAAY;AAAO;AAEvB,WAAA,SAAS,KAAK,MAAM;AAEzB,WAAK,WAAW,cAAc,oBAAoB,eAAe,KAAK,aAAa;AACnF,WAAK,WAAW,cAAc,oBAAoB,aAAa,KAAK,WAAW;AAG1E,WAAA,cAAc,KAAK,QAAQ;AAAA,IAAA;AAG1B,sCAAa,CAAC,UAA4B;AAChD,UAAI,KAAK,YAAY;AAAO;AAE5B,UAAI,KAAK,WAAW;AAAM;AAE1B,YAAM,eAAe;AAErB,cAAQ,MAAM,WAAW;AAAA,QACvB,KAAK;AAEE,eAAA,WAAW,KAAK,MAAM,SAAS;AACpC;AAAA,QAEF,KAAK;AAEE,eAAA,WAAW,KAAK,MAAM,SAAS;AACpC;AAAA,QAEF;AAEO,eAAA,WAAW,KAAK,MAAM,SAAS;AACpC;AAAA,MACJ;AAEA,WAAK,cAAc,IAAK,MAAM,UAAU,KAAK,OAAO,QAAS,IAAI;AAC5D,WAAA,cAAc,IAAI,EAAE,MAAM,UAAU,KAAK,OAAO,UAAU,IAAI;AAG9D,WAAA,cAAc,KAAK,UAAU;AAE7B,WAAA,cAAc,KAAK,QAAQ;AAAA,IAAA;AAG1B,sCAAa,CAAC,UAA4B;AAChD,UAAI,KAAK,YAAY;AAAO;AAE5B,YAAM,eAAe;AAEb,cAAA,MAAM,QAAQ,QAAQ;AAAA,QAC5B,KAAK;AACE,eAAA,SAAS,KAAK,MAAM;AACzB,eAAK,UAAU,KAAK,KAAK,iBAAiB,MAAM,QAAQ,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC,EAAE,KAAK,CAAC;AACpF,eAAA,UAAU,KAAK,KAAK,SAAS;AAClC;AAAA,QAEF;AAEO,eAAA,SAAS,KAAK,MAAM;AACnB,gBAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AAC/C,gBAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AAChD,eAAA,wBAAwB,KAAK,0BAA0B,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAEjF,gBAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE,SAAS;AACxD,gBAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE,SAAS;AAC9D,eAAK,UAAU,KAAK,KAAK,iBAAiB,GAAG,CAAC,CAAC;AAC1C,eAAA,QAAQ,KAAK,KAAK,SAAS;AAChC;AAAA,MACJ;AAGK,WAAA,cAAc,KAAK,UAAU;AAAA,IAAA;AAG5B,qCAAY,CAAC,UAA4B;AAC/C,UAAI,KAAK,YAAY;AAAO;AAE5B,YAAM,eAAe;AAEb,cAAA,MAAM,QAAQ,QAAQ;AAAA,QAC5B,KAAK;AACE,eAAA,UAAU,KAAK,KAAK,SAAS;AAClC,eAAK,UAAU,KAAK,KAAK,iBAAiB,MAAM,QAAQ,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC,EAAE,KAAK,CAAC;AACzF;AAAA,QAEF;AAEQ,gBAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AAC/C,gBAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AACrD,eAAK,wBAAwB,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAElD,gBAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE,SAAS;AACxD,gBAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE,SAAS;AAC9D,eAAK,QAAQ,KAAK,KAAK,iBAAiB,GAAG,CAAC,CAAC;AAC7C;AAAA,MACJ;AAAA,IAAA;AAGM,oCAAW,CAAC,UAA4B;AAC9C,UAAI,KAAK,YAAY;AAAO;AAEpB,cAAA,MAAM,QAAQ,QAAQ;AAAA,QAC5B,KAAK;AACE,eAAA,SAAS,KAAK,MAAM;AACzB;AAAA,QAEF,KAAK;AACE,eAAA,SAAS,KAAK,MAAM;AACzB,eAAK,UAAU,KAAK,KAAK,iBAAiB,MAAM,QAAQ,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC,EAAE,KAAK,CAAC;AACpF,eAAA,UAAU,KAAK,KAAK,SAAS;AAClC;AAAA,MACJ;AAGK,WAAA,cAAc,KAAK,QAAQ;AAAA,IAAA;AAG1B,uCAAc,CAAC,UAA4B;AACjD,UAAI,KAAK,YAAY;AAAO;AAE5B,YAAM,eAAe;AAAA,IAAA;AAIhB;AAAA,mCAAU,CAAC,eAAkC;AAClD,UAAK,eAAuB,UAAU;AAC5B,gBAAA;AAAA,UACN;AAAA,QAAA;AAAA,MAEJ;AACA,WAAK,aAAa;AAClB,WAAK,WAAW,iBAAiB,eAAe,KAAK,WAAW;AAEhE,WAAK,WAAW,iBAAiB,eAAe,KAAK,aAAa;AAClE,WAAK,WAAW,iBAAiB,SAAS,KAAK,UAAU;AAEzD,WAAK,WAAW,iBAAiB,cAAc,KAAK,UAAU;AAC9D,WAAK,WAAW,iBAAiB,YAAY,KAAK,QAAQ;AAC1D,WAAK,WAAW,iBAAiB,aAAa,KAAK,SAAS;AAE5D,WAAK,WAAW,cAAc,iBAAiB,eAAe,KAAK,aAAa;AAChF,WAAK,WAAW,cAAc,iBAAiB,aAAa,KAAK,WAAW;AAErE,aAAA,iBAAiB,WAAW,KAAK,OAAO;AACxC,aAAA,iBAAiB,SAAS,KAAK,KAAK;AAE3C,WAAK,aAAa;AAAA,IAAA;AAGb,mCAAU,MAAY;AAC3B,UAAI,CAAC,KAAK;AAAY;AACtB,WAAK,WAAW,oBAAoB,eAAe,KAAK,WAAW;AAEnE,WAAK,WAAW,oBAAoB,eAAe,KAAK,aAAa;AACrE,WAAK,WAAW,oBAAoB,SAAS,KAAK,UAAU;AAE5D,WAAK,WAAW,oBAAoB,cAAc,KAAK,UAAU;AACjE,WAAK,WAAW,oBAAoB,YAAY,KAAK,QAAQ;AAC7D,WAAK,WAAW,oBAAoB,aAAa,KAAK,SAAS;AAE/D,WAAK,WAAW,cAAc,oBAAoB,eAAe,KAAK,aAAa;AACnF,WAAK,WAAW,cAAc,oBAAoB,aAAa,KAAK,WAAW;AAExE,aAAA,oBAAoB,WAAW,KAAK,OAAO;AAC3C,aAAA,oBAAoB,SAAS,KAAK,KAAK;AAAA,IAAA;AAriB9C,SAAK,SAAS;AAIT,SAAA,UAAU,KAAK,OAAO,MAAM;AACjC,SAAK,YAAY,KAAK,OAAO,SAAS,MAAM;AAC5C,SAAK,MAAM,KAAK,OAAO,GAAG,MAAM;AAC3B,SAAA,QAAQ,KAAK,OAAO;AAGzB,QAAI,eAAe;AAAW,WAAK,QAAQ,UAAU;AAGrD,SAAK,OAAO;AAAA,EACd;AAyhBF;"}