{"version": 3, "file": "OBJLoader.js", "sources": ["../../src/loaders/OBJLoader.js"], "sourcesContent": ["import {\n  BufferGeometry,\n  FileLoader,\n  Float32BufferAttribute,\n  Group,\n  LineBasicMaterial,\n  LineSegments,\n  Loader,\n  Material,\n  Mesh,\n  MeshPhongMaterial,\n  Points,\n  PointsMaterial,\n  Vector3,\n} from 'three'\n\n// o object_name | g group_name\nconst _object_pattern = /^[og]\\s*(.+)?/\n// mtllib file_reference\nconst _material_library_pattern = /^mtllib /\n// usemtl material_name\nconst _material_use_pattern = /^usemtl /\n// usemap map_name\nconst _map_use_pattern = /^usemap /\n\nconst _vA = /* @__PURE__ */ new Vector3()\nconst _vB = /* @__PURE__ */ new Vector3()\nconst _vC = /* @__PURE__ */ new Vector3()\n\nconst _ab = /* @__PURE__ */ new Vector3()\nconst _cb = /* @__PURE__ */ new Vector3()\n\nfunction ParserState() {\n  const state = {\n    objects: [],\n    object: {},\n\n    vertices: [],\n    normals: [],\n    colors: [],\n    uvs: [],\n\n    materials: {},\n    materialLibraries: [],\n\n    startObject: function (name, fromDeclaration) {\n      // If the current object (initial from reset) is not from a g/o declaration in the parsed\n      // file. We need to use it for the first parsed g/o to keep things in sync.\n      if (this.object && this.object.fromDeclaration === false) {\n        this.object.name = name\n        this.object.fromDeclaration = fromDeclaration !== false\n        return\n      }\n\n      const previousMaterial =\n        this.object && typeof this.object.currentMaterial === 'function' ? this.object.currentMaterial() : undefined\n\n      if (this.object && typeof this.object._finalize === 'function') {\n        this.object._finalize(true)\n      }\n\n      this.object = {\n        name: name || '',\n        fromDeclaration: fromDeclaration !== false,\n\n        geometry: {\n          vertices: [],\n          normals: [],\n          colors: [],\n          uvs: [],\n          hasUVIndices: false,\n        },\n        materials: [],\n        smooth: true,\n\n        startMaterial: function (name, libraries) {\n          const previous = this._finalize(false)\n\n          // New usemtl declaration overwrites an inherited material, except if faces were declared\n          // after the material, then it must be preserved for proper MultiMaterial continuation.\n          if (previous && (previous.inherited || previous.groupCount <= 0)) {\n            this.materials.splice(previous.index, 1)\n          }\n\n          const material = {\n            index: this.materials.length,\n            name: name || '',\n            mtllib: Array.isArray(libraries) && libraries.length > 0 ? libraries[libraries.length - 1] : '',\n            smooth: previous !== undefined ? previous.smooth : this.smooth,\n            groupStart: previous !== undefined ? previous.groupEnd : 0,\n            groupEnd: -1,\n            groupCount: -1,\n            inherited: false,\n\n            clone: function (index) {\n              const cloned = {\n                index: typeof index === 'number' ? index : this.index,\n                name: this.name,\n                mtllib: this.mtllib,\n                smooth: this.smooth,\n                groupStart: 0,\n                groupEnd: -1,\n                groupCount: -1,\n                inherited: false,\n              }\n              cloned.clone = this.clone.bind(cloned)\n              return cloned\n            },\n          }\n\n          this.materials.push(material)\n\n          return material\n        },\n\n        currentMaterial: function () {\n          if (this.materials.length > 0) {\n            return this.materials[this.materials.length - 1]\n          }\n\n          return undefined\n        },\n\n        _finalize: function (end) {\n          const lastMultiMaterial = this.currentMaterial()\n          if (lastMultiMaterial && lastMultiMaterial.groupEnd === -1) {\n            lastMultiMaterial.groupEnd = this.geometry.vertices.length / 3\n            lastMultiMaterial.groupCount = lastMultiMaterial.groupEnd - lastMultiMaterial.groupStart\n            lastMultiMaterial.inherited = false\n          }\n\n          // Ignore objects tail materials if no face declarations followed them before a new o/g started.\n          if (end && this.materials.length > 1) {\n            for (let mi = this.materials.length - 1; mi >= 0; mi--) {\n              if (this.materials[mi].groupCount <= 0) {\n                this.materials.splice(mi, 1)\n              }\n            }\n          }\n\n          // Guarantee at least one empty material, this makes the creation later more straight forward.\n          if (end && this.materials.length === 0) {\n            this.materials.push({\n              name: '',\n              smooth: this.smooth,\n            })\n          }\n\n          return lastMultiMaterial\n        },\n      }\n\n      // Inherit previous objects material.\n      // Spec tells us that a declared material must be set to all objects until a new material is declared.\n      // If a usemtl declaration is encountered while this new object is being parsed, it will\n      // overwrite the inherited material. Exception being that there was already face declarations\n      // to the inherited material, then it will be preserved for proper MultiMaterial continuation.\n\n      if (previousMaterial && previousMaterial.name && typeof previousMaterial.clone === 'function') {\n        const declared = previousMaterial.clone(0)\n        declared.inherited = true\n        this.object.materials.push(declared)\n      }\n\n      this.objects.push(this.object)\n    },\n\n    finalize: function () {\n      if (this.object && typeof this.object._finalize === 'function') {\n        this.object._finalize(true)\n      }\n    },\n\n    parseVertexIndex: function (value, len) {\n      const index = parseInt(value, 10)\n      return (index >= 0 ? index - 1 : index + len / 3) * 3\n    },\n\n    parseNormalIndex: function (value, len) {\n      const index = parseInt(value, 10)\n      return (index >= 0 ? index - 1 : index + len / 3) * 3\n    },\n\n    parseUVIndex: function (value, len) {\n      const index = parseInt(value, 10)\n      return (index >= 0 ? index - 1 : index + len / 2) * 2\n    },\n\n    addVertex: function (a, b, c) {\n      const src = this.vertices\n      const dst = this.object.geometry.vertices\n\n      dst.push(src[a + 0], src[a + 1], src[a + 2])\n      dst.push(src[b + 0], src[b + 1], src[b + 2])\n      dst.push(src[c + 0], src[c + 1], src[c + 2])\n    },\n\n    addVertexPoint: function (a) {\n      const src = this.vertices\n      const dst = this.object.geometry.vertices\n\n      dst.push(src[a + 0], src[a + 1], src[a + 2])\n    },\n\n    addVertexLine: function (a) {\n      const src = this.vertices\n      const dst = this.object.geometry.vertices\n\n      dst.push(src[a + 0], src[a + 1], src[a + 2])\n    },\n\n    addNormal: function (a, b, c) {\n      const src = this.normals\n      const dst = this.object.geometry.normals\n\n      dst.push(src[a + 0], src[a + 1], src[a + 2])\n      dst.push(src[b + 0], src[b + 1], src[b + 2])\n      dst.push(src[c + 0], src[c + 1], src[c + 2])\n    },\n\n    addFaceNormal: function (a, b, c) {\n      const src = this.vertices\n      const dst = this.object.geometry.normals\n\n      _vA.fromArray(src, a)\n      _vB.fromArray(src, b)\n      _vC.fromArray(src, c)\n\n      _cb.subVectors(_vC, _vB)\n      _ab.subVectors(_vA, _vB)\n      _cb.cross(_ab)\n\n      _cb.normalize()\n\n      dst.push(_cb.x, _cb.y, _cb.z)\n      dst.push(_cb.x, _cb.y, _cb.z)\n      dst.push(_cb.x, _cb.y, _cb.z)\n    },\n\n    addColor: function (a, b, c) {\n      const src = this.colors\n      const dst = this.object.geometry.colors\n\n      if (src[a] !== undefined) dst.push(src[a + 0], src[a + 1], src[a + 2])\n      if (src[b] !== undefined) dst.push(src[b + 0], src[b + 1], src[b + 2])\n      if (src[c] !== undefined) dst.push(src[c + 0], src[c + 1], src[c + 2])\n    },\n\n    addUV: function (a, b, c) {\n      const src = this.uvs\n      const dst = this.object.geometry.uvs\n\n      dst.push(src[a + 0], src[a + 1])\n      dst.push(src[b + 0], src[b + 1])\n      dst.push(src[c + 0], src[c + 1])\n    },\n\n    addDefaultUV: function () {\n      const dst = this.object.geometry.uvs\n\n      dst.push(0, 0)\n      dst.push(0, 0)\n      dst.push(0, 0)\n    },\n\n    addUVLine: function (a) {\n      const src = this.uvs\n      const dst = this.object.geometry.uvs\n\n      dst.push(src[a + 0], src[a + 1])\n    },\n\n    addFace: function (a, b, c, ua, ub, uc, na, nb, nc) {\n      const vLen = this.vertices.length\n\n      let ia = this.parseVertexIndex(a, vLen)\n      let ib = this.parseVertexIndex(b, vLen)\n      let ic = this.parseVertexIndex(c, vLen)\n\n      this.addVertex(ia, ib, ic)\n      this.addColor(ia, ib, ic)\n\n      // normals\n\n      if (na !== undefined && na !== '') {\n        const nLen = this.normals.length\n\n        ia = this.parseNormalIndex(na, nLen)\n        ib = this.parseNormalIndex(nb, nLen)\n        ic = this.parseNormalIndex(nc, nLen)\n\n        this.addNormal(ia, ib, ic)\n      } else {\n        this.addFaceNormal(ia, ib, ic)\n      }\n\n      // uvs\n\n      if (ua !== undefined && ua !== '') {\n        const uvLen = this.uvs.length\n\n        ia = this.parseUVIndex(ua, uvLen)\n        ib = this.parseUVIndex(ub, uvLen)\n        ic = this.parseUVIndex(uc, uvLen)\n\n        this.addUV(ia, ib, ic)\n\n        this.object.geometry.hasUVIndices = true\n      } else {\n        // add placeholder values (for inconsistent face definitions)\n\n        this.addDefaultUV()\n      }\n    },\n\n    addPointGeometry: function (vertices) {\n      this.object.geometry.type = 'Points'\n\n      const vLen = this.vertices.length\n\n      for (let vi = 0, l = vertices.length; vi < l; vi++) {\n        const index = this.parseVertexIndex(vertices[vi], vLen)\n\n        this.addVertexPoint(index)\n        this.addColor(index)\n      }\n    },\n\n    addLineGeometry: function (vertices, uvs) {\n      this.object.geometry.type = 'Line'\n\n      const vLen = this.vertices.length\n      const uvLen = this.uvs.length\n\n      for (let vi = 0, l = vertices.length; vi < l; vi++) {\n        this.addVertexLine(this.parseVertexIndex(vertices[vi], vLen))\n      }\n\n      for (let uvi = 0, l = uvs.length; uvi < l; uvi++) {\n        this.addUVLine(this.parseUVIndex(uvs[uvi], uvLen))\n      }\n    },\n  }\n\n  state.startObject('', false)\n\n  return state\n}\n\n//\n\nclass OBJLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.materials = null\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  setMaterials(materials) {\n    this.materials = materials\n\n    return this\n  }\n\n  parse(text) {\n    const state = new ParserState()\n\n    if (text.indexOf('\\r\\n') !== -1) {\n      // This is faster than String.split with regex that splits on both\n      text = text.replace(/\\r\\n/g, '\\n')\n    }\n\n    if (text.indexOf('\\\\\\n') !== -1) {\n      // join lines separated by a line continuation character (\\)\n      text = text.replace(/\\\\\\n/g, '')\n    }\n\n    const lines = text.split('\\n')\n    let line = '',\n      lineFirstChar = ''\n    let lineLength = 0\n    let result = []\n\n    // Faster to just trim left side of the line. Use if available.\n    const trimLeft = typeof ''.trimLeft === 'function'\n\n    for (let i = 0, l = lines.length; i < l; i++) {\n      line = lines[i]\n\n      line = trimLeft ? line.trimLeft() : line.trim()\n\n      lineLength = line.length\n\n      if (lineLength === 0) continue\n\n      lineFirstChar = line.charAt(0)\n\n      // @todo invoke passed in handler if any\n      if (lineFirstChar === '#') continue\n\n      if (lineFirstChar === 'v') {\n        const data = line.split(/\\s+/)\n\n        switch (data[0]) {\n          case 'v':\n            state.vertices.push(parseFloat(data[1]), parseFloat(data[2]), parseFloat(data[3]))\n            if (data.length >= 7) {\n              state.colors.push(parseFloat(data[4]), parseFloat(data[5]), parseFloat(data[6]))\n            } else {\n              // if no colors are defined, add placeholders so color and vertex indices match\n\n              state.colors.push(undefined, undefined, undefined)\n            }\n\n            break\n          case 'vn':\n            state.normals.push(parseFloat(data[1]), parseFloat(data[2]), parseFloat(data[3]))\n            break\n          case 'vt':\n            state.uvs.push(parseFloat(data[1]), parseFloat(data[2]))\n            break\n        }\n      } else if (lineFirstChar === 'f') {\n        const lineData = line.substr(1).trim()\n        const vertexData = lineData.split(/\\s+/)\n        const faceVertices = []\n\n        // Parse the face vertex data into an easy to work with format\n\n        for (let j = 0, jl = vertexData.length; j < jl; j++) {\n          const vertex = vertexData[j]\n\n          if (vertex.length > 0) {\n            const vertexParts = vertex.split('/')\n            faceVertices.push(vertexParts)\n          }\n        }\n\n        // Draw an edge between the first vertex and all subsequent vertices to form an n-gon\n\n        const v1 = faceVertices[0]\n\n        for (let j = 1, jl = faceVertices.length - 1; j < jl; j++) {\n          const v2 = faceVertices[j]\n          const v3 = faceVertices[j + 1]\n\n          state.addFace(v1[0], v2[0], v3[0], v1[1], v2[1], v3[1], v1[2], v2[2], v3[2])\n        }\n      } else if (lineFirstChar === 'l') {\n        const lineParts = line.substring(1).trim().split(' ')\n        let lineVertices = []\n        const lineUVs = []\n\n        if (line.indexOf('/') === -1) {\n          lineVertices = lineParts\n        } else {\n          for (let li = 0, llen = lineParts.length; li < llen; li++) {\n            const parts = lineParts[li].split('/')\n\n            if (parts[0] !== '') lineVertices.push(parts[0])\n            if (parts[1] !== '') lineUVs.push(parts[1])\n          }\n        }\n\n        state.addLineGeometry(lineVertices, lineUVs)\n      } else if (lineFirstChar === 'p') {\n        const lineData = line.substr(1).trim()\n        const pointData = lineData.split(' ')\n\n        state.addPointGeometry(pointData)\n      } else if ((result = _object_pattern.exec(line)) !== null) {\n        // o object_name\n        // or\n        // g group_name\n\n        // WORKAROUND: https://bugs.chromium.org/p/v8/issues/detail?id=2869\n        // let name = result[ 0 ].substr( 1 ).trim();\n        const name = (' ' + result[0].substr(1).trim()).substr(1)\n\n        state.startObject(name)\n      } else if (_material_use_pattern.test(line)) {\n        // material\n\n        state.object.startMaterial(line.substring(7).trim(), state.materialLibraries)\n      } else if (_material_library_pattern.test(line)) {\n        // mtl file\n\n        state.materialLibraries.push(line.substring(7).trim())\n      } else if (_map_use_pattern.test(line)) {\n        // the line is parsed but ignored since the loader assumes textures are defined MTL files\n        // (according to https://www.okino.com/conv/imp_wave.htm, 'usemap' is the old-style Wavefront texture reference method)\n\n        console.warn(\n          'THREE.OBJLoader: Rendering identifier \"usemap\" not supported. Textures must be defined in MTL files.',\n        )\n      } else if (lineFirstChar === 's') {\n        result = line.split(' ')\n\n        // smooth shading\n\n        // @todo Handle files that have varying smooth values for a set of faces inside one geometry,\n        // but does not define a usemtl for each face set.\n        // This should be detected and a dummy material created (later MultiMaterial and geometry groups).\n        // This requires some care to not create extra material on each smooth value for \"normal\" obj files.\n        // where explicit usemtl defines geometry groups.\n        // Example asset: examples/models/obj/cerberus/Cerberus.obj\n\n        /*\n         * http://paulbourke.net/dataformats/obj/\n         * or\n         * http://www.cs.utah.edu/~boulos/cs3505/obj_spec.pdf\n         *\n         * From chapter \"Grouping\" Syntax explanation \"s group_number\":\n         * \"group_number is the smoothing group number. To turn off smoothing groups, use a value of 0 or off.\n         * Polygonal elements use group numbers to put elements in different smoothing groups. For free-form\n         * surfaces, smoothing groups are either turned on or off; there is no difference between values greater\n         * than 0.\"\n         */\n        if (result.length > 1) {\n          const value = result[1].trim().toLowerCase()\n          state.object.smooth = value !== '0' && value !== 'off'\n        } else {\n          // ZBrush can produce \"s\" lines #11707\n          state.object.smooth = true\n        }\n\n        const material = state.object.currentMaterial()\n        if (material) material.smooth = state.object.smooth\n      } else {\n        // Handle null terminated files without exception\n        if (line === '\\0') continue\n\n        console.warn('THREE.OBJLoader: Unexpected line: \"' + line + '\"')\n      }\n    }\n\n    state.finalize()\n\n    const container = new Group()\n    container.materialLibraries = [].concat(state.materialLibraries)\n\n    const hasPrimitives = !(state.objects.length === 1 && state.objects[0].geometry.vertices.length === 0)\n\n    if (hasPrimitives === true) {\n      for (let i = 0, l = state.objects.length; i < l; i++) {\n        const object = state.objects[i]\n        const geometry = object.geometry\n        const materials = object.materials\n        const isLine = geometry.type === 'Line'\n        const isPoints = geometry.type === 'Points'\n        let hasVertexColors = false\n\n        // Skip o/g line declarations that did not follow with any faces\n        if (geometry.vertices.length === 0) continue\n\n        const buffergeometry = new BufferGeometry()\n\n        buffergeometry.setAttribute('position', new Float32BufferAttribute(geometry.vertices, 3))\n\n        if (geometry.normals.length > 0) {\n          buffergeometry.setAttribute('normal', new Float32BufferAttribute(geometry.normals, 3))\n        }\n\n        if (geometry.colors.length > 0) {\n          hasVertexColors = true\n          buffergeometry.setAttribute('color', new Float32BufferAttribute(geometry.colors, 3))\n        }\n\n        if (geometry.hasUVIndices === true) {\n          buffergeometry.setAttribute('uv', new Float32BufferAttribute(geometry.uvs, 2))\n        }\n\n        // Create materials\n\n        const createdMaterials = []\n\n        for (let mi = 0, miLen = materials.length; mi < miLen; mi++) {\n          const sourceMaterial = materials[mi]\n          const materialHash = sourceMaterial.name + '_' + sourceMaterial.smooth + '_' + hasVertexColors\n          let material = state.materials[materialHash]\n\n          if (this.materials !== null) {\n            material = this.materials.create(sourceMaterial.name)\n\n            // mtl etc. loaders probably can't create line materials correctly, copy properties to a line material.\n            if (isLine && material && !(material instanceof LineBasicMaterial)) {\n              const materialLine = new LineBasicMaterial()\n              Material.prototype.copy.call(materialLine, material)\n              materialLine.color.copy(material.color)\n              material = materialLine\n            } else if (isPoints && material && !(material instanceof PointsMaterial)) {\n              const materialPoints = new PointsMaterial({ size: 10, sizeAttenuation: false })\n              Material.prototype.copy.call(materialPoints, material)\n              materialPoints.color.copy(material.color)\n              materialPoints.map = material.map\n              material = materialPoints\n            }\n          }\n\n          if (material === undefined) {\n            if (isLine) {\n              material = new LineBasicMaterial()\n            } else if (isPoints) {\n              material = new PointsMaterial({ size: 1, sizeAttenuation: false })\n            } else {\n              material = new MeshPhongMaterial()\n            }\n\n            material.name = sourceMaterial.name\n            material.flatShading = sourceMaterial.smooth ? false : true\n            material.vertexColors = hasVertexColors\n\n            state.materials[materialHash] = material\n          }\n\n          createdMaterials.push(material)\n        }\n\n        // Create mesh\n\n        let mesh\n\n        if (createdMaterials.length > 1) {\n          for (let mi = 0, miLen = materials.length; mi < miLen; mi++) {\n            const sourceMaterial = materials[mi]\n            buffergeometry.addGroup(sourceMaterial.groupStart, sourceMaterial.groupCount, mi)\n          }\n\n          if (isLine) {\n            mesh = new LineSegments(buffergeometry, createdMaterials)\n          } else if (isPoints) {\n            mesh = new Points(buffergeometry, createdMaterials)\n          } else {\n            mesh = new Mesh(buffergeometry, createdMaterials)\n          }\n        } else {\n          if (isLine) {\n            mesh = new LineSegments(buffergeometry, createdMaterials[0])\n          } else if (isPoints) {\n            mesh = new Points(buffergeometry, createdMaterials[0])\n          } else {\n            mesh = new Mesh(buffergeometry, createdMaterials[0])\n          }\n        }\n\n        mesh.name = object.name\n\n        container.add(mesh)\n      }\n    } else {\n      // if there is only the default parser state object with no geometry data, interpret data as point cloud\n\n      if (state.vertices.length > 0) {\n        const material = new PointsMaterial({ size: 1, sizeAttenuation: false })\n\n        const buffergeometry = new BufferGeometry()\n\n        buffergeometry.setAttribute('position', new Float32BufferAttribute(state.vertices, 3))\n\n        if (state.colors.length > 0 && state.colors[0] !== undefined) {\n          buffergeometry.setAttribute('color', new Float32BufferAttribute(state.colors, 3))\n          material.vertexColors = true\n        }\n\n        const points = new Points(buffergeometry, material)\n        container.add(points)\n      }\n    }\n\n    return container\n  }\n}\n\nexport { OBJLoader }\n"], "names": ["name"], "mappings": ";AAiBA,MAAM,kBAAkB;AAExB,MAAM,4BAA4B;AAElC,MAAM,wBAAwB;AAE9B,MAAM,mBAAmB;AAEzB,MAAM,MAAsB,oBAAI,QAAS;AACzC,MAAM,MAAsB,oBAAI,QAAS;AACzC,MAAM,MAAsB,oBAAI,QAAS;AAEzC,MAAM,MAAsB,oBAAI,QAAS;AACzC,MAAM,MAAsB,oBAAI,QAAS;AAEzC,SAAS,cAAc;AACrB,QAAM,QAAQ;AAAA,IACZ,SAAS,CAAE;AAAA,IACX,QAAQ,CAAE;AAAA,IAEV,UAAU,CAAE;AAAA,IACZ,SAAS,CAAE;AAAA,IACX,QAAQ,CAAE;AAAA,IACV,KAAK,CAAE;AAAA,IAEP,WAAW,CAAE;AAAA,IACb,mBAAmB,CAAE;AAAA,IAErB,aAAa,SAAU,MAAM,iBAAiB;AAG5C,UAAI,KAAK,UAAU,KAAK,OAAO,oBAAoB,OAAO;AACxD,aAAK,OAAO,OAAO;AACnB,aAAK,OAAO,kBAAkB,oBAAoB;AAClD;AAAA,MACD;AAED,YAAM,mBACJ,KAAK,UAAU,OAAO,KAAK,OAAO,oBAAoB,aAAa,KAAK,OAAO,gBAAe,IAAK;AAErG,UAAI,KAAK,UAAU,OAAO,KAAK,OAAO,cAAc,YAAY;AAC9D,aAAK,OAAO,UAAU,IAAI;AAAA,MAC3B;AAED,WAAK,SAAS;AAAA,QACZ,MAAM,QAAQ;AAAA,QACd,iBAAiB,oBAAoB;AAAA,QAErC,UAAU;AAAA,UACR,UAAU,CAAE;AAAA,UACZ,SAAS,CAAE;AAAA,UACX,QAAQ,CAAE;AAAA,UACV,KAAK,CAAE;AAAA,UACP,cAAc;AAAA,QACf;AAAA,QACD,WAAW,CAAE;AAAA,QACb,QAAQ;AAAA,QAER,eAAe,SAAUA,OAAM,WAAW;AACxC,gBAAM,WAAW,KAAK,UAAU,KAAK;AAIrC,cAAI,aAAa,SAAS,aAAa,SAAS,cAAc,IAAI;AAChE,iBAAK,UAAU,OAAO,SAAS,OAAO,CAAC;AAAA,UACxC;AAED,gBAAM,WAAW;AAAA,YACf,OAAO,KAAK,UAAU;AAAA,YACtB,MAAMA,SAAQ;AAAA,YACd,QAAQ,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,IAAI,UAAU,UAAU,SAAS,CAAC,IAAI;AAAA,YAC7F,QAAQ,aAAa,SAAY,SAAS,SAAS,KAAK;AAAA,YACxD,YAAY,aAAa,SAAY,SAAS,WAAW;AAAA,YACzD,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,WAAW;AAAA,YAEX,OAAO,SAAU,OAAO;AACtB,oBAAM,SAAS;AAAA,gBACb,OAAO,OAAO,UAAU,WAAW,QAAQ,KAAK;AAAA,gBAChD,MAAM,KAAK;AAAA,gBACX,QAAQ,KAAK;AAAA,gBACb,QAAQ,KAAK;AAAA,gBACb,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,WAAW;AAAA,cACZ;AACD,qBAAO,QAAQ,KAAK,MAAM,KAAK,MAAM;AACrC,qBAAO;AAAA,YACR;AAAA,UACF;AAED,eAAK,UAAU,KAAK,QAAQ;AAE5B,iBAAO;AAAA,QACR;AAAA,QAED,iBAAiB,WAAY;AAC3B,cAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,mBAAO,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC;AAAA,UAChD;AAED,iBAAO;AAAA,QACR;AAAA,QAED,WAAW,SAAU,KAAK;AACxB,gBAAM,oBAAoB,KAAK,gBAAiB;AAChD,cAAI,qBAAqB,kBAAkB,aAAa,IAAI;AAC1D,8BAAkB,WAAW,KAAK,SAAS,SAAS,SAAS;AAC7D,8BAAkB,aAAa,kBAAkB,WAAW,kBAAkB;AAC9E,8BAAkB,YAAY;AAAA,UAC/B;AAGD,cAAI,OAAO,KAAK,UAAU,SAAS,GAAG;AACpC,qBAAS,KAAK,KAAK,UAAU,SAAS,GAAG,MAAM,GAAG,MAAM;AACtD,kBAAI,KAAK,UAAU,EAAE,EAAE,cAAc,GAAG;AACtC,qBAAK,UAAU,OAAO,IAAI,CAAC;AAAA,cAC5B;AAAA,YACF;AAAA,UACF;AAGD,cAAI,OAAO,KAAK,UAAU,WAAW,GAAG;AACtC,iBAAK,UAAU,KAAK;AAAA,cAClB,MAAM;AAAA,cACN,QAAQ,KAAK;AAAA,YAC3B,CAAa;AAAA,UACF;AAED,iBAAO;AAAA,QACR;AAAA,MACF;AAQD,UAAI,oBAAoB,iBAAiB,QAAQ,OAAO,iBAAiB,UAAU,YAAY;AAC7F,cAAM,WAAW,iBAAiB,MAAM,CAAC;AACzC,iBAAS,YAAY;AACrB,aAAK,OAAO,UAAU,KAAK,QAAQ;AAAA,MACpC;AAED,WAAK,QAAQ,KAAK,KAAK,MAAM;AAAA,IAC9B;AAAA,IAED,UAAU,WAAY;AACpB,UAAI,KAAK,UAAU,OAAO,KAAK,OAAO,cAAc,YAAY;AAC9D,aAAK,OAAO,UAAU,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,IAED,kBAAkB,SAAU,OAAO,KAAK;AACtC,YAAM,QAAQ,SAAS,OAAO,EAAE;AAChC,cAAQ,SAAS,IAAI,QAAQ,IAAI,QAAQ,MAAM,KAAK;AAAA,IACrD;AAAA,IAED,kBAAkB,SAAU,OAAO,KAAK;AACtC,YAAM,QAAQ,SAAS,OAAO,EAAE;AAChC,cAAQ,SAAS,IAAI,QAAQ,IAAI,QAAQ,MAAM,KAAK;AAAA,IACrD;AAAA,IAED,cAAc,SAAU,OAAO,KAAK;AAClC,YAAM,QAAQ,SAAS,OAAO,EAAE;AAChC,cAAQ,SAAS,IAAI,QAAQ,IAAI,QAAQ,MAAM,KAAK;AAAA,IACrD;AAAA,IAED,WAAW,SAAU,GAAG,GAAG,GAAG;AAC5B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC3C,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC3C,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IAC5C;AAAA,IAED,gBAAgB,SAAU,GAAG;AAC3B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IAC5C;AAAA,IAED,eAAe,SAAU,GAAG;AAC1B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IAC5C;AAAA,IAED,WAAW,SAAU,GAAG,GAAG,GAAG;AAC5B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC3C,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC3C,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IAC5C;AAAA,IAED,eAAe,SAAU,GAAG,GAAG,GAAG;AAChC,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,UAAU,KAAK,CAAC;AACpB,UAAI,UAAU,KAAK,CAAC;AACpB,UAAI,UAAU,KAAK,CAAC;AAEpB,UAAI,WAAW,KAAK,GAAG;AACvB,UAAI,WAAW,KAAK,GAAG;AACvB,UAAI,MAAM,GAAG;AAEb,UAAI,UAAW;AAEf,UAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC5B,UAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC5B,UAAI,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAAA,IAC7B;AAAA,IAED,UAAU,SAAU,GAAG,GAAG,GAAG;AAC3B,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,IAAI,CAAC,MAAM;AAAW,YAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AACrE,UAAI,IAAI,CAAC,MAAM;AAAW,YAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AACrE,UAAI,IAAI,CAAC,MAAM;AAAW,YAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IACtE;AAAA,IAED,OAAO,SAAU,GAAG,GAAG,GAAG;AACxB,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC/B,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAC/B,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IAChC;AAAA,IAED,cAAc,WAAY;AACxB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAK,GAAG,CAAC;AACb,UAAI,KAAK,GAAG,CAAC;AACb,UAAI,KAAK,GAAG,CAAC;AAAA,IACd;AAAA,IAED,WAAW,SAAU,GAAG;AACtB,YAAM,MAAM,KAAK;AACjB,YAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,UAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,IAChC;AAAA,IAED,SAAS,SAAU,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAClD,YAAM,OAAO,KAAK,SAAS;AAE3B,UAAI,KAAK,KAAK,iBAAiB,GAAG,IAAI;AACtC,UAAI,KAAK,KAAK,iBAAiB,GAAG,IAAI;AACtC,UAAI,KAAK,KAAK,iBAAiB,GAAG,IAAI;AAEtC,WAAK,UAAU,IAAI,IAAI,EAAE;AACzB,WAAK,SAAS,IAAI,IAAI,EAAE;AAIxB,UAAI,OAAO,UAAa,OAAO,IAAI;AACjC,cAAM,OAAO,KAAK,QAAQ;AAE1B,aAAK,KAAK,iBAAiB,IAAI,IAAI;AACnC,aAAK,KAAK,iBAAiB,IAAI,IAAI;AACnC,aAAK,KAAK,iBAAiB,IAAI,IAAI;AAEnC,aAAK,UAAU,IAAI,IAAI,EAAE;AAAA,MACjC,OAAa;AACL,aAAK,cAAc,IAAI,IAAI,EAAE;AAAA,MAC9B;AAID,UAAI,OAAO,UAAa,OAAO,IAAI;AACjC,cAAM,QAAQ,KAAK,IAAI;AAEvB,aAAK,KAAK,aAAa,IAAI,KAAK;AAChC,aAAK,KAAK,aAAa,IAAI,KAAK;AAChC,aAAK,KAAK,aAAa,IAAI,KAAK;AAEhC,aAAK,MAAM,IAAI,IAAI,EAAE;AAErB,aAAK,OAAO,SAAS,eAAe;AAAA,MAC5C,OAAa;AAGL,aAAK,aAAc;AAAA,MACpB;AAAA,IACF;AAAA,IAED,kBAAkB,SAAU,UAAU;AACpC,WAAK,OAAO,SAAS,OAAO;AAE5B,YAAM,OAAO,KAAK,SAAS;AAE3B,eAAS,KAAK,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG,MAAM;AAClD,cAAM,QAAQ,KAAK,iBAAiB,SAAS,EAAE,GAAG,IAAI;AAEtD,aAAK,eAAe,KAAK;AACzB,aAAK,SAAS,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,IAED,iBAAiB,SAAU,UAAU,KAAK;AACxC,WAAK,OAAO,SAAS,OAAO;AAE5B,YAAM,OAAO,KAAK,SAAS;AAC3B,YAAM,QAAQ,KAAK,IAAI;AAEvB,eAAS,KAAK,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG,MAAM;AAClD,aAAK,cAAc,KAAK,iBAAiB,SAAS,EAAE,GAAG,IAAI,CAAC;AAAA,MAC7D;AAED,eAAS,MAAM,GAAG,IAAI,IAAI,QAAQ,MAAM,GAAG,OAAO;AAChD,aAAK,UAAU,KAAK,aAAa,IAAI,GAAG,GAAG,KAAK,CAAC;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAED,QAAM,YAAY,IAAI,KAAK;AAE3B,SAAO;AACT;AAIA,MAAM,kBAAkB,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAEb,SAAK,YAAY;AAAA,EAClB;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAI,WAAW,KAAK,OAAO;AAC1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,iBAAiB,KAAK,aAAa;AAC1C,WAAO,mBAAmB,KAAK,eAAe;AAC9C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,iBAAO,MAAM,MAAM,IAAI,CAAC;AAAA,QACzB,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,aAAa,WAAW;AACtB,SAAK,YAAY;AAEjB,WAAO;AAAA,EACR;AAAA,EAED,MAAM,MAAM;AACV,UAAM,QAAQ,IAAI,YAAa;AAE/B,QAAI,KAAK,QAAQ,MAAM,MAAM,IAAI;AAE/B,aAAO,KAAK,QAAQ,SAAS,IAAI;AAAA,IAClC;AAED,QAAI,KAAK,QAAQ,MAAM,MAAM,IAAI;AAE/B,aAAO,KAAK,QAAQ,SAAS,EAAE;AAAA,IAChC;AAED,UAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,QAAI,OAAO,IACT,gBAAgB;AAClB,QAAI,aAAa;AACjB,QAAI,SAAS,CAAE;AAGf,UAAM,WAAW,OAAO,GAAG,aAAa;AAExC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,aAAO,MAAM,CAAC;AAEd,aAAO,WAAW,KAAK,SAAQ,IAAK,KAAK,KAAM;AAE/C,mBAAa,KAAK;AAElB,UAAI,eAAe;AAAG;AAEtB,sBAAgB,KAAK,OAAO,CAAC;AAG7B,UAAI,kBAAkB;AAAK;AAE3B,UAAI,kBAAkB,KAAK;AACzB,cAAM,OAAO,KAAK,MAAM,KAAK;AAE7B,gBAAQ,KAAK,CAAC,GAAC;AAAA,UACb,KAAK;AACH,kBAAM,SAAS,KAAK,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC;AACjF,gBAAI,KAAK,UAAU,GAAG;AACpB,oBAAM,OAAO,KAAK,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC;AAAA,YAC7F,OAAmB;AAGL,oBAAM,OAAO,KAAK,QAAW,QAAW,MAAS;AAAA,YAClD;AAED;AAAA,UACF,KAAK;AACH,kBAAM,QAAQ,KAAK,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC;AAChF;AAAA,UACF,KAAK;AACH,kBAAM,IAAI,KAAK,WAAW,KAAK,CAAC,CAAC,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC;AACvD;AAAA,QACH;AAAA,MACT,WAAiB,kBAAkB,KAAK;AAChC,cAAM,WAAW,KAAK,OAAO,CAAC,EAAE,KAAM;AACtC,cAAM,aAAa,SAAS,MAAM,KAAK;AACvC,cAAM,eAAe,CAAE;AAIvB,iBAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,gBAAM,SAAS,WAAW,CAAC;AAE3B,cAAI,OAAO,SAAS,GAAG;AACrB,kBAAM,cAAc,OAAO,MAAM,GAAG;AACpC,yBAAa,KAAK,WAAW;AAAA,UAC9B;AAAA,QACF;AAID,cAAM,KAAK,aAAa,CAAC;AAEzB,iBAAS,IAAI,GAAG,KAAK,aAAa,SAAS,GAAG,IAAI,IAAI,KAAK;AACzD,gBAAM,KAAK,aAAa,CAAC;AACzB,gBAAM,KAAK,aAAa,IAAI,CAAC;AAE7B,gBAAM,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,QAC5E;AAAA,MACT,WAAiB,kBAAkB,KAAK;AAChC,cAAM,YAAY,KAAK,UAAU,CAAC,EAAE,KAAM,EAAC,MAAM,GAAG;AACpD,YAAI,eAAe,CAAE;AACrB,cAAM,UAAU,CAAE;AAElB,YAAI,KAAK,QAAQ,GAAG,MAAM,IAAI;AAC5B,yBAAe;AAAA,QACzB,OAAe;AACL,mBAAS,KAAK,GAAG,OAAO,UAAU,QAAQ,KAAK,MAAM,MAAM;AACzD,kBAAM,QAAQ,UAAU,EAAE,EAAE,MAAM,GAAG;AAErC,gBAAI,MAAM,CAAC,MAAM;AAAI,2BAAa,KAAK,MAAM,CAAC,CAAC;AAC/C,gBAAI,MAAM,CAAC,MAAM;AAAI,sBAAQ,KAAK,MAAM,CAAC,CAAC;AAAA,UAC3C;AAAA,QACF;AAED,cAAM,gBAAgB,cAAc,OAAO;AAAA,MACnD,WAAiB,kBAAkB,KAAK;AAChC,cAAM,WAAW,KAAK,OAAO,CAAC,EAAE,KAAM;AACtC,cAAM,YAAY,SAAS,MAAM,GAAG;AAEpC,cAAM,iBAAiB,SAAS;AAAA,MACxC,YAAkB,SAAS,gBAAgB,KAAK,IAAI,OAAO,MAAM;AAOzD,cAAM,QAAQ,MAAM,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,OAAO,CAAC;AAExD,cAAM,YAAY,IAAI;AAAA,MACvB,WAAU,sBAAsB,KAAK,IAAI,GAAG;AAG3C,cAAM,OAAO,cAAc,KAAK,UAAU,CAAC,EAAE,KAAI,GAAI,MAAM,iBAAiB;AAAA,MAC7E,WAAU,0BAA0B,KAAK,IAAI,GAAG;AAG/C,cAAM,kBAAkB,KAAK,KAAK,UAAU,CAAC,EAAE,MAAM;AAAA,MACtD,WAAU,iBAAiB,KAAK,IAAI,GAAG;AAItC,gBAAQ;AAAA,UACN;AAAA,QACD;AAAA,MACT,WAAiB,kBAAkB,KAAK;AAChC,iBAAS,KAAK,MAAM,GAAG;AAsBvB,YAAI,OAAO,SAAS,GAAG;AACrB,gBAAM,QAAQ,OAAO,CAAC,EAAE,KAAI,EAAG,YAAa;AAC5C,gBAAM,OAAO,SAAS,UAAU,OAAO,UAAU;AAAA,QAC3D,OAAe;AAEL,gBAAM,OAAO,SAAS;AAAA,QACvB;AAED,cAAM,WAAW,MAAM,OAAO,gBAAiB;AAC/C,YAAI;AAAU,mBAAS,SAAS,MAAM,OAAO;AAAA,MACrD,OAAa;AAEL,YAAI,SAAS;AAAM;AAEnB,gBAAQ,KAAK,wCAAwC,OAAO,GAAG;AAAA,MAChE;AAAA,IACF;AAED,UAAM,SAAU;AAEhB,UAAM,YAAY,IAAI,MAAO;AAC7B,cAAU,oBAAoB,CAAA,EAAG,OAAO,MAAM,iBAAiB;AAE/D,UAAM,gBAAgB,EAAE,MAAM,QAAQ,WAAW,KAAK,MAAM,QAAQ,CAAC,EAAE,SAAS,SAAS,WAAW;AAEpG,QAAI,kBAAkB,MAAM;AAC1B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK;AACpD,cAAM,SAAS,MAAM,QAAQ,CAAC;AAC9B,cAAM,WAAW,OAAO;AACxB,cAAM,YAAY,OAAO;AACzB,cAAM,SAAS,SAAS,SAAS;AACjC,cAAM,WAAW,SAAS,SAAS;AACnC,YAAI,kBAAkB;AAGtB,YAAI,SAAS,SAAS,WAAW;AAAG;AAEpC,cAAM,iBAAiB,IAAI,eAAgB;AAE3C,uBAAe,aAAa,YAAY,IAAI,uBAAuB,SAAS,UAAU,CAAC,CAAC;AAExF,YAAI,SAAS,QAAQ,SAAS,GAAG;AAC/B,yBAAe,aAAa,UAAU,IAAI,uBAAuB,SAAS,SAAS,CAAC,CAAC;AAAA,QACtF;AAED,YAAI,SAAS,OAAO,SAAS,GAAG;AAC9B,4BAAkB;AAClB,yBAAe,aAAa,SAAS,IAAI,uBAAuB,SAAS,QAAQ,CAAC,CAAC;AAAA,QACpF;AAED,YAAI,SAAS,iBAAiB,MAAM;AAClC,yBAAe,aAAa,MAAM,IAAI,uBAAuB,SAAS,KAAK,CAAC,CAAC;AAAA,QAC9E;AAID,cAAM,mBAAmB,CAAE;AAE3B,iBAAS,KAAK,GAAG,QAAQ,UAAU,QAAQ,KAAK,OAAO,MAAM;AAC3D,gBAAM,iBAAiB,UAAU,EAAE;AACnC,gBAAM,eAAe,eAAe,OAAO,MAAM,eAAe,SAAS,MAAM;AAC/E,cAAI,WAAW,MAAM,UAAU,YAAY;AAE3C,cAAI,KAAK,cAAc,MAAM;AAC3B,uBAAW,KAAK,UAAU,OAAO,eAAe,IAAI;AAGpD,gBAAI,UAAU,YAAY,EAAE,oBAAoB,oBAAoB;AAClE,oBAAM,eAAe,IAAI,kBAAmB;AAC5C,uBAAS,UAAU,KAAK,KAAK,cAAc,QAAQ;AACnD,2BAAa,MAAM,KAAK,SAAS,KAAK;AACtC,yBAAW;AAAA,YACZ,WAAU,YAAY,YAAY,EAAE,oBAAoB,iBAAiB;AACxE,oBAAM,iBAAiB,IAAI,eAAe,EAAE,MAAM,IAAI,iBAAiB,OAAO;AAC9E,uBAAS,UAAU,KAAK,KAAK,gBAAgB,QAAQ;AACrD,6BAAe,MAAM,KAAK,SAAS,KAAK;AACxC,6BAAe,MAAM,SAAS;AAC9B,yBAAW;AAAA,YACZ;AAAA,UACF;AAED,cAAI,aAAa,QAAW;AAC1B,gBAAI,QAAQ;AACV,yBAAW,IAAI,kBAAmB;AAAA,YACnC,WAAU,UAAU;AACnB,yBAAW,IAAI,eAAe,EAAE,MAAM,GAAG,iBAAiB,OAAO;AAAA,YAC/E,OAAmB;AACL,yBAAW,IAAI,kBAAmB;AAAA,YACnC;AAED,qBAAS,OAAO,eAAe;AAC/B,qBAAS,cAAc,eAAe,SAAS,QAAQ;AACvD,qBAAS,eAAe;AAExB,kBAAM,UAAU,YAAY,IAAI;AAAA,UACjC;AAED,2BAAiB,KAAK,QAAQ;AAAA,QAC/B;AAID,YAAI;AAEJ,YAAI,iBAAiB,SAAS,GAAG;AAC/B,mBAAS,KAAK,GAAG,QAAQ,UAAU,QAAQ,KAAK,OAAO,MAAM;AAC3D,kBAAM,iBAAiB,UAAU,EAAE;AACnC,2BAAe,SAAS,eAAe,YAAY,eAAe,YAAY,EAAE;AAAA,UACjF;AAED,cAAI,QAAQ;AACV,mBAAO,IAAI,aAAa,gBAAgB,gBAAgB;AAAA,UACzD,WAAU,UAAU;AACnB,mBAAO,IAAI,OAAO,gBAAgB,gBAAgB;AAAA,UAC9D,OAAiB;AACL,mBAAO,IAAI,KAAK,gBAAgB,gBAAgB;AAAA,UACjD;AAAA,QACX,OAAe;AACL,cAAI,QAAQ;AACV,mBAAO,IAAI,aAAa,gBAAgB,iBAAiB,CAAC,CAAC;AAAA,UAC5D,WAAU,UAAU;AACnB,mBAAO,IAAI,OAAO,gBAAgB,iBAAiB,CAAC,CAAC;AAAA,UACjE,OAAiB;AACL,mBAAO,IAAI,KAAK,gBAAgB,iBAAiB,CAAC,CAAC;AAAA,UACpD;AAAA,QACF;AAED,aAAK,OAAO,OAAO;AAEnB,kBAAU,IAAI,IAAI;AAAA,MACnB;AAAA,IACP,OAAW;AAGL,UAAI,MAAM,SAAS,SAAS,GAAG;AAC7B,cAAM,WAAW,IAAI,eAAe,EAAE,MAAM,GAAG,iBAAiB,OAAO;AAEvE,cAAM,iBAAiB,IAAI,eAAgB;AAE3C,uBAAe,aAAa,YAAY,IAAI,uBAAuB,MAAM,UAAU,CAAC,CAAC;AAErF,YAAI,MAAM,OAAO,SAAS,KAAK,MAAM,OAAO,CAAC,MAAM,QAAW;AAC5D,yBAAe,aAAa,SAAS,IAAI,uBAAuB,MAAM,QAAQ,CAAC,CAAC;AAChF,mBAAS,eAAe;AAAA,QACzB;AAED,cAAM,SAAS,IAAI,OAAO,gBAAgB,QAAQ;AAClD,kBAAU,IAAI,MAAM;AAAA,MACrB;AAAA,IACF;AAED,WAAO;AAAA,EACR;AACH;"}