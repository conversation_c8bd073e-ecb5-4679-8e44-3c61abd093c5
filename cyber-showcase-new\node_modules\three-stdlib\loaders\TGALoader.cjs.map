{"version": 3, "file": "TGALoader.cjs", "sources": ["../../src/loaders/TGALoader.js"], "sourcesContent": ["import { DataTextureLoader, LinearMipmapLinearFilter } from 'three'\n\nclass TGALoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  parse(buffer) {\n    // reference from vthibault, https://github.com/vthibault/roBrowser/blob/master/src/Loaders/Targa.js\n\n    function tgaCheckHeader(header) {\n      switch (header.image_type) {\n        // check indexed type\n\n        case TGA_TYPE_INDEXED:\n        case TGA_TYPE_RLE_INDEXED:\n          if (header.colormap_length > 256 || header.colormap_size !== 24 || header.colormap_type !== 1) {\n            console.error('THREE.TGALoader: Invalid type colormap data for indexed type.')\n          }\n\n          break\n\n        // check colormap type\n\n        case TGA_TYPE_RGB:\n        case TGA_TYPE_GREY:\n        case TGA_TYPE_RLE_RGB:\n        case TGA_TYPE_RLE_GREY:\n          if (header.colormap_type) {\n            console.error('THREE.TGALoader: Invalid type colormap data for colormap type.')\n          }\n\n          break\n\n        // What the need of a file without data ?\n\n        case TGA_TYPE_NO_DATA:\n          console.error('THREE.TGALoader: No data.')\n\n        // Invalid type ?\n\n        default:\n          console.error('THREE.TGALoader: Invalid type \"%s\".', header.image_type)\n      }\n\n      // check image width and height\n\n      if (header.width <= 0 || header.height <= 0) {\n        console.error('THREE.TGALoader: Invalid image size.')\n      }\n\n      // check image pixel size\n\n      if (header.pixel_size !== 8 && header.pixel_size !== 16 && header.pixel_size !== 24 && header.pixel_size !== 32) {\n        console.error('THREE.TGALoader: Invalid pixel size \"%s\".', header.pixel_size)\n      }\n    }\n\n    // parse tga image buffer\n\n    function tgaParse(use_rle, use_pal, header, offset, data) {\n      let pixel_data, palettes\n\n      const pixel_size = header.pixel_size >> 3\n      const pixel_total = header.width * header.height * pixel_size\n\n      // read palettes\n\n      if (use_pal) {\n        palettes = data.subarray(offset, (offset += header.colormap_length * (header.colormap_size >> 3)))\n      }\n\n      // read RLE\n\n      if (use_rle) {\n        pixel_data = new Uint8Array(pixel_total)\n\n        let c, count, i\n        let shift = 0\n        const pixels = new Uint8Array(pixel_size)\n\n        while (shift < pixel_total) {\n          c = data[offset++]\n          count = (c & 0x7f) + 1\n\n          // RLE pixels\n\n          if (c & 0x80) {\n            // bind pixel tmp array\n\n            for (i = 0; i < pixel_size; ++i) {\n              pixels[i] = data[offset++]\n            }\n\n            // copy pixel array\n\n            for (i = 0; i < count; ++i) {\n              pixel_data.set(pixels, shift + i * pixel_size)\n            }\n\n            shift += pixel_size * count\n          } else {\n            // raw pixels\n\n            count *= pixel_size\n\n            for (i = 0; i < count; ++i) {\n              pixel_data[shift + i] = data[offset++]\n            }\n\n            shift += count\n          }\n        }\n      } else {\n        // raw pixels\n\n        pixel_data = data.subarray(offset, (offset += use_pal ? header.width * header.height : pixel_total))\n      }\n\n      return {\n        pixel_data: pixel_data,\n        palettes: palettes,\n      }\n    }\n\n    function tgaGetImageData8bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image, palettes) {\n      const colormap = palettes\n      let color,\n        i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i++) {\n          color = image[i]\n          imageData[(x + width * y) * 4 + 3] = 255\n          imageData[(x + width * y) * 4 + 2] = colormap[color * 3 + 0]\n          imageData[(x + width * y) * 4 + 1] = colormap[color * 3 + 1]\n          imageData[(x + width * y) * 4 + 0] = colormap[color * 3 + 2]\n        }\n      }\n\n      return imageData\n    }\n\n    function tgaGetImageData16bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let color,\n        i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 2) {\n          color = image[i + 0] + (image[i + 1] << 8) // Inversed ?\n          imageData[(x + width * y) * 4 + 0] = (color & 0x7c00) >> 7\n          imageData[(x + width * y) * 4 + 1] = (color & 0x03e0) >> 2\n          imageData[(x + width * y) * 4 + 2] = (color & 0x001f) >> 3\n          imageData[(x + width * y) * 4 + 3] = color & 0x8000 ? 0 : 255\n        }\n      }\n\n      return imageData\n    }\n\n    function tgaGetImageData24bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 3) {\n          imageData[(x + width * y) * 4 + 3] = 255\n          imageData[(x + width * y) * 4 + 2] = image[i + 0]\n          imageData[(x + width * y) * 4 + 1] = image[i + 1]\n          imageData[(x + width * y) * 4 + 0] = image[i + 2]\n        }\n      }\n\n      return imageData\n    }\n\n    function tgaGetImageData32bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 4) {\n          imageData[(x + width * y) * 4 + 2] = image[i + 0]\n          imageData[(x + width * y) * 4 + 1] = image[i + 1]\n          imageData[(x + width * y) * 4 + 0] = image[i + 2]\n          imageData[(x + width * y) * 4 + 3] = image[i + 3]\n        }\n      }\n\n      return imageData\n    }\n\n    function tgaGetImageDataGrey8bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let color,\n        i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i++) {\n          color = image[i]\n          imageData[(x + width * y) * 4 + 0] = color\n          imageData[(x + width * y) * 4 + 1] = color\n          imageData[(x + width * y) * 4 + 2] = color\n          imageData[(x + width * y) * 4 + 3] = 255\n        }\n      }\n\n      return imageData\n    }\n\n    function tgaGetImageDataGrey16bits(imageData, y_start, y_step, y_end, x_start, x_step, x_end, image) {\n      let i = 0,\n        x,\n        y\n      const width = header.width\n\n      for (y = y_start; y !== y_end; y += y_step) {\n        for (x = x_start; x !== x_end; x += x_step, i += 2) {\n          imageData[(x + width * y) * 4 + 0] = image[i + 0]\n          imageData[(x + width * y) * 4 + 1] = image[i + 0]\n          imageData[(x + width * y) * 4 + 2] = image[i + 0]\n          imageData[(x + width * y) * 4 + 3] = image[i + 1]\n        }\n      }\n\n      return imageData\n    }\n\n    function getTgaRGBA(data, width, height, image, palette) {\n      let x_start, y_start, x_step, y_step, x_end, y_end\n\n      switch ((header.flags & TGA_ORIGIN_MASK) >> TGA_ORIGIN_SHIFT) {\n        default:\n        case TGA_ORIGIN_UL:\n          x_start = 0\n          x_step = 1\n          x_end = width\n          y_start = 0\n          y_step = 1\n          y_end = height\n          break\n\n        case TGA_ORIGIN_BL:\n          x_start = 0\n          x_step = 1\n          x_end = width\n          y_start = height - 1\n          y_step = -1\n          y_end = -1\n          break\n\n        case TGA_ORIGIN_UR:\n          x_start = width - 1\n          x_step = -1\n          x_end = -1\n          y_start = 0\n          y_step = 1\n          y_end = height\n          break\n\n        case TGA_ORIGIN_BR:\n          x_start = width - 1\n          x_step = -1\n          x_end = -1\n          y_start = height - 1\n          y_step = -1\n          y_end = -1\n          break\n      }\n\n      if (use_grey) {\n        switch (header.pixel_size) {\n          case 8:\n            tgaGetImageDataGrey8bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image)\n            break\n\n          case 16:\n            tgaGetImageDataGrey16bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image)\n            break\n\n          default:\n            console.error('THREE.TGALoader: Format not supported.')\n            break\n        }\n      } else {\n        switch (header.pixel_size) {\n          case 8:\n            tgaGetImageData8bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image, palette)\n            break\n\n          case 16:\n            tgaGetImageData16bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image)\n            break\n\n          case 24:\n            tgaGetImageData24bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image)\n            break\n\n          case 32:\n            tgaGetImageData32bits(data, y_start, y_step, y_end, x_start, x_step, x_end, image)\n            break\n\n          default:\n            console.error('THREE.TGALoader: Format not supported.')\n            break\n        }\n      }\n\n      // Load image data according to specific method\n      // let func = 'tgaGetImageData' + (use_grey ? 'Grey' : '') + (header.pixel_size) + 'bits';\n      // func(data, y_start, y_step, y_end, x_start, x_step, x_end, width, image, palette );\n      return data\n    }\n\n    // TGA constants\n\n    const TGA_TYPE_NO_DATA = 0,\n      TGA_TYPE_INDEXED = 1,\n      TGA_TYPE_RGB = 2,\n      TGA_TYPE_GREY = 3,\n      TGA_TYPE_RLE_INDEXED = 9,\n      TGA_TYPE_RLE_RGB = 10,\n      TGA_TYPE_RLE_GREY = 11,\n      TGA_ORIGIN_MASK = 0x30,\n      TGA_ORIGIN_SHIFT = 0x04,\n      TGA_ORIGIN_BL = 0x00,\n      TGA_ORIGIN_BR = 0x01,\n      TGA_ORIGIN_UL = 0x02,\n      TGA_ORIGIN_UR = 0x03\n\n    if (buffer.length < 19) console.error('THREE.TGALoader: Not enough data to contain header.')\n\n    let offset = 0\n\n    const content = new Uint8Array(buffer),\n      header = {\n        id_length: content[offset++],\n        colormap_type: content[offset++],\n        image_type: content[offset++],\n        colormap_index: content[offset++] | (content[offset++] << 8),\n        colormap_length: content[offset++] | (content[offset++] << 8),\n        colormap_size: content[offset++],\n        origin: [content[offset++] | (content[offset++] << 8), content[offset++] | (content[offset++] << 8)],\n        width: content[offset++] | (content[offset++] << 8),\n        height: content[offset++] | (content[offset++] << 8),\n        pixel_size: content[offset++],\n        flags: content[offset++],\n      }\n\n    // check tga if it is valid format\n\n    tgaCheckHeader(header)\n\n    if (header.id_length + offset > buffer.length) {\n      console.error('THREE.TGALoader: No data.')\n    }\n\n    // skip the needn't data\n\n    offset += header.id_length\n\n    // get targa information about RLE compression and palette\n\n    let use_rle = false,\n      use_pal = false,\n      use_grey = false\n\n    switch (header.image_type) {\n      case TGA_TYPE_RLE_INDEXED:\n        use_rle = true\n        use_pal = true\n        break\n\n      case TGA_TYPE_INDEXED:\n        use_pal = true\n        break\n\n      case TGA_TYPE_RLE_RGB:\n        use_rle = true\n        break\n\n      case TGA_TYPE_RGB:\n        break\n\n      case TGA_TYPE_RLE_GREY:\n        use_rle = true\n        use_grey = true\n        break\n\n      case TGA_TYPE_GREY:\n        use_grey = true\n        break\n    }\n\n    //\n\n    const imageData = new Uint8Array(header.width * header.height * 4)\n    const result = tgaParse(use_rle, use_pal, header, offset, content)\n    getTgaRGBA(imageData, header.width, header.height, result.pixel_data, result.palettes)\n\n    return {\n      data: imageData,\n      width: header.width,\n      height: header.height,\n      flipY: true,\n      generateMipmaps: true,\n      minFilter: LinearMipmapLinearFilter,\n    }\n  }\n}\n\nexport { TGALoader }\n"], "names": ["DataTextureLoader", "header", "use_rle", "use_pal", "offset", "imageData", "LinearMipmapLinearFilter"], "mappings": ";;;AAEA,MAAM,kBAAkBA,MAAAA,kBAAkB;AAAA,EACxC,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACd;AAAA,EAED,MAAM,QAAQ;AAGZ,aAAS,eAAeC,SAAQ;AAC9B,cAAQA,QAAO,YAAU;AAAA,QAGvB,KAAK;AAAA,QACL,KAAK;AACH,cAAIA,QAAO,kBAAkB,OAAOA,QAAO,kBAAkB,MAAMA,QAAO,kBAAkB,GAAG;AAC7F,oBAAQ,MAAM,+DAA+D;AAAA,UAC9E;AAED;AAAA,QAIF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,cAAIA,QAAO,eAAe;AACxB,oBAAQ,MAAM,gEAAgE;AAAA,UAC/E;AAED;AAAA,QAIF,KAAK;AACH,kBAAQ,MAAM,2BAA2B;AAAA,QAI3C;AACE,kBAAQ,MAAM,uCAAuCA,QAAO,UAAU;AAAA,MACzE;AAID,UAAIA,QAAO,SAAS,KAAKA,QAAO,UAAU,GAAG;AAC3C,gBAAQ,MAAM,sCAAsC;AAAA,MACrD;AAID,UAAIA,QAAO,eAAe,KAAKA,QAAO,eAAe,MAAMA,QAAO,eAAe,MAAMA,QAAO,eAAe,IAAI;AAC/G,gBAAQ,MAAM,6CAA6CA,QAAO,UAAU;AAAA,MAC7E;AAAA,IACF;AAID,aAAS,SAASC,UAASC,UAASF,SAAQG,SAAQ,MAAM;AACxD,UAAI,YAAY;AAEhB,YAAM,aAAaH,QAAO,cAAc;AACxC,YAAM,cAAcA,QAAO,QAAQA,QAAO,SAAS;AAInD,UAAIE,UAAS;AACX,mBAAW,KAAK,SAASC,SAASA,WAAUH,QAAO,mBAAmBA,QAAO,iBAAiB,EAAI;AAAA,MACnG;AAID,UAAIC,UAAS;AACX,qBAAa,IAAI,WAAW,WAAW;AAEvC,YAAI,GAAG,OAAO;AACd,YAAI,QAAQ;AACZ,cAAM,SAAS,IAAI,WAAW,UAAU;AAExC,eAAO,QAAQ,aAAa;AAC1B,cAAI,KAAKE,SAAQ;AACjB,mBAAS,IAAI,OAAQ;AAIrB,cAAI,IAAI,KAAM;AAGZ,iBAAK,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AAC/B,qBAAO,CAAC,IAAI,KAAKA,SAAQ;AAAA,YAC1B;AAID,iBAAK,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC1B,yBAAW,IAAI,QAAQ,QAAQ,IAAI,UAAU;AAAA,YAC9C;AAED,qBAAS,aAAa;AAAA,UAClC,OAAiB;AAGL,qBAAS;AAET,iBAAK,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC1B,yBAAW,QAAQ,CAAC,IAAI,KAAKA,SAAQ;AAAA,YACtC;AAED,qBAAS;AAAA,UACV;AAAA,QACF;AAAA,MACT,OAAa;AAGL,qBAAa,KAAK,SAASA,SAASA,WAAUD,WAAUF,QAAO,QAAQA,QAAO,SAAS,WAAa;AAAA,MACrG;AAED,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACD;AAAA,IACF;AAED,aAAS,qBAAqBI,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAO,UAAU;AACxG,YAAM,WAAW;AACjB,UAAI,OACF,IAAI,GACJ,GACA;AACF,YAAM,QAAQ,OAAO;AAErB,WAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ;AAC1C,aAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAK;AAC/C,kBAAQ,MAAM,CAAC;AACf,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AACrC,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,SAAS,QAAQ,IAAI,CAAC;AAC3D,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,SAAS,QAAQ,IAAI,CAAC;AAC3D,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,SAAS,QAAQ,IAAI,CAAC;AAAA,QAC5D;AAAA,MACF;AAED,aAAOA;AAAA,IACR;AAED,aAAS,sBAAsBA,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAO;AAC/F,UAAI,OACF,IAAI,GACJ,GACA;AACF,YAAM,QAAQ,OAAO;AAErB,WAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ;AAC1C,aAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAK,GAAG;AAClD,kBAAQ,MAAM,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK;AACxC,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,KAAK,QAAQ,UAAW;AACzD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,KAAK,QAAQ,QAAW;AACzD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,KAAK,QAAQ,OAAW;AACzD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,QAAQ,QAAS,IAAI;AAAA,QAC3D;AAAA,MACF;AAED,aAAOA;AAAA,IACR;AAED,aAAS,sBAAsBA,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAO;AAC/F,UAAI,IAAI,GACN,GACA;AACF,YAAM,QAAQ,OAAO;AAErB,WAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ;AAC1C,aAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAK,GAAG;AAClD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AACrC,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAChD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAChD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAAA,QACjD;AAAA,MACF;AAED,aAAOA;AAAA,IACR;AAED,aAAS,sBAAsBA,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAO;AAC/F,UAAI,IAAI,GACN,GACA;AACF,YAAM,QAAQ,OAAO;AAErB,WAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ;AAC1C,aAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAK,GAAG;AAClD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAChD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAChD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAChD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAAA,QACjD;AAAA,MACF;AAED,aAAOA;AAAA,IACR;AAED,aAAS,yBAAyBA,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAO;AAClG,UAAI,OACF,IAAI,GACJ,GACA;AACF,YAAM,QAAQ,OAAO;AAErB,WAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ;AAC1C,aAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAK;AAC/C,kBAAQ,MAAM,CAAC;AACf,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AACrC,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AACrC,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AACrC,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AAAA,QACtC;AAAA,MACF;AAED,aAAOA;AAAA,IACR;AAED,aAAS,0BAA0BA,YAAW,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAO;AACnG,UAAI,IAAI,GACN,GACA;AACF,YAAM,QAAQ,OAAO;AAErB,WAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ;AAC1C,aAAK,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAK,GAAG;AAClD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAChD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAChD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAChD,UAAAA,YAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC;AAAA,QACjD;AAAA,MACF;AAED,aAAOA;AAAA,IACR;AAED,aAAS,WAAW,MAAM,OAAO,QAAQ,OAAO,SAAS;AACvD,UAAI,SAAS,SAAS,QAAQ,QAAQ,OAAO;AAE7C,eAAS,OAAO,QAAQ,oBAAoB,kBAAgB;AAAA,QAC1D;AAAA,QACA,KAAK;AACH,oBAAU;AACV,mBAAS;AACT,kBAAQ;AACR,oBAAU;AACV,mBAAS;AACT,kBAAQ;AACR;AAAA,QAEF,KAAK;AACH,oBAAU;AACV,mBAAS;AACT,kBAAQ;AACR,oBAAU,SAAS;AACnB,mBAAS;AACT,kBAAQ;AACR;AAAA,QAEF,KAAK;AACH,oBAAU,QAAQ;AAClB,mBAAS;AACT,kBAAQ;AACR,oBAAU;AACV,mBAAS;AACT,kBAAQ;AACR;AAAA,QAEF,KAAK;AACH,oBAAU,QAAQ;AAClB,mBAAS;AACT,kBAAQ;AACR,oBAAU,SAAS;AACnB,mBAAS;AACT,kBAAQ;AACR;AAAA,MACH;AAED,UAAI,UAAU;AACZ,gBAAQ,OAAO,YAAU;AAAA,UACvB,KAAK;AACH,qCAAyB,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,KAAK;AACpF;AAAA,UAEF,KAAK;AACH,sCAA0B,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,KAAK;AACrF;AAAA,UAEF;AACE,oBAAQ,MAAM,wCAAwC;AACtD;AAAA,QACH;AAAA,MACT,OAAa;AACL,gBAAQ,OAAO,YAAU;AAAA,UACvB,KAAK;AACH,iCAAqB,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAO,OAAO;AACzF;AAAA,UAEF,KAAK;AACH,kCAAsB,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,KAAK;AACjF;AAAA,UAEF,KAAK;AACH,kCAAsB,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,KAAK;AACjF;AAAA,UAEF,KAAK;AACH,kCAAsB,MAAM,SAAS,QAAQ,OAAO,SAAS,QAAQ,OAAO,KAAK;AACjF;AAAA,UAEF;AACE,oBAAQ,MAAM,wCAAwC;AACtD;AAAA,QACH;AAAA,MACF;AAKD,aAAO;AAAA,IACR;AAID,UAAM,mBAAmB,GACvB,mBAAmB,GACnB,eAAe,GACf,gBAAgB,GAChB,uBAAuB,GACvB,mBAAmB,IACnB,oBAAoB,IACpB,kBAAkB,IAClB,mBAAmB,GACnB,gBAAgB,GAChB,gBAAgB,GAChB,gBAAgB,GAChB,gBAAgB;AAElB,QAAI,OAAO,SAAS;AAAI,cAAQ,MAAM,qDAAqD;AAE3F,QAAI,SAAS;AAEb,UAAM,UAAU,IAAI,WAAW,MAAM,GACnC,SAAS;AAAA,MACP,WAAW,QAAQ,QAAQ;AAAA,MAC3B,eAAe,QAAQ,QAAQ;AAAA,MAC/B,YAAY,QAAQ,QAAQ;AAAA,MAC5B,gBAAgB,QAAQ,QAAQ,IAAK,QAAQ,QAAQ,KAAK;AAAA,MAC1D,iBAAiB,QAAQ,QAAQ,IAAK,QAAQ,QAAQ,KAAK;AAAA,MAC3D,eAAe,QAAQ,QAAQ;AAAA,MAC/B,QAAQ,CAAC,QAAQ,QAAQ,IAAK,QAAQ,QAAQ,KAAK,GAAI,QAAQ,QAAQ,IAAK,QAAQ,QAAQ,KAAK,CAAE;AAAA,MACnG,OAAO,QAAQ,QAAQ,IAAK,QAAQ,QAAQ,KAAK;AAAA,MACjD,QAAQ,QAAQ,QAAQ,IAAK,QAAQ,QAAQ,KAAK;AAAA,MAClD,YAAY,QAAQ,QAAQ;AAAA,MAC5B,OAAO,QAAQ,QAAQ;AAAA,IACxB;AAIH,mBAAe,MAAM;AAErB,QAAI,OAAO,YAAY,SAAS,OAAO,QAAQ;AAC7C,cAAQ,MAAM,2BAA2B;AAAA,IAC1C;AAID,cAAU,OAAO;AAIjB,QAAI,UAAU,OACZ,UAAU,OACV,WAAW;AAEb,YAAQ,OAAO,YAAU;AAAA,MACvB,KAAK;AACH,kBAAU;AACV,kBAAU;AACV;AAAA,MAEF,KAAK;AACH,kBAAU;AACV;AAAA,MAEF,KAAK;AACH,kBAAU;AACV;AAAA,MAEF,KAAK;AACH;AAAA,MAEF,KAAK;AACH,kBAAU;AACV,mBAAW;AACX;AAAA,MAEF,KAAK;AACH,mBAAW;AACX;AAAA,IACH;AAID,UAAM,YAAY,IAAI,WAAW,OAAO,QAAQ,OAAO,SAAS,CAAC;AACjE,UAAM,SAAS,SAAS,SAAS,SAAS,QAAQ,QAAQ,OAAO;AACjE,eAAW,WAAW,OAAO,OAAO,OAAO,QAAQ,OAAO,YAAY,OAAO,QAAQ;AAErF,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO,OAAO;AAAA,MACd,QAAQ,OAAO;AAAA,MACf,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,WAAWC,MAAwB;AAAA,IACpC;AAAA,EACF;AACH;;"}