{"version": 3, "file": "VTKLoader.cjs", "sources": ["../../src/loaders/VTKLoader.js"], "sourcesContent": ["import { BufferAttribute, BufferGeometry, FileLoader, Float32BufferAttribute, Loader, LoaderUtils } from 'three'\nimport { unzlibSync } from 'fflate'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\nclass VTKLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    function parseASCII(data) {\n      // connectivity of the triangles\n      var indices = []\n\n      // triangles vertices\n      var positions = []\n\n      // red, green, blue colors in the range 0 to 1\n      var colors = []\n\n      // normal vector, one per vertex\n      var normals = []\n\n      var result\n\n      // pattern for detecting the end of a number sequence\n      var patWord = /^[^\\d.\\s-]+/\n\n      // pattern for reading vertices, 3 floats or integers\n      var pat3Floats = /(\\-?\\d+\\.?[\\d\\-\\+e]*)\\s+(\\-?\\d+\\.?[\\d\\-\\+e]*)\\s+(\\-?\\d+\\.?[\\d\\-\\+e]*)/g\n\n      // pattern for connectivity, an integer followed by any number of ints\n      // the first integer is the number of polygon nodes\n      var patConnectivity = /^(\\d+)\\s+([\\s\\d]*)/\n\n      // indicates start of vertex data section\n      var patPOINTS = /^POINTS /\n\n      // indicates start of polygon connectivity section\n      var patPOLYGONS = /^POLYGONS /\n\n      // indicates start of triangle strips section\n      var patTRIANGLE_STRIPS = /^TRIANGLE_STRIPS /\n\n      // POINT_DATA number_of_values\n      var patPOINT_DATA = /^POINT_DATA[ ]+(\\d+)/\n\n      // CELL_DATA number_of_polys\n      var patCELL_DATA = /^CELL_DATA[ ]+(\\d+)/\n\n      // Start of color section\n      var patCOLOR_SCALARS = /^COLOR_SCALARS[ ]+(\\w+)[ ]+3/\n\n      // NORMALS Normals float\n      var patNORMALS = /^NORMALS[ ]+(\\w+)[ ]+(\\w+)/\n\n      var inPointsSection = false\n      var inPolygonsSection = false\n      var inTriangleStripSection = false\n      var inPointDataSection = false\n      var inCellDataSection = false\n      var inColorSection = false\n      var inNormalsSection = false\n\n      var lines = data.split('\\n')\n\n      for (var i in lines) {\n        var line = lines[i].trim()\n\n        if (line.indexOf('DATASET') === 0) {\n          var dataset = line.split(' ')[1]\n\n          if (dataset !== 'POLYDATA') throw new Error('Unsupported DATASET type: ' + dataset)\n        } else if (inPointsSection) {\n          // get the vertices\n          while ((result = pat3Floats.exec(line)) !== null) {\n            if (patWord.exec(line) !== null) break\n\n            var x = parseFloat(result[1])\n            var y = parseFloat(result[2])\n            var z = parseFloat(result[3])\n            positions.push(x, y, z)\n          }\n        } else if (inPolygonsSection) {\n          if ((result = patConnectivity.exec(line)) !== null) {\n            // numVertices i0 i1 i2 ...\n            var numVertices = parseInt(result[1])\n            var inds = result[2].split(/\\s+/)\n\n            if (numVertices >= 3) {\n              var i0 = parseInt(inds[0])\n              var i1, i2\n              var k = 1\n              // split the polygon in numVertices - 2 triangles\n              for (var j = 0; j < numVertices - 2; ++j) {\n                i1 = parseInt(inds[k])\n                i2 = parseInt(inds[k + 1])\n                indices.push(i0, i1, i2)\n                k++\n              }\n            }\n          }\n        } else if (inTriangleStripSection) {\n          if ((result = patConnectivity.exec(line)) !== null) {\n            // numVertices i0 i1 i2 ...\n            var numVertices = parseInt(result[1])\n            var inds = result[2].split(/\\s+/)\n\n            if (numVertices >= 3) {\n              var i0, i1, i2\n              // split the polygon in numVertices - 2 triangles\n              for (var j = 0; j < numVertices - 2; j++) {\n                if (j % 2 === 1) {\n                  i0 = parseInt(inds[j])\n                  i1 = parseInt(inds[j + 2])\n                  i2 = parseInt(inds[j + 1])\n                  indices.push(i0, i1, i2)\n                } else {\n                  i0 = parseInt(inds[j])\n                  i1 = parseInt(inds[j + 1])\n                  i2 = parseInt(inds[j + 2])\n                  indices.push(i0, i1, i2)\n                }\n              }\n            }\n          }\n        } else if (inPointDataSection || inCellDataSection) {\n          if (inColorSection) {\n            // Get the colors\n\n            while ((result = pat3Floats.exec(line)) !== null) {\n              if (patWord.exec(line) !== null) break\n\n              var r = parseFloat(result[1])\n              var g = parseFloat(result[2])\n              var b = parseFloat(result[3])\n              colors.push(r, g, b)\n            }\n          } else if (inNormalsSection) {\n            // Get the normal vectors\n\n            while ((result = pat3Floats.exec(line)) !== null) {\n              if (patWord.exec(line) !== null) break\n\n              var nx = parseFloat(result[1])\n              var ny = parseFloat(result[2])\n              var nz = parseFloat(result[3])\n              normals.push(nx, ny, nz)\n            }\n          }\n        }\n\n        if (patPOLYGONS.exec(line) !== null) {\n          inPolygonsSection = true\n          inPointsSection = false\n          inTriangleStripSection = false\n        } else if (patPOINTS.exec(line) !== null) {\n          inPolygonsSection = false\n          inPointsSection = true\n          inTriangleStripSection = false\n        } else if (patTRIANGLE_STRIPS.exec(line) !== null) {\n          inPolygonsSection = false\n          inPointsSection = false\n          inTriangleStripSection = true\n        } else if (patPOINT_DATA.exec(line) !== null) {\n          inPointDataSection = true\n          inPointsSection = false\n          inPolygonsSection = false\n          inTriangleStripSection = false\n        } else if (patCELL_DATA.exec(line) !== null) {\n          inCellDataSection = true\n          inPointsSection = false\n          inPolygonsSection = false\n          inTriangleStripSection = false\n        } else if (patCOLOR_SCALARS.exec(line) !== null) {\n          inColorSection = true\n          inNormalsSection = false\n          inPointsSection = false\n          inPolygonsSection = false\n          inTriangleStripSection = false\n        } else if (patNORMALS.exec(line) !== null) {\n          inNormalsSection = true\n          inColorSection = false\n          inPointsSection = false\n          inPolygonsSection = false\n          inTriangleStripSection = false\n        }\n      }\n\n      var geometry = new BufferGeometry()\n      geometry.setIndex(indices)\n      geometry.setAttribute('position', new Float32BufferAttribute(positions, 3))\n\n      if (normals.length === positions.length) {\n        geometry.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n      }\n\n      if (colors.length !== indices.length) {\n        // stagger\n\n        if (colors.length === positions.length) {\n          geometry.setAttribute('color', new Float32BufferAttribute(colors, 3))\n        }\n      } else {\n        // cell\n\n        geometry = geometry.toNonIndexed()\n        var numTriangles = geometry.attributes.position.count / 3\n\n        if (colors.length === numTriangles * 3) {\n          var newColors = []\n\n          for (var i = 0; i < numTriangles; i++) {\n            var r = colors[3 * i + 0]\n            var g = colors[3 * i + 1]\n            var b = colors[3 * i + 2]\n\n            newColors.push(r, g, b)\n            newColors.push(r, g, b)\n            newColors.push(r, g, b)\n          }\n\n          geometry.setAttribute('color', new Float32BufferAttribute(newColors, 3))\n        }\n      }\n\n      return geometry\n    }\n\n    function parseBinary(data) {\n      var count, pointIndex, i, numberOfPoints, s\n      var buffer = new Uint8Array(data)\n      var dataView = new DataView(data)\n\n      // Points and normals, by default, are empty\n      var points = []\n      var normals = []\n      var indices = []\n\n      // Going to make a big array of strings\n      var vtk = []\n      var index = 0\n\n      function findString(buffer, start) {\n        var index = start\n        var c = buffer[index]\n        var s = []\n        while (c !== 10) {\n          s.push(String.fromCharCode(c))\n          index++\n          c = buffer[index]\n        }\n\n        return { start: start, end: index, next: index + 1, parsedString: s.join('') }\n      }\n\n      var state, line\n\n      while (true) {\n        // Get a string\n        state = findString(buffer, index)\n        line = state.parsedString\n\n        if (line.indexOf('DATASET') === 0) {\n          var dataset = line.split(' ')[1]\n\n          if (dataset !== 'POLYDATA') throw new Error('Unsupported DATASET type: ' + dataset)\n        } else if (line.indexOf('POINTS') === 0) {\n          vtk.push(line)\n          // Add the points\n          numberOfPoints = parseInt(line.split(' ')[1], 10)\n\n          // Each point is 3 4-byte floats\n          count = numberOfPoints * 4 * 3\n\n          points = new Float32Array(numberOfPoints * 3)\n\n          pointIndex = state.next\n          for (i = 0; i < numberOfPoints; i++) {\n            points[3 * i] = dataView.getFloat32(pointIndex, false)\n            points[3 * i + 1] = dataView.getFloat32(pointIndex + 4, false)\n            points[3 * i + 2] = dataView.getFloat32(pointIndex + 8, false)\n            pointIndex = pointIndex + 12\n          }\n\n          // increment our next pointer\n          state.next = state.next + count + 1\n        } else if (line.indexOf('TRIANGLE_STRIPS') === 0) {\n          var numberOfStrips = parseInt(line.split(' ')[1], 10)\n          var size = parseInt(line.split(' ')[2], 10)\n          // 4 byte integers\n          count = size * 4\n\n          indices = new Uint32Array(3 * size - 9 * numberOfStrips)\n          var indicesIndex = 0\n\n          pointIndex = state.next\n          for (i = 0; i < numberOfStrips; i++) {\n            // For each strip, read the first value, then record that many more points\n            var indexCount = dataView.getInt32(pointIndex, false)\n            var strip = []\n            pointIndex += 4\n            for (s = 0; s < indexCount; s++) {\n              strip.push(dataView.getInt32(pointIndex, false))\n              pointIndex += 4\n            }\n\n            // retrieves the n-2 triangles from the triangle strip\n            for (var j = 0; j < indexCount - 2; j++) {\n              if (j % 2) {\n                indices[indicesIndex++] = strip[j]\n                indices[indicesIndex++] = strip[j + 2]\n                indices[indicesIndex++] = strip[j + 1]\n              } else {\n                indices[indicesIndex++] = strip[j]\n                indices[indicesIndex++] = strip[j + 1]\n                indices[indicesIndex++] = strip[j + 2]\n              }\n            }\n          }\n\n          // increment our next pointer\n          state.next = state.next + count + 1\n        } else if (line.indexOf('POLYGONS') === 0) {\n          var numberOfStrips = parseInt(line.split(' ')[1], 10)\n          var size = parseInt(line.split(' ')[2], 10)\n          // 4 byte integers\n          count = size * 4\n\n          indices = new Uint32Array(3 * size - 9 * numberOfStrips)\n          var indicesIndex = 0\n\n          pointIndex = state.next\n          for (i = 0; i < numberOfStrips; i++) {\n            // For each strip, read the first value, then record that many more points\n            var indexCount = dataView.getInt32(pointIndex, false)\n            var strip = []\n            pointIndex += 4\n            for (s = 0; s < indexCount; s++) {\n              strip.push(dataView.getInt32(pointIndex, false))\n              pointIndex += 4\n            }\n\n            // divide the polygon in n-2 triangle\n            for (var j = 1; j < indexCount - 1; j++) {\n              indices[indicesIndex++] = strip[0]\n              indices[indicesIndex++] = strip[j]\n              indices[indicesIndex++] = strip[j + 1]\n            }\n          }\n\n          // increment our next pointer\n          state.next = state.next + count + 1\n        } else if (line.indexOf('POINT_DATA') === 0) {\n          numberOfPoints = parseInt(line.split(' ')[1], 10)\n\n          // Grab the next line\n          state = findString(buffer, state.next)\n\n          // Now grab the binary data\n          count = numberOfPoints * 4 * 3\n\n          normals = new Float32Array(numberOfPoints * 3)\n          pointIndex = state.next\n          for (i = 0; i < numberOfPoints; i++) {\n            normals[3 * i] = dataView.getFloat32(pointIndex, false)\n            normals[3 * i + 1] = dataView.getFloat32(pointIndex + 4, false)\n            normals[3 * i + 2] = dataView.getFloat32(pointIndex + 8, false)\n            pointIndex += 12\n          }\n\n          // Increment past our data\n          state.next = state.next + count\n        }\n\n        // Increment index\n        index = state.next\n\n        if (index >= buffer.byteLength) {\n          break\n        }\n      }\n\n      var geometry = new BufferGeometry()\n      geometry.setIndex(new BufferAttribute(indices, 1))\n      geometry.setAttribute('position', new BufferAttribute(points, 3))\n\n      if (normals.length === points.length) {\n        geometry.setAttribute('normal', new BufferAttribute(normals, 3))\n      }\n\n      return geometry\n    }\n\n    function Float32Concat(first, second) {\n      const firstLength = first.length,\n        result = new Float32Array(firstLength + second.length)\n\n      result.set(first)\n      result.set(second, firstLength)\n\n      return result\n    }\n\n    function Int32Concat(first, second) {\n      var firstLength = first.length,\n        result = new Int32Array(firstLength + second.length)\n\n      result.set(first)\n      result.set(second, firstLength)\n\n      return result\n    }\n\n    function parseXML(stringFile) {\n      // Changes XML to JSON, based on https://davidwalsh.name/convert-xml-json\n\n      function xmlToJson(xml) {\n        // Create the return object\n        var obj = {}\n\n        if (xml.nodeType === 1) {\n          // element\n\n          // do attributes\n\n          if (xml.attributes) {\n            if (xml.attributes.length > 0) {\n              obj['attributes'] = {}\n\n              for (var j = 0; j < xml.attributes.length; j++) {\n                var attribute = xml.attributes.item(j)\n                obj['attributes'][attribute.nodeName] = attribute.nodeValue.trim()\n              }\n            }\n          }\n        } else if (xml.nodeType === 3) {\n          // text\n\n          obj = xml.nodeValue.trim()\n        }\n\n        // do children\n        if (xml.hasChildNodes()) {\n          for (var i = 0; i < xml.childNodes.length; i++) {\n            var item = xml.childNodes.item(i)\n            var nodeName = item.nodeName\n\n            if (typeof obj[nodeName] === 'undefined') {\n              var tmp = xmlToJson(item)\n\n              if (tmp !== '') obj[nodeName] = tmp\n            } else {\n              if (typeof obj[nodeName].push === 'undefined') {\n                var old = obj[nodeName]\n                obj[nodeName] = [old]\n              }\n\n              var tmp = xmlToJson(item)\n\n              if (tmp !== '') obj[nodeName].push(tmp)\n            }\n          }\n        }\n\n        return obj\n      }\n\n      // Taken from Base64-js\n      function Base64toByteArray(b64) {\n        var Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n        var i\n        var lookup = []\n        var revLookup = []\n        var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\n        var len = code.length\n\n        for (i = 0; i < len; i++) {\n          lookup[i] = code[i]\n        }\n\n        for (i = 0; i < len; ++i) {\n          revLookup[code.charCodeAt(i)] = i\n        }\n\n        revLookup['-'.charCodeAt(0)] = 62\n        revLookup['_'.charCodeAt(0)] = 63\n\n        var j, l, tmp, placeHolders, arr\n        var len = b64.length\n\n        if (len % 4 > 0) {\n          throw new Error('Invalid string. Length must be a multiple of 4')\n        }\n\n        placeHolders = b64[len - 2] === '=' ? 2 : b64[len - 1] === '=' ? 1 : 0\n        arr = new Arr((len * 3) / 4 - placeHolders)\n        l = placeHolders > 0 ? len - 4 : len\n\n        var L = 0\n\n        for (i = 0, j = 0; i < l; i += 4, j += 3) {\n          tmp =\n            (revLookup[b64.charCodeAt(i)] << 18) |\n            (revLookup[b64.charCodeAt(i + 1)] << 12) |\n            (revLookup[b64.charCodeAt(i + 2)] << 6) |\n            revLookup[b64.charCodeAt(i + 3)]\n          arr[L++] = (tmp & 0xff0000) >> 16\n          arr[L++] = (tmp & 0xff00) >> 8\n          arr[L++] = tmp & 0xff\n        }\n\n        if (placeHolders === 2) {\n          tmp = (revLookup[b64.charCodeAt(i)] << 2) | (revLookup[b64.charCodeAt(i + 1)] >> 4)\n          arr[L++] = tmp & 0xff\n        } else if (placeHolders === 1) {\n          tmp =\n            (revLookup[b64.charCodeAt(i)] << 10) |\n            (revLookup[b64.charCodeAt(i + 1)] << 4) |\n            (revLookup[b64.charCodeAt(i + 2)] >> 2)\n          arr[L++] = (tmp >> 8) & 0xff\n          arr[L++] = tmp & 0xff\n        }\n\n        return arr\n      }\n\n      function parseDataArray(ele, compressed) {\n        var numBytes = 0\n\n        if (json.attributes.header_type === 'UInt64') {\n          numBytes = 8\n        } else if (json.attributes.header_type === 'UInt32') {\n          numBytes = 4\n        }\n\n        // Check the format\n        if (ele.attributes.format === 'binary' && compressed) {\n          var rawData, content, byteData, blocks, cSizeStart, headerSize, padding, dataOffsets, currentOffset\n\n          if (ele.attributes.type === 'Float32') {\n            var txt = new Float32Array()\n          } else if (ele.attributes.type === 'Int64') {\n            var txt = new Int32Array()\n          }\n\n          // VTP data with the header has the following structure:\n          // [#blocks][#u-size][#p-size][#c-size-1][#c-size-2]...[#c-size-#blocks][DATA]\n          //\n          // Each token is an integer value whose type is specified by \"header_type\" at the top of the file (UInt32 if no type specified). The token meanings are:\n          // [#blocks] = Number of blocks\n          // [#u-size] = Block size before compression\n          // [#p-size] = Size of last partial block (zero if it not needed)\n          // [#c-size-i] = Size in bytes of block i after compression\n          //\n          // The [DATA] portion stores contiguously every block appended together. The offset from the beginning of the data section to the beginning of a block is\n          // computed by summing the compressed block sizes from preceding blocks according to the header.\n\n          rawData = ele['#text']\n\n          byteData = Base64toByteArray(rawData)\n\n          blocks = byteData[0]\n          for (var i = 1; i < numBytes - 1; i++) {\n            blocks = blocks | (byteData[i] << (i * numBytes))\n          }\n\n          headerSize = (blocks + 3) * numBytes\n          padding = headerSize % 3 > 0 ? 3 - (headerSize % 3) : 0\n          headerSize = headerSize + padding\n\n          dataOffsets = []\n          currentOffset = headerSize\n          dataOffsets.push(currentOffset)\n\n          // Get the blocks sizes after the compression.\n          // There are three blocks before c-size-i, so we skip 3*numBytes\n          cSizeStart = 3 * numBytes\n\n          for (var i = 0; i < blocks; i++) {\n            var currentBlockSize = byteData[i * numBytes + cSizeStart]\n\n            for (var j = 1; j < numBytes - 1; j++) {\n              // Each data point consists of 8 bytes regardless of the header type\n              currentBlockSize = currentBlockSize | (byteData[i * numBytes + cSizeStart + j] << (j * 8))\n            }\n\n            currentOffset = currentOffset + currentBlockSize\n            dataOffsets.push(currentOffset)\n          }\n\n          for (var i = 0; i < dataOffsets.length - 1; i++) {\n            var data = unzlibSync(byteData.slice(dataOffsets[i], dataOffsets[i + 1]))\n            content = data.buffer\n\n            if (ele.attributes.type === 'Float32') {\n              content = new Float32Array(content)\n              txt = Float32Concat(txt, content)\n            } else if (ele.attributes.type === 'Int64') {\n              content = new Int32Array(content)\n              txt = Int32Concat(txt, content)\n            }\n          }\n\n          delete ele['#text']\n\n          if (ele.attributes.type === 'Int64') {\n            if (ele.attributes.format === 'binary') {\n              txt = txt.filter(function (el, idx) {\n                if (idx % 2 !== 1) return true\n              })\n            }\n          }\n        } else {\n          if (ele.attributes.format === 'binary' && !compressed) {\n            var content = Base64toByteArray(ele['#text'])\n\n            //  VTP data for the uncompressed case has the following structure:\n            // [#bytes][DATA]\n            // where \"[#bytes]\" is an integer value specifying the number of bytes in the block of data following it.\n            content = content.slice(numBytes).buffer\n          } else {\n            if (ele['#text']) {\n              var content = ele['#text'].split(/\\s+/).filter(function (el) {\n                if (el !== '') return el\n              })\n            } else {\n              var content = new Int32Array(0).buffer\n            }\n          }\n\n          delete ele['#text']\n\n          // Get the content and optimize it\n          if (ele.attributes.type === 'Float32') {\n            var txt = new Float32Array(content)\n          } else if (ele.attributes.type === 'Int32') {\n            var txt = new Int32Array(content)\n          } else if (ele.attributes.type === 'Int64') {\n            var txt = new Int32Array(content)\n\n            if (ele.attributes.format === 'binary') {\n              txt = txt.filter(function (el, idx) {\n                if (idx % 2 !== 1) return true\n              })\n            }\n          }\n        } // endif ( ele.attributes.format === 'binary' && compressed )\n\n        return txt\n      }\n\n      // Main part\n      // Get Dom\n      var dom = null\n\n      if (window.DOMParser) {\n        try {\n          dom = new DOMParser().parseFromString(stringFile, 'text/xml')\n        } catch (e) {\n          dom = null\n        }\n      } else if (window.ActiveXObject) {\n        try {\n          dom = new ActiveXObject('Microsoft.XMLDOM')\n          dom.async = false\n\n          if (!(dom.loadXML(/* xml */))) {\n            throw new Error(dom.parseError.reason + dom.parseError.srcText)\n          }\n        } catch (e) {\n          dom = null\n        }\n      } else {\n        throw new Error('Cannot parse xml string!')\n      }\n\n      // Get the doc\n      var doc = dom.documentElement\n      // Convert to json\n      var json = xmlToJson(doc)\n      var points = []\n      var normals = []\n      var indices = []\n\n      if (json.PolyData) {\n        var piece = json.PolyData.Piece\n        var compressed = json.attributes.hasOwnProperty('compressor')\n\n        // Can be optimized\n        // Loop through the sections\n        var sections = ['PointData', 'Points', 'Strips', 'Polys'] // +['CellData', 'Verts', 'Lines'];\n        var sectionIndex = 0,\n          numberOfSections = sections.length\n\n        while (sectionIndex < numberOfSections) {\n          var section = piece[sections[sectionIndex]]\n\n          // If it has a DataArray in it\n\n          if (section && section.DataArray) {\n            // Depending on the number of DataArrays\n\n            if (Object.prototype.toString.call(section.DataArray) === '[object Array]') {\n              var arr = section.DataArray\n            } else {\n              var arr = [section.DataArray]\n            }\n\n            var dataArrayIndex = 0,\n              numberOfDataArrays = arr.length\n\n            while (dataArrayIndex < numberOfDataArrays) {\n              // Parse the DataArray\n              if ('#text' in arr[dataArrayIndex] && arr[dataArrayIndex]['#text'].length > 0) {\n                arr[dataArrayIndex].text = parseDataArray(arr[dataArrayIndex], compressed)\n              }\n\n              dataArrayIndex++\n            }\n\n            switch (sections[sectionIndex]) {\n              // if iti is point data\n              case 'PointData':\n                var numberOfPoints = parseInt(piece.attributes.NumberOfPoints)\n                var normalsName = section.attributes.Normals\n\n                if (numberOfPoints > 0) {\n                  for (var i = 0, len = arr.length; i < len; i++) {\n                    if (normalsName === arr[i].attributes.Name) {\n                      var components = arr[i].attributes.NumberOfComponents\n                      normals = new Float32Array(numberOfPoints * components)\n                      normals.set(arr[i].text, 0)\n                    }\n                  }\n                }\n\n                break\n\n              // if it is points\n              case 'Points':\n                var numberOfPoints = parseInt(piece.attributes.NumberOfPoints)\n\n                if (numberOfPoints > 0) {\n                  var components = section.DataArray.attributes.NumberOfComponents\n                  points = new Float32Array(numberOfPoints * components)\n                  points.set(section.DataArray.text, 0)\n                }\n\n                break\n\n              // if it is strips\n              case 'Strips':\n                var numberOfStrips = parseInt(piece.attributes.NumberOfStrips)\n\n                if (numberOfStrips > 0) {\n                  var connectivity = new Int32Array(section.DataArray[0].text.length)\n                  var offset = new Int32Array(section.DataArray[1].text.length)\n                  connectivity.set(section.DataArray[0].text, 0)\n                  offset.set(section.DataArray[1].text, 0)\n\n                  var size = numberOfStrips + connectivity.length\n                  indices = new Uint32Array(3 * size - 9 * numberOfStrips)\n\n                  var indicesIndex = 0\n\n                  for (var i = 0, len = numberOfStrips; i < len; i++) {\n                    var strip = []\n\n                    for (var s = 0, len1 = offset[i], len0 = 0; s < len1 - len0; s++) {\n                      strip.push(connectivity[s])\n\n                      if (i > 0) len0 = offset[i - 1]\n                    }\n\n                    for (var j = 0, len1 = offset[i], len0 = 0; j < len1 - len0 - 2; j++) {\n                      if (j % 2) {\n                        indices[indicesIndex++] = strip[j]\n                        indices[indicesIndex++] = strip[j + 2]\n                        indices[indicesIndex++] = strip[j + 1]\n                      } else {\n                        indices[indicesIndex++] = strip[j]\n                        indices[indicesIndex++] = strip[j + 1]\n                        indices[indicesIndex++] = strip[j + 2]\n                      }\n\n                      if (i > 0) len0 = offset[i - 1]\n                    }\n                  }\n                }\n\n                break\n\n              // if it is polys\n              case 'Polys':\n                var numberOfPolys = parseInt(piece.attributes.NumberOfPolys)\n\n                if (numberOfPolys > 0) {\n                  var connectivity = new Int32Array(section.DataArray[0].text.length)\n                  var offset = new Int32Array(section.DataArray[1].text.length)\n                  connectivity.set(section.DataArray[0].text, 0)\n                  offset.set(section.DataArray[1].text, 0)\n\n                  var size = numberOfPolys + connectivity.length\n                  indices = new Uint32Array(3 * size - 9 * numberOfPolys)\n                  var indicesIndex = 0,\n                    connectivityIndex = 0\n                  var i = 0,\n                    len = numberOfPolys,\n                    len0 = 0\n\n                  while (i < len) {\n                    var poly = []\n                    var s = 0,\n                      len1 = offset[i]\n\n                    while (s < len1 - len0) {\n                      poly.push(connectivity[connectivityIndex++])\n                      s++\n                    }\n\n                    var j = 1\n\n                    while (j < len1 - len0 - 1) {\n                      indices[indicesIndex++] = poly[0]\n                      indices[indicesIndex++] = poly[j]\n                      indices[indicesIndex++] = poly[j + 1]\n                      j++\n                    }\n\n                    i++\n                    len0 = offset[i - 1]\n                  }\n                }\n\n                break\n\n              default:\n                break\n            }\n          }\n\n          sectionIndex++\n        }\n\n        var geometry = new BufferGeometry()\n        geometry.setIndex(new BufferAttribute(indices, 1))\n        geometry.setAttribute('position', new BufferAttribute(points, 3))\n\n        if (normals.length === points.length) {\n          geometry.setAttribute('normal', new BufferAttribute(normals, 3))\n        }\n\n        return geometry\n      } else {\n        throw new Error('Unsupported DATASET type')\n      }\n    }\n\n    // get the 5 first lines of the files to check if there is the key word binary\n    var meta = decodeText(new Uint8Array(data, 0, 250)).split('\\n')\n\n    if (meta[0].indexOf('xml') !== -1) {\n      return parseXML(decodeText(data))\n    } else if (meta[2].includes('ASCII')) {\n      return parseASCII(decodeText(data))\n    } else {\n      return parseBinary(data)\n    }\n  }\n}\n\nexport { VTKLoader }\n"], "names": ["Loader", "<PERSON><PERSON><PERSON><PERSON>", "data", "BufferGeometry", "Float32BufferAttribute", "buffer", "index", "s", "BufferAttribute", "j", "i", "len", "arr", "compressed", "unzlibSync", "decodeText"], "mappings": ";;;;;AAIA,MAAM,kBAAkBA,MAAAA,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACd;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAIC,iBAAW,MAAM,OAAO;AAC3C,WAAO,QAAQ,MAAM,IAAI;AACzB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,MAAM,aAAa;AAC3C,WAAO,mBAAmB,MAAM,eAAe;AAC/C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,iBAAO,MAAM,MAAM,IAAI,CAAC;AAAA,QACzB,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,MAAM;AACV,aAAS,WAAWC,OAAM;AAExB,UAAI,UAAU,CAAE;AAGhB,UAAI,YAAY,CAAE;AAGlB,UAAI,SAAS,CAAE;AAGf,UAAI,UAAU,CAAE;AAEhB,UAAI;AAGJ,UAAI,UAAU;AAGd,UAAI,aAAa;AAIjB,UAAI,kBAAkB;AAGtB,UAAI,YAAY;AAGhB,UAAI,cAAc;AAGlB,UAAI,qBAAqB;AAGzB,UAAI,gBAAgB;AAGpB,UAAI,eAAe;AAGnB,UAAI,mBAAmB;AAGvB,UAAI,aAAa;AAEjB,UAAI,kBAAkB;AACtB,UAAI,oBAAoB;AACxB,UAAI,yBAAyB;AAC7B,UAAI,qBAAqB;AACzB,UAAI,oBAAoB;AACxB,UAAI,iBAAiB;AACrB,UAAI,mBAAmB;AAEvB,UAAI,QAAQA,MAAK,MAAM,IAAI;AAE3B,eAAS,KAAK,OAAO;AACnB,YAAI,OAAO,MAAM,CAAC,EAAE,KAAM;AAE1B,YAAI,KAAK,QAAQ,SAAS,MAAM,GAAG;AACjC,cAAI,UAAU,KAAK,MAAM,GAAG,EAAE,CAAC;AAE/B,cAAI,YAAY;AAAY,kBAAM,IAAI,MAAM,+BAA+B,OAAO;AAAA,QACnF,WAAU,iBAAiB;AAE1B,kBAAQ,SAAS,WAAW,KAAK,IAAI,OAAO,MAAM;AAChD,gBAAI,QAAQ,KAAK,IAAI,MAAM;AAAM;AAEjC,gBAAI,IAAI,WAAW,OAAO,CAAC,CAAC;AAC5B,gBAAI,IAAI,WAAW,OAAO,CAAC,CAAC;AAC5B,gBAAI,IAAI,WAAW,OAAO,CAAC,CAAC;AAC5B,sBAAU,KAAK,GAAG,GAAG,CAAC;AAAA,UACvB;AAAA,QACF,WAAU,mBAAmB;AAC5B,eAAK,SAAS,gBAAgB,KAAK,IAAI,OAAO,MAAM;AAElD,gBAAI,cAAc,SAAS,OAAO,CAAC,CAAC;AACpC,gBAAI,OAAO,OAAO,CAAC,EAAE,MAAM,KAAK;AAEhC,gBAAI,eAAe,GAAG;AACpB,kBAAI,KAAK,SAAS,KAAK,CAAC,CAAC;AACzB,kBAAI,IAAI;AACR,kBAAI,IAAI;AAER,uBAAS,IAAI,GAAG,IAAI,cAAc,GAAG,EAAE,GAAG;AACxC,qBAAK,SAAS,KAAK,CAAC,CAAC;AACrB,qBAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AACzB,wBAAQ,KAAK,IAAI,IAAI,EAAE;AACvB;AAAA,cACD;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAU,wBAAwB;AACjC,eAAK,SAAS,gBAAgB,KAAK,IAAI,OAAO,MAAM;AAElD,gBAAI,cAAc,SAAS,OAAO,CAAC,CAAC;AACpC,gBAAI,OAAO,OAAO,CAAC,EAAE,MAAM,KAAK;AAEhC,gBAAI,eAAe,GAAG;AACpB,kBAAI,IAAI,IAAI;AAEZ,uBAAS,IAAI,GAAG,IAAI,cAAc,GAAG,KAAK;AACxC,oBAAI,IAAI,MAAM,GAAG;AACf,uBAAK,SAAS,KAAK,CAAC,CAAC;AACrB,uBAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AACzB,uBAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AACzB,0BAAQ,KAAK,IAAI,IAAI,EAAE;AAAA,gBACzC,OAAuB;AACL,uBAAK,SAAS,KAAK,CAAC,CAAC;AACrB,uBAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AACzB,uBAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AACzB,0BAAQ,KAAK,IAAI,IAAI,EAAE;AAAA,gBACxB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACX,WAAmB,sBAAsB,mBAAmB;AAClD,cAAI,gBAAgB;AAGlB,oBAAQ,SAAS,WAAW,KAAK,IAAI,OAAO,MAAM;AAChD,kBAAI,QAAQ,KAAK,IAAI,MAAM;AAAM;AAEjC,kBAAI,IAAI,WAAW,OAAO,CAAC,CAAC;AAC5B,kBAAI,IAAI,WAAW,OAAO,CAAC,CAAC;AAC5B,kBAAI,IAAI,WAAW,OAAO,CAAC,CAAC;AAC5B,qBAAO,KAAK,GAAG,GAAG,CAAC;AAAA,YACpB;AAAA,UACF,WAAU,kBAAkB;AAG3B,oBAAQ,SAAS,WAAW,KAAK,IAAI,OAAO,MAAM;AAChD,kBAAI,QAAQ,KAAK,IAAI,MAAM;AAAM;AAEjC,kBAAI,KAAK,WAAW,OAAO,CAAC,CAAC;AAC7B,kBAAI,KAAK,WAAW,OAAO,CAAC,CAAC;AAC7B,kBAAI,KAAK,WAAW,OAAO,CAAC,CAAC;AAC7B,sBAAQ,KAAK,IAAI,IAAI,EAAE;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AAED,YAAI,YAAY,KAAK,IAAI,MAAM,MAAM;AACnC,8BAAoB;AACpB,4BAAkB;AAClB,mCAAyB;AAAA,QAC1B,WAAU,UAAU,KAAK,IAAI,MAAM,MAAM;AACxC,8BAAoB;AACpB,4BAAkB;AAClB,mCAAyB;AAAA,QAC1B,WAAU,mBAAmB,KAAK,IAAI,MAAM,MAAM;AACjD,8BAAoB;AACpB,4BAAkB;AAClB,mCAAyB;AAAA,QAC1B,WAAU,cAAc,KAAK,IAAI,MAAM,MAAM;AAC5C,+BAAqB;AACrB,4BAAkB;AAClB,8BAAoB;AACpB,mCAAyB;AAAA,QAC1B,WAAU,aAAa,KAAK,IAAI,MAAM,MAAM;AAC3C,8BAAoB;AACpB,4BAAkB;AAClB,8BAAoB;AACpB,mCAAyB;AAAA,QAC1B,WAAU,iBAAiB,KAAK,IAAI,MAAM,MAAM;AAC/C,2BAAiB;AACjB,6BAAmB;AACnB,4BAAkB;AAClB,8BAAoB;AACpB,mCAAyB;AAAA,QAC1B,WAAU,WAAW,KAAK,IAAI,MAAM,MAAM;AACzC,6BAAmB;AACnB,2BAAiB;AACjB,4BAAkB;AAClB,8BAAoB;AACpB,mCAAyB;AAAA,QAC1B;AAAA,MACF;AAED,UAAI,WAAW,IAAIC,qBAAgB;AACnC,eAAS,SAAS,OAAO;AACzB,eAAS,aAAa,YAAY,IAAIC,MAAAA,uBAAuB,WAAW,CAAC,CAAC;AAE1E,UAAI,QAAQ,WAAW,UAAU,QAAQ;AACvC,iBAAS,aAAa,UAAU,IAAIA,MAAAA,uBAAuB,SAAS,CAAC,CAAC;AAAA,MACvE;AAED,UAAI,OAAO,WAAW,QAAQ,QAAQ;AAGpC,YAAI,OAAO,WAAW,UAAU,QAAQ;AACtC,mBAAS,aAAa,SAAS,IAAIA,MAAAA,uBAAuB,QAAQ,CAAC,CAAC;AAAA,QACrE;AAAA,MACT,OAAa;AAGL,mBAAW,SAAS,aAAc;AAClC,YAAI,eAAe,SAAS,WAAW,SAAS,QAAQ;AAExD,YAAI,OAAO,WAAW,eAAe,GAAG;AACtC,cAAI,YAAY,CAAE;AAElB,mBAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACrC,gBAAI,IAAI,OAAO,IAAI,IAAI,CAAC;AACxB,gBAAI,IAAI,OAAO,IAAI,IAAI,CAAC;AACxB,gBAAI,IAAI,OAAO,IAAI,IAAI,CAAC;AAExB,sBAAU,KAAK,GAAG,GAAG,CAAC;AACtB,sBAAU,KAAK,GAAG,GAAG,CAAC;AACtB,sBAAU,KAAK,GAAG,GAAG,CAAC;AAAA,UACvB;AAED,mBAAS,aAAa,SAAS,IAAIA,MAAAA,uBAAuB,WAAW,CAAC,CAAC;AAAA,QACxE;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,YAAYF,OAAM;AACzB,UAAI,OAAO,YAAY,GAAG,gBAAgB;AAC1C,UAAI,SAAS,IAAI,WAAWA,KAAI;AAChC,UAAI,WAAW,IAAI,SAASA,KAAI;AAGhC,UAAI,SAAS,CAAE;AACf,UAAI,UAAU,CAAE;AAChB,UAAI,UAAU,CAAE;AAIhB,UAAI,QAAQ;AAEZ,eAAS,WAAWG,SAAQ,OAAO;AACjC,YAAIC,SAAQ;AACZ,YAAI,IAAID,QAAOC,MAAK;AACpB,YAAIC,KAAI,CAAE;AACV,eAAO,MAAM,IAAI;AACf,UAAAA,GAAE,KAAK,OAAO,aAAa,CAAC,CAAC;AAC7B,UAAAD;AACA,cAAID,QAAOC,MAAK;AAAA,QACjB;AAED,eAAO,EAAE,OAAc,KAAKA,QAAO,MAAMA,SAAQ,GAAG,cAAcC,GAAE,KAAK,EAAE,EAAG;AAAA,MAC/E;AAED,UAAI,OAAO;AAEX,aAAO,MAAM;AAEX,gBAAQ,WAAW,QAAQ,KAAK;AAChC,eAAO,MAAM;AAEb,YAAI,KAAK,QAAQ,SAAS,MAAM,GAAG;AACjC,cAAI,UAAU,KAAK,MAAM,GAAG,EAAE,CAAC;AAE/B,cAAI,YAAY;AAAY,kBAAM,IAAI,MAAM,+BAA+B,OAAO;AAAA,QACnF,WAAU,KAAK,QAAQ,QAAQ,MAAM,GAAG;AAGvC,2BAAiB,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAGhD,kBAAQ,iBAAiB,IAAI;AAE7B,mBAAS,IAAI,aAAa,iBAAiB,CAAC;AAE5C,uBAAa,MAAM;AACnB,eAAK,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACnC,mBAAO,IAAI,CAAC,IAAI,SAAS,WAAW,YAAY,KAAK;AACrD,mBAAO,IAAI,IAAI,CAAC,IAAI,SAAS,WAAW,aAAa,GAAG,KAAK;AAC7D,mBAAO,IAAI,IAAI,CAAC,IAAI,SAAS,WAAW,aAAa,GAAG,KAAK;AAC7D,yBAAa,aAAa;AAAA,UAC3B;AAGD,gBAAM,OAAO,MAAM,OAAO,QAAQ;AAAA,QACnC,WAAU,KAAK,QAAQ,iBAAiB,MAAM,GAAG;AAChD,cAAI,iBAAiB,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AACpD,cAAI,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAE1C,kBAAQ,OAAO;AAEf,oBAAU,IAAI,YAAY,IAAI,OAAO,IAAI,cAAc;AACvD,cAAI,eAAe;AAEnB,uBAAa,MAAM;AACnB,eAAK,IAAI,GAAG,IAAI,gBAAgB,KAAK;AAEnC,gBAAI,aAAa,SAAS,SAAS,YAAY,KAAK;AACpD,gBAAI,QAAQ,CAAE;AACd,0BAAc;AACd,iBAAK,IAAI,GAAG,IAAI,YAAY,KAAK;AAC/B,oBAAM,KAAK,SAAS,SAAS,YAAY,KAAK,CAAC;AAC/C,4BAAc;AAAA,YACf;AAGD,qBAAS,IAAI,GAAG,IAAI,aAAa,GAAG,KAAK;AACvC,kBAAI,IAAI,GAAG;AACT,wBAAQ,cAAc,IAAI,MAAM,CAAC;AACjC,wBAAQ,cAAc,IAAI,MAAM,IAAI,CAAC;AACrC,wBAAQ,cAAc,IAAI,MAAM,IAAI,CAAC;AAAA,cACrD,OAAqB;AACL,wBAAQ,cAAc,IAAI,MAAM,CAAC;AACjC,wBAAQ,cAAc,IAAI,MAAM,IAAI,CAAC;AACrC,wBAAQ,cAAc,IAAI,MAAM,IAAI,CAAC;AAAA,cACtC;AAAA,YACF;AAAA,UACF;AAGD,gBAAM,OAAO,MAAM,OAAO,QAAQ;AAAA,QACnC,WAAU,KAAK,QAAQ,UAAU,MAAM,GAAG;AACzC,cAAI,iBAAiB,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AACpD,cAAI,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAE1C,kBAAQ,OAAO;AAEf,oBAAU,IAAI,YAAY,IAAI,OAAO,IAAI,cAAc;AACvD,cAAI,eAAe;AAEnB,uBAAa,MAAM;AACnB,eAAK,IAAI,GAAG,IAAI,gBAAgB,KAAK;AAEnC,gBAAI,aAAa,SAAS,SAAS,YAAY,KAAK;AACpD,gBAAI,QAAQ,CAAE;AACd,0BAAc;AACd,iBAAK,IAAI,GAAG,IAAI,YAAY,KAAK;AAC/B,oBAAM,KAAK,SAAS,SAAS,YAAY,KAAK,CAAC;AAC/C,4BAAc;AAAA,YACf;AAGD,qBAAS,IAAI,GAAG,IAAI,aAAa,GAAG,KAAK;AACvC,sBAAQ,cAAc,IAAI,MAAM,CAAC;AACjC,sBAAQ,cAAc,IAAI,MAAM,CAAC;AACjC,sBAAQ,cAAc,IAAI,MAAM,IAAI,CAAC;AAAA,YACtC;AAAA,UACF;AAGD,gBAAM,OAAO,MAAM,OAAO,QAAQ;AAAA,QACnC,WAAU,KAAK,QAAQ,YAAY,MAAM,GAAG;AAC3C,2BAAiB,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAGhD,kBAAQ,WAAW,QAAQ,MAAM,IAAI;AAGrC,kBAAQ,iBAAiB,IAAI;AAE7B,oBAAU,IAAI,aAAa,iBAAiB,CAAC;AAC7C,uBAAa,MAAM;AACnB,eAAK,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACnC,oBAAQ,IAAI,CAAC,IAAI,SAAS,WAAW,YAAY,KAAK;AACtD,oBAAQ,IAAI,IAAI,CAAC,IAAI,SAAS,WAAW,aAAa,GAAG,KAAK;AAC9D,oBAAQ,IAAI,IAAI,CAAC,IAAI,SAAS,WAAW,aAAa,GAAG,KAAK;AAC9D,0BAAc;AAAA,UACf;AAGD,gBAAM,OAAO,MAAM,OAAO;AAAA,QAC3B;AAGD,gBAAQ,MAAM;AAEd,YAAI,SAAS,OAAO,YAAY;AAC9B;AAAA,QACD;AAAA,MACF;AAED,UAAI,WAAW,IAAIJ,qBAAgB;AACnC,eAAS,SAAS,IAAIK,MAAe,gBAAC,SAAS,CAAC,CAAC;AACjD,eAAS,aAAa,YAAY,IAAIA,MAAAA,gBAAgB,QAAQ,CAAC,CAAC;AAEhE,UAAI,QAAQ,WAAW,OAAO,QAAQ;AACpC,iBAAS,aAAa,UAAU,IAAIA,MAAAA,gBAAgB,SAAS,CAAC,CAAC;AAAA,MAChE;AAED,aAAO;AAAA,IACR;AAED,aAAS,cAAc,OAAO,QAAQ;AACpC,YAAM,cAAc,MAAM,QACxB,SAAS,IAAI,aAAa,cAAc,OAAO,MAAM;AAEvD,aAAO,IAAI,KAAK;AAChB,aAAO,IAAI,QAAQ,WAAW;AAE9B,aAAO;AAAA,IACR;AAED,aAAS,YAAY,OAAO,QAAQ;AAClC,UAAI,cAAc,MAAM,QACtB,SAAS,IAAI,WAAW,cAAc,OAAO,MAAM;AAErD,aAAO,IAAI,KAAK;AAChB,aAAO,IAAI,QAAQ,WAAW;AAE9B,aAAO;AAAA,IACR;AAED,aAAS,SAAS,YAAY;AAG5B,eAAS,UAAU,KAAK;AAEtB,YAAI,MAAM,CAAE;AAEZ,YAAI,IAAI,aAAa,GAAG;AAKtB,cAAI,IAAI,YAAY;AAClB,gBAAI,IAAI,WAAW,SAAS,GAAG;AAC7B,kBAAI,YAAY,IAAI,CAAE;AAEtB,uBAASC,KAAI,GAAGA,KAAI,IAAI,WAAW,QAAQA,MAAK;AAC9C,oBAAI,YAAY,IAAI,WAAW,KAAKA,EAAC;AACrC,oBAAI,YAAY,EAAE,UAAU,QAAQ,IAAI,UAAU,UAAU,KAAM;AAAA,cACnE;AAAA,YACF;AAAA,UACF;AAAA,QACX,WAAmB,IAAI,aAAa,GAAG;AAG7B,gBAAM,IAAI,UAAU,KAAM;AAAA,QAC3B;AAGD,YAAI,IAAI,iBAAiB;AACvB,mBAASC,KAAI,GAAGA,KAAI,IAAI,WAAW,QAAQA,MAAK;AAC9C,gBAAI,OAAO,IAAI,WAAW,KAAKA,EAAC;AAChC,gBAAI,WAAW,KAAK;AAEpB,gBAAI,OAAO,IAAI,QAAQ,MAAM,aAAa;AACxC,kBAAI,MAAM,UAAU,IAAI;AAExB,kBAAI,QAAQ;AAAI,oBAAI,QAAQ,IAAI;AAAA,YAC9C,OAAmB;AACL,kBAAI,OAAO,IAAI,QAAQ,EAAE,SAAS,aAAa;AAC7C,oBAAI,MAAM,IAAI,QAAQ;AACtB,oBAAI,QAAQ,IAAI,CAAC,GAAG;AAAA,cACrB;AAED,kBAAI,MAAM,UAAU,IAAI;AAExB,kBAAI,QAAQ;AAAI,oBAAI,QAAQ,EAAE,KAAK,GAAG;AAAA,YACvC;AAAA,UACF;AAAA,QACF;AAED,eAAO;AAAA,MACR;AAGD,eAAS,kBAAkB,KAAK;AAC9B,YAAI,MAAM,OAAO,eAAe,cAAc,aAAa;AAC3D,YAAIA;AAEJ,YAAI,YAAY,CAAE;AAClB,YAAI,OAAO;AACX,YAAIC,OAAM,KAAK;AAEf,aAAKD,KAAI,GAAGA,KAAIC,MAAKD,MAAK;AAAA,QAEzB;AAED,aAAKA,KAAI,GAAGA,KAAIC,MAAK,EAAED,IAAG;AACxB,oBAAU,KAAK,WAAWA,EAAC,CAAC,IAAIA;AAAA,QACjC;AAED,kBAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAC/B,kBAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAE/B,YAAID,IAAG,GAAG,KAAK,cAAcG;AAC7B,YAAID,OAAM,IAAI;AAEd,YAAIA,OAAM,IAAI,GAAG;AACf,gBAAM,IAAI,MAAM,gDAAgD;AAAA,QACjE;AAED,uBAAe,IAAIA,OAAM,CAAC,MAAM,MAAM,IAAI,IAAIA,OAAM,CAAC,MAAM,MAAM,IAAI;AACrE,QAAAC,OAAM,IAAI,IAAKD,OAAM,IAAK,IAAI,YAAY;AAC1C,YAAI,eAAe,IAAIA,OAAM,IAAIA;AAEjC,YAAI,IAAI;AAER,aAAKD,KAAI,GAAGD,KAAI,GAAGC,KAAI,GAAGA,MAAK,GAAGD,MAAK,GAAG;AACxC,gBACG,UAAU,IAAI,WAAWC,EAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK,KACpC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK,IACrC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC;AACjC,UAAAE,KAAI,GAAG,KAAK,MAAM,aAAa;AAC/B,UAAAA,KAAI,GAAG,KAAK,MAAM,UAAW;AAC7B,UAAAA,KAAI,GAAG,IAAI,MAAM;AAAA,QAClB;AAED,YAAI,iBAAiB,GAAG;AACtB,gBAAO,UAAU,IAAI,WAAWF,EAAC,CAAC,KAAK,IAAM,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK;AACjF,UAAAE,KAAI,GAAG,IAAI,MAAM;AAAA,QAC3B,WAAmB,iBAAiB,GAAG;AAC7B,gBACG,UAAU,IAAI,WAAWF,EAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK,IACpC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK;AACvC,UAAAE,KAAI,GAAG,IAAK,OAAO,IAAK;AACxB,UAAAA,KAAI,GAAG,IAAI,MAAM;AAAA,QAClB;AAED,eAAOA;AAAA,MACR;AAED,eAAS,eAAe,KAAKC,aAAY;AACvC,YAAI,WAAW;AAEf,YAAI,KAAK,WAAW,gBAAgB,UAAU;AAC5C,qBAAW;AAAA,QACZ,WAAU,KAAK,WAAW,gBAAgB,UAAU;AACnD,qBAAW;AAAA,QACZ;AAGD,YAAI,IAAI,WAAW,WAAW,YAAYA,aAAY;AACpD,cAAI,SAAS,SAAS,UAAU,QAAQ,YAAY,YAAY,SAAS,aAAa;AAEtF,cAAI,IAAI,WAAW,SAAS,WAAW;AACrC,gBAAI,MAAM,IAAI,aAAc;AAAA,UAC7B,WAAU,IAAI,WAAW,SAAS,SAAS;AAC1C,gBAAI,MAAM,IAAI,WAAY;AAAA,UAC3B;AAcD,oBAAU,IAAI,OAAO;AAErB,qBAAW,kBAAkB,OAAO;AAEpC,mBAAS,SAAS,CAAC;AACnB,mBAASH,KAAI,GAAGA,KAAI,WAAW,GAAGA,MAAK;AACrC,qBAAS,SAAU,SAASA,EAAC,KAAMA,KAAI;AAAA,UACxC;AAED,wBAAc,SAAS,KAAK;AAC5B,oBAAU,aAAa,IAAI,IAAI,IAAK,aAAa,IAAK;AACtD,uBAAa,aAAa;AAE1B,wBAAc,CAAE;AAChB,0BAAgB;AAChB,sBAAY,KAAK,aAAa;AAI9B,uBAAa,IAAI;AAEjB,mBAASA,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAC/B,gBAAI,mBAAmB,SAASA,KAAI,WAAW,UAAU;AAEzD,qBAASD,KAAI,GAAGA,KAAI,WAAW,GAAGA,MAAK;AAErC,iCAAmB,mBAAoB,SAASC,KAAI,WAAW,aAAaD,EAAC,KAAMA,KAAI;AAAA,YACxF;AAED,4BAAgB,gBAAgB;AAChC,wBAAY,KAAK,aAAa;AAAA,UAC/B;AAED,mBAASC,KAAI,GAAGA,KAAI,YAAY,SAAS,GAAGA,MAAK;AAC/C,gBAAIR,QAAOY,OAAAA,WAAW,SAAS,MAAM,YAAYJ,EAAC,GAAG,YAAYA,KAAI,CAAC,CAAC,CAAC;AACxE,sBAAUR,MAAK;AAEf,gBAAI,IAAI,WAAW,SAAS,WAAW;AACrC,wBAAU,IAAI,aAAa,OAAO;AAClC,oBAAM,cAAc,KAAK,OAAO;AAAA,YACjC,WAAU,IAAI,WAAW,SAAS,SAAS;AAC1C,wBAAU,IAAI,WAAW,OAAO;AAChC,oBAAM,YAAY,KAAK,OAAO;AAAA,YAC/B;AAAA,UACF;AAED,iBAAO,IAAI,OAAO;AAElB,cAAI,IAAI,WAAW,SAAS,SAAS;AACnC,gBAAI,IAAI,WAAW,WAAW,UAAU;AACtC,oBAAM,IAAI,OAAO,SAAU,IAAI,KAAK;AAClC,oBAAI,MAAM,MAAM;AAAG,yBAAO;AAAA,cAC1C,CAAe;AAAA,YACF;AAAA,UACF;AAAA,QACX,OAAe;AACL,cAAI,IAAI,WAAW,WAAW,YAAY,CAACW,aAAY;AACrD,gBAAI,UAAU,kBAAkB,IAAI,OAAO,CAAC;AAK5C,sBAAU,QAAQ,MAAM,QAAQ,EAAE;AAAA,UAC9C,OAAiB;AACL,gBAAI,IAAI,OAAO,GAAG;AAChB,kBAAI,UAAU,IAAI,OAAO,EAAE,MAAM,KAAK,EAAE,OAAO,SAAU,IAAI;AAC3D,oBAAI,OAAO;AAAI,yBAAO;AAAA,cACtC,CAAe;AAAA,YACf,OAAmB;AACL,kBAAI,UAAU,IAAI,WAAW,CAAC,EAAE;AAAA,YACjC;AAAA,UACF;AAED,iBAAO,IAAI,OAAO;AAGlB,cAAI,IAAI,WAAW,SAAS,WAAW;AACrC,gBAAI,MAAM,IAAI,aAAa,OAAO;AAAA,UACnC,WAAU,IAAI,WAAW,SAAS,SAAS;AAC1C,gBAAI,MAAM,IAAI,WAAW,OAAO;AAAA,UACjC,WAAU,IAAI,WAAW,SAAS,SAAS;AAC1C,gBAAI,MAAM,IAAI,WAAW,OAAO;AAEhC,gBAAI,IAAI,WAAW,WAAW,UAAU;AACtC,oBAAM,IAAI,OAAO,SAAU,IAAI,KAAK;AAClC,oBAAI,MAAM,MAAM;AAAG,yBAAO;AAAA,cAC1C,CAAe;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAED,eAAO;AAAA,MACR;AAID,UAAI,MAAM;AAEV,UAAI,OAAO,WAAW;AACpB,YAAI;AACF,gBAAM,IAAI,UAAS,EAAG,gBAAgB,YAAY,UAAU;AAAA,QAC7D,SAAQ,GAAP;AACA,gBAAM;AAAA,QACP;AAAA,MACT,WAAiB,OAAO,eAAe;AAC/B,YAAI;AACF,gBAAM,IAAI,cAAc,kBAAkB;AAC1C,cAAI,QAAQ;AAEZ,cAAI,CAAE,IAAI;AAAA;AAAA,UAAO,GAAc;AAC7B,kBAAM,IAAI,MAAM,IAAI,WAAW,SAAS,IAAI,WAAW,OAAO;AAAA,UAC/D;AAAA,QACF,SAAQ,GAAP;AACA,gBAAM;AAAA,QACP;AAAA,MACT,OAAa;AACL,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC3C;AAGD,UAAI,MAAM,IAAI;AAEd,UAAI,OAAO,UAAU,GAAG;AACxB,UAAI,SAAS,CAAE;AACf,UAAI,UAAU,CAAE;AAChB,UAAI,UAAU,CAAE;AAEhB,UAAI,KAAK,UAAU;AACjB,YAAI,QAAQ,KAAK,SAAS;AAC1B,YAAI,aAAa,KAAK,WAAW,eAAe,YAAY;AAI5D,YAAI,WAAW,CAAC,aAAa,UAAU,UAAU,OAAO;AACxD,YAAI,eAAe,GACjB,mBAAmB,SAAS;AAE9B,eAAO,eAAe,kBAAkB;AACtC,cAAI,UAAU,MAAM,SAAS,YAAY,CAAC;AAI1C,cAAI,WAAW,QAAQ,WAAW;AAGhC,gBAAI,OAAO,UAAU,SAAS,KAAK,QAAQ,SAAS,MAAM,kBAAkB;AAC1E,kBAAI,MAAM,QAAQ;AAAA,YAChC,OAAmB;AACL,kBAAI,MAAM,CAAC,QAAQ,SAAS;AAAA,YAC7B;AAED,gBAAI,iBAAiB,GACnB,qBAAqB,IAAI;AAE3B,mBAAO,iBAAiB,oBAAoB;AAE1C,kBAAI,WAAW,IAAI,cAAc,KAAK,IAAI,cAAc,EAAE,OAAO,EAAE,SAAS,GAAG;AAC7E,oBAAI,cAAc,EAAE,OAAO,eAAe,IAAI,cAAc,GAAG,UAAU;AAAA,cAC1E;AAED;AAAA,YACD;AAED,oBAAQ,SAAS,YAAY,GAAC;AAAA,cAE5B,KAAK;AACH,oBAAI,iBAAiB,SAAS,MAAM,WAAW,cAAc;AAC7D,oBAAI,cAAc,QAAQ,WAAW;AAErC,oBAAI,iBAAiB,GAAG;AACtB,2BAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC9C,wBAAI,gBAAgB,IAAI,CAAC,EAAE,WAAW,MAAM;AAC1C,0BAAI,aAAa,IAAI,CAAC,EAAE,WAAW;AACnC,gCAAU,IAAI,aAAa,iBAAiB,UAAU;AACtD,8BAAQ,IAAI,IAAI,CAAC,EAAE,MAAM,CAAC;AAAA,oBAC3B;AAAA,kBACF;AAAA,gBACF;AAED;AAAA,cAGF,KAAK;AACH,oBAAI,iBAAiB,SAAS,MAAM,WAAW,cAAc;AAE7D,oBAAI,iBAAiB,GAAG;AACtB,sBAAI,aAAa,QAAQ,UAAU,WAAW;AAC9C,2BAAS,IAAI,aAAa,iBAAiB,UAAU;AACrD,yBAAO,IAAI,QAAQ,UAAU,MAAM,CAAC;AAAA,gBACrC;AAED;AAAA,cAGF,KAAK;AACH,oBAAI,iBAAiB,SAAS,MAAM,WAAW,cAAc;AAE7D,oBAAI,iBAAiB,GAAG;AACtB,sBAAI,eAAe,IAAI,WAAW,QAAQ,UAAU,CAAC,EAAE,KAAK,MAAM;AAClE,sBAAI,SAAS,IAAI,WAAW,QAAQ,UAAU,CAAC,EAAE,KAAK,MAAM;AAC5D,+BAAa,IAAI,QAAQ,UAAU,CAAC,EAAE,MAAM,CAAC;AAC7C,yBAAO,IAAI,QAAQ,UAAU,CAAC,EAAE,MAAM,CAAC;AAEvC,sBAAI,OAAO,iBAAiB,aAAa;AACzC,4BAAU,IAAI,YAAY,IAAI,OAAO,IAAI,cAAc;AAEvD,sBAAI,eAAe;AAEnB,2BAAS,IAAI,GAAG,MAAM,gBAAgB,IAAI,KAAK,KAAK;AAClD,wBAAI,QAAQ,CAAE;AAEd,6BAAS,IAAI,GAAG,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,IAAI,OAAO,MAAM,KAAK;AAChE,4BAAM,KAAK,aAAa,CAAC,CAAC;AAE1B,0BAAI,IAAI;AAAG,+BAAO,OAAO,IAAI,CAAC;AAAA,oBAC/B;AAED,6BAAS,IAAI,GAAG,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,IAAI,OAAO,OAAO,GAAG,KAAK;AACpE,0BAAI,IAAI,GAAG;AACT,gCAAQ,cAAc,IAAI,MAAM,CAAC;AACjC,gCAAQ,cAAc,IAAI,MAAM,IAAI,CAAC;AACrC,gCAAQ,cAAc,IAAI,MAAM,IAAI,CAAC;AAAA,sBAC7D,OAA6B;AACL,gCAAQ,cAAc,IAAI,MAAM,CAAC;AACjC,gCAAQ,cAAc,IAAI,MAAM,IAAI,CAAC;AACrC,gCAAQ,cAAc,IAAI,MAAM,IAAI,CAAC;AAAA,sBACtC;AAED,0BAAI,IAAI;AAAG,+BAAO,OAAO,IAAI,CAAC;AAAA,oBAC/B;AAAA,kBACF;AAAA,gBACF;AAED;AAAA,cAGF,KAAK;AACH,oBAAI,gBAAgB,SAAS,MAAM,WAAW,aAAa;AAE3D,oBAAI,gBAAgB,GAAG;AACrB,sBAAI,eAAe,IAAI,WAAW,QAAQ,UAAU,CAAC,EAAE,KAAK,MAAM;AAClE,sBAAI,SAAS,IAAI,WAAW,QAAQ,UAAU,CAAC,EAAE,KAAK,MAAM;AAC5D,+BAAa,IAAI,QAAQ,UAAU,CAAC,EAAE,MAAM,CAAC;AAC7C,yBAAO,IAAI,QAAQ,UAAU,CAAC,EAAE,MAAM,CAAC;AAEvC,sBAAI,OAAO,gBAAgB,aAAa;AACxC,4BAAU,IAAI,YAAY,IAAI,OAAO,IAAI,aAAa;AACtD,sBAAI,eAAe,GACjB,oBAAoB;AACtB,sBAAI,IAAI,GACN,MAAM,eACN,OAAO;AAET,yBAAO,IAAI,KAAK;AACd,wBAAI,OAAO,CAAE;AACb,wBAAI,IAAI,GACN,OAAO,OAAO,CAAC;AAEjB,2BAAO,IAAI,OAAO,MAAM;AACtB,2BAAK,KAAK,aAAa,mBAAmB,CAAC;AAC3C;AAAA,oBACD;AAED,wBAAI,IAAI;AAER,2BAAO,IAAI,OAAO,OAAO,GAAG;AAC1B,8BAAQ,cAAc,IAAI,KAAK,CAAC;AAChC,8BAAQ,cAAc,IAAI,KAAK,CAAC;AAChC,8BAAQ,cAAc,IAAI,KAAK,IAAI,CAAC;AACpC;AAAA,oBACD;AAED;AACA,2BAAO,OAAO,IAAI,CAAC;AAAA,kBACpB;AAAA,gBACF;AAED;AAAA,YAIH;AAAA,UACF;AAED;AAAA,QACD;AAED,YAAI,WAAW,IAAIV,qBAAgB;AACnC,iBAAS,SAAS,IAAIK,MAAe,gBAAC,SAAS,CAAC,CAAC;AACjD,iBAAS,aAAa,YAAY,IAAIA,MAAAA,gBAAgB,QAAQ,CAAC,CAAC;AAEhE,YAAI,QAAQ,WAAW,OAAO,QAAQ;AACpC,mBAAS,aAAa,UAAU,IAAIA,MAAAA,gBAAgB,SAAS,CAAC,CAAC;AAAA,QAChE;AAED,eAAO;AAAA,MACf,OAAa;AACL,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC3C;AAAA,IACF;AAGD,QAAI,OAAOO,uBAAW,IAAI,WAAW,MAAM,GAAG,GAAG,CAAC,EAAE,MAAM,IAAI;AAE9D,QAAI,KAAK,CAAC,EAAE,QAAQ,KAAK,MAAM,IAAI;AACjC,aAAO,SAASA,uBAAW,IAAI,CAAC;AAAA,IACjC,WAAU,KAAK,CAAC,EAAE,SAAS,OAAO,GAAG;AACpC,aAAO,WAAWA,uBAAW,IAAI,CAAC;AAAA,IACxC,OAAW;AACL,aAAO,YAAY,IAAI;AAAA,IACxB;AAAA,EACF;AACH;;"}