{"version": 3, "file": "XYZLoader.js", "sources": ["../../src/loaders/XYZLoader.js"], "sourcesContent": ["import { <PERSON>ufferG<PERSON><PERSON>, FileLoader, Float32<PERSON>uffer<PERSON>ttribute, Loader } from 'three'\n\nclass XYZLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(text) {\n    const lines = text.split('\\n')\n\n    const vertices = []\n    const colors = []\n\n    for (let line of lines) {\n      line = line.trim()\n\n      if (line.charAt(0) === '#') continue // skip comments\n\n      const lineValues = line.split(/\\s+/)\n\n      if (lineValues.length === 3) {\n        // XYZ\n\n        vertices.push(parseFloat(lineValues[0]))\n        vertices.push(parseFloat(lineValues[1]))\n        vertices.push(parseFloat(lineValues[2]))\n      }\n\n      if (lineValues.length === 6) {\n        // XYZRGB\n\n        vertices.push(parseFloat(lineValues[0]))\n        vertices.push(parseFloat(lineValues[1]))\n        vertices.push(parseFloat(lineValues[2]))\n\n        colors.push(parseFloat(lineValues[3]) / 255)\n        colors.push(parseFloat(lineValues[4]) / 255)\n        colors.push(parseFloat(lineValues[5]) / 255)\n      }\n    }\n\n    const geometry = new BufferGeometry()\n    geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n\n    if (colors.length > 0) {\n      geometry.setAttribute('color', new Float32BufferAttribute(colors, 3))\n    }\n\n    return geometry\n  }\n}\n\nexport { XYZLoader }\n"], "names": [], "mappings": ";AAEA,MAAM,kBAAkB,OAAO;AAAA,EAC7B,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAI,WAAW,KAAK,OAAO;AAC1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,iBAAiB,KAAK,aAAa;AAC1C,WAAO,mBAAmB,KAAK,eAAe;AAC9C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,iBAAO,MAAM,MAAM,IAAI,CAAC;AAAA,QACzB,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,MAAM;AACV,UAAM,QAAQ,KAAK,MAAM,IAAI;AAE7B,UAAM,WAAW,CAAE;AACnB,UAAM,SAAS,CAAE;AAEjB,aAAS,QAAQ,OAAO;AACtB,aAAO,KAAK,KAAM;AAElB,UAAI,KAAK,OAAO,CAAC,MAAM;AAAK;AAE5B,YAAM,aAAa,KAAK,MAAM,KAAK;AAEnC,UAAI,WAAW,WAAW,GAAG;AAG3B,iBAAS,KAAK,WAAW,WAAW,CAAC,CAAC,CAAC;AACvC,iBAAS,KAAK,WAAW,WAAW,CAAC,CAAC,CAAC;AACvC,iBAAS,KAAK,WAAW,WAAW,CAAC,CAAC,CAAC;AAAA,MACxC;AAED,UAAI,WAAW,WAAW,GAAG;AAG3B,iBAAS,KAAK,WAAW,WAAW,CAAC,CAAC,CAAC;AACvC,iBAAS,KAAK,WAAW,WAAW,CAAC,CAAC,CAAC;AACvC,iBAAS,KAAK,WAAW,WAAW,CAAC,CAAC,CAAC;AAEvC,eAAO,KAAK,WAAW,WAAW,CAAC,CAAC,IAAI,GAAG;AAC3C,eAAO,KAAK,WAAW,WAAW,CAAC,CAAC,IAAI,GAAG;AAC3C,eAAO,KAAK,WAAW,WAAW,CAAC,CAAC,IAAI,GAAG;AAAA,MAC5C;AAAA,IACF;AAED,UAAM,WAAW,IAAI,eAAgB;AACrC,aAAS,aAAa,YAAY,IAAI,uBAAuB,UAAU,CAAC,CAAC;AAEzE,QAAI,OAAO,SAAS,GAAG;AACrB,eAAS,aAAa,SAAS,IAAI,uBAAuB,QAAQ,CAAC,CAAC;AAAA,IACrE;AAED,WAAO;AAAA,EACR;AACH;"}