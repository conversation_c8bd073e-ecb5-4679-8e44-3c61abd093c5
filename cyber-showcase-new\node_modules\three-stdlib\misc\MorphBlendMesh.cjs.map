{"version": 3, "file": "MorphBlendMesh.cjs", "sources": ["../../src/misc/MorphBlendMesh.js"], "sourcesContent": ["import { MathUtil<PERSON>, Mesh } from 'three'\n\nclass MorphBlendMesh extends Mesh {\n  constructor(geometry, material) {\n    super(geometry, material)\n\n    this.animationsMap = {}\n    this.animationsList = []\n\n    // prepare default animation\n    // (all frames played together in 1 second)\n\n    const numFrames = Object.keys(this.morphTargetDictionary).length\n\n    const name = '__default'\n\n    const startFrame = 0\n    const endFrame = numFrames - 1\n\n    const fps = numFrames / 1\n\n    this.createAnimation(name, startFrame, endFrame, fps)\n    this.setAnimationWeight(name, 1)\n  }\n\n  createAnimation(name, start, end, fps) {\n    const animation = {\n      start: start,\n      end: end,\n\n      length: end - start + 1,\n\n      fps: fps,\n      duration: (end - start) / fps,\n\n      lastFrame: 0,\n      currentFrame: 0,\n\n      active: false,\n\n      time: 0,\n      direction: 1,\n      weight: 1,\n\n      directionBackwards: false,\n      mirroredLoop: false,\n    }\n\n    this.animationsMap[name] = animation\n    this.animationsList.push(animation)\n  }\n\n  autoCreateAnimations(fps) {\n    const pattern = /([a-z]+)_?(\\d+)/i\n\n    let firstAnimation\n\n    const frameRanges = {}\n\n    let i = 0\n\n    for (const key in this.morphTargetDictionary) {\n      const chunks = key.match(pattern)\n\n      if (chunks && chunks.length > 1) {\n        const name = chunks[1]\n\n        if (!frameRanges[name]) frameRanges[name] = { start: Infinity, end: -Infinity }\n\n        const range = frameRanges[name]\n\n        if (i < range.start) range.start = i\n        if (i > range.end) range.end = i\n\n        if (!firstAnimation) firstAnimation = name\n      }\n\n      i++\n    }\n\n    for (const name in frameRanges) {\n      const range = frameRanges[name]\n      this.createAnimation(name, range.start, range.end, fps)\n    }\n\n    this.firstAnimation = firstAnimation\n  }\n\n  setAnimationDirectionForward(name) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.direction = 1\n      animation.directionBackwards = false\n    }\n  }\n\n  setAnimationDirectionBackward(name) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.direction = -1\n      animation.directionBackwards = true\n    }\n  }\n\n  setAnimationFPS(name, fps) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.fps = fps\n      animation.duration = (animation.end - animation.start) / animation.fps\n    }\n  }\n\n  setAnimationDuration(name, duration) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.duration = duration\n      animation.fps = (animation.end - animation.start) / animation.duration\n    }\n  }\n\n  setAnimationWeight(name, weight) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.weight = weight\n    }\n  }\n\n  setAnimationTime(name, time) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.time = time\n    }\n  }\n\n  getAnimationTime(name) {\n    let time = 0\n\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      time = animation.time\n    }\n\n    return time\n  }\n\n  getAnimationDuration(name) {\n    let duration = -1\n\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      duration = animation.duration\n    }\n\n    return duration\n  }\n\n  playAnimation(name) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.time = 0\n      animation.active = true\n    } else {\n      console.warn('THREE.MorphBlendMesh: animation[' + name + '] undefined in .playAnimation()')\n    }\n  }\n\n  stopAnimation(name) {\n    const animation = this.animationsMap[name]\n\n    if (animation) {\n      animation.active = false\n    }\n  }\n\n  update(delta) {\n    for (let i = 0, il = this.animationsList.length; i < il; i++) {\n      const animation = this.animationsList[i]\n\n      if (!animation.active) continue\n\n      const frameTime = animation.duration / animation.length\n\n      animation.time += animation.direction * delta\n\n      if (animation.mirroredLoop) {\n        if (animation.time > animation.duration || animation.time < 0) {\n          animation.direction *= -1\n\n          if (animation.time > animation.duration) {\n            animation.time = animation.duration\n            animation.directionBackwards = true\n          }\n\n          if (animation.time < 0) {\n            animation.time = 0\n            animation.directionBackwards = false\n          }\n        }\n      } else {\n        animation.time = animation.time % animation.duration\n\n        if (animation.time < 0) animation.time += animation.duration\n      }\n\n      const keyframe =\n        animation.start + MathUtils.clamp(Math.floor(animation.time / frameTime), 0, animation.length - 1)\n      const weight = animation.weight\n\n      if (keyframe !== animation.currentFrame) {\n        this.morphTargetInfluences[animation.lastFrame] = 0\n        this.morphTargetInfluences[animation.currentFrame] = 1 * weight\n\n        this.morphTargetInfluences[keyframe] = 0\n\n        animation.lastFrame = animation.currentFrame\n        animation.currentFrame = keyframe\n      }\n\n      let mix = (animation.time % frameTime) / frameTime\n\n      if (animation.directionBackwards) mix = 1 - mix\n\n      if (animation.currentFrame !== animation.lastFrame) {\n        this.morphTargetInfluences[animation.currentFrame] = mix * weight\n        this.morphTargetInfluences[animation.lastFrame] = (1 - mix) * weight\n      } else {\n        this.morphTargetInfluences[animation.currentFrame] = weight\n      }\n    }\n  }\n}\n\nexport { MorphBlendMesh }\n"], "names": ["<PERSON><PERSON>", "MathUtils"], "mappings": ";;;AAEA,MAAM,uBAAuBA,MAAAA,KAAK;AAAA,EAChC,YAAY,UAAU,UAAU;AAC9B,UAAM,UAAU,QAAQ;AAExB,SAAK,gBAAgB,CAAE;AACvB,SAAK,iBAAiB,CAAE;AAKxB,UAAM,YAAY,OAAO,KAAK,KAAK,qBAAqB,EAAE;AAE1D,UAAM,OAAO;AAEb,UAAM,aAAa;AACnB,UAAM,WAAW,YAAY;AAE7B,UAAM,MAAM,YAAY;AAExB,SAAK,gBAAgB,MAAM,YAAY,UAAU,GAAG;AACpD,SAAK,mBAAmB,MAAM,CAAC;AAAA,EAChC;AAAA,EAED,gBAAgB,MAAM,OAAO,KAAK,KAAK;AACrC,UAAM,YAAY;AAAA,MAChB;AAAA,MACA;AAAA,MAEA,QAAQ,MAAM,QAAQ;AAAA,MAEtB;AAAA,MACA,WAAW,MAAM,SAAS;AAAA,MAE1B,WAAW;AAAA,MACX,cAAc;AAAA,MAEd,QAAQ;AAAA,MAER,MAAM;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MAER,oBAAoB;AAAA,MACpB,cAAc;AAAA,IACf;AAED,SAAK,cAAc,IAAI,IAAI;AAC3B,SAAK,eAAe,KAAK,SAAS;AAAA,EACnC;AAAA,EAED,qBAAqB,KAAK;AACxB,UAAM,UAAU;AAEhB,QAAI;AAEJ,UAAM,cAAc,CAAE;AAEtB,QAAI,IAAI;AAER,eAAW,OAAO,KAAK,uBAAuB;AAC5C,YAAM,SAAS,IAAI,MAAM,OAAO;AAEhC,UAAI,UAAU,OAAO,SAAS,GAAG;AAC/B,cAAM,OAAO,OAAO,CAAC;AAErB,YAAI,CAAC,YAAY,IAAI;AAAG,sBAAY,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,UAAW;AAE/E,cAAM,QAAQ,YAAY,IAAI;AAE9B,YAAI,IAAI,MAAM;AAAO,gBAAM,QAAQ;AACnC,YAAI,IAAI,MAAM;AAAK,gBAAM,MAAM;AAE/B,YAAI,CAAC;AAAgB,2BAAiB;AAAA,MACvC;AAED;AAAA,IACD;AAED,eAAW,QAAQ,aAAa;AAC9B,YAAM,QAAQ,YAAY,IAAI;AAC9B,WAAK,gBAAgB,MAAM,MAAM,OAAO,MAAM,KAAK,GAAG;AAAA,IACvD;AAED,SAAK,iBAAiB;AAAA,EACvB;AAAA,EAED,6BAA6B,MAAM;AACjC,UAAM,YAAY,KAAK,cAAc,IAAI;AAEzC,QAAI,WAAW;AACb,gBAAU,YAAY;AACtB,gBAAU,qBAAqB;AAAA,IAChC;AAAA,EACF;AAAA,EAED,8BAA8B,MAAM;AAClC,UAAM,YAAY,KAAK,cAAc,IAAI;AAEzC,QAAI,WAAW;AACb,gBAAU,YAAY;AACtB,gBAAU,qBAAqB;AAAA,IAChC;AAAA,EACF;AAAA,EAED,gBAAgB,MAAM,KAAK;AACzB,UAAM,YAAY,KAAK,cAAc,IAAI;AAEzC,QAAI,WAAW;AACb,gBAAU,MAAM;AAChB,gBAAU,YAAY,UAAU,MAAM,UAAU,SAAS,UAAU;AAAA,IACpE;AAAA,EACF;AAAA,EAED,qBAAqB,MAAM,UAAU;AACnC,UAAM,YAAY,KAAK,cAAc,IAAI;AAEzC,QAAI,WAAW;AACb,gBAAU,WAAW;AACrB,gBAAU,OAAO,UAAU,MAAM,UAAU,SAAS,UAAU;AAAA,IAC/D;AAAA,EACF;AAAA,EAED,mBAAmB,MAAM,QAAQ;AAC/B,UAAM,YAAY,KAAK,cAAc,IAAI;AAEzC,QAAI,WAAW;AACb,gBAAU,SAAS;AAAA,IACpB;AAAA,EACF;AAAA,EAED,iBAAiB,MAAM,MAAM;AAC3B,UAAM,YAAY,KAAK,cAAc,IAAI;AAEzC,QAAI,WAAW;AACb,gBAAU,OAAO;AAAA,IAClB;AAAA,EACF;AAAA,EAED,iBAAiB,MAAM;AACrB,QAAI,OAAO;AAEX,UAAM,YAAY,KAAK,cAAc,IAAI;AAEzC,QAAI,WAAW;AACb,aAAO,UAAU;AAAA,IAClB;AAED,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB,MAAM;AACzB,QAAI,WAAW;AAEf,UAAM,YAAY,KAAK,cAAc,IAAI;AAEzC,QAAI,WAAW;AACb,iBAAW,UAAU;AAAA,IACtB;AAED,WAAO;AAAA,EACR;AAAA,EAED,cAAc,MAAM;AAClB,UAAM,YAAY,KAAK,cAAc,IAAI;AAEzC,QAAI,WAAW;AACb,gBAAU,OAAO;AACjB,gBAAU,SAAS;AAAA,IACzB,OAAW;AACL,cAAQ,KAAK,qCAAqC,OAAO,iCAAiC;AAAA,IAC3F;AAAA,EACF;AAAA,EAED,cAAc,MAAM;AAClB,UAAM,YAAY,KAAK,cAAc,IAAI;AAEzC,QAAI,WAAW;AACb,gBAAU,SAAS;AAAA,IACpB;AAAA,EACF;AAAA,EAED,OAAO,OAAO;AACZ,aAAS,IAAI,GAAG,KAAK,KAAK,eAAe,QAAQ,IAAI,IAAI,KAAK;AAC5D,YAAM,YAAY,KAAK,eAAe,CAAC;AAEvC,UAAI,CAAC,UAAU;AAAQ;AAEvB,YAAM,YAAY,UAAU,WAAW,UAAU;AAEjD,gBAAU,QAAQ,UAAU,YAAY;AAExC,UAAI,UAAU,cAAc;AAC1B,YAAI,UAAU,OAAO,UAAU,YAAY,UAAU,OAAO,GAAG;AAC7D,oBAAU,aAAa;AAEvB,cAAI,UAAU,OAAO,UAAU,UAAU;AACvC,sBAAU,OAAO,UAAU;AAC3B,sBAAU,qBAAqB;AAAA,UAChC;AAED,cAAI,UAAU,OAAO,GAAG;AACtB,sBAAU,OAAO;AACjB,sBAAU,qBAAqB;AAAA,UAChC;AAAA,QACF;AAAA,MACT,OAAa;AACL,kBAAU,OAAO,UAAU,OAAO,UAAU;AAE5C,YAAI,UAAU,OAAO;AAAG,oBAAU,QAAQ,UAAU;AAAA,MACrD;AAED,YAAM,WACJ,UAAU,QAAQC,MAAAA,UAAU,MAAM,KAAK,MAAM,UAAU,OAAO,SAAS,GAAG,GAAG,UAAU,SAAS,CAAC;AACnG,YAAM,SAAS,UAAU;AAEzB,UAAI,aAAa,UAAU,cAAc;AACvC,aAAK,sBAAsB,UAAU,SAAS,IAAI;AAClD,aAAK,sBAAsB,UAAU,YAAY,IAAI,IAAI;AAEzD,aAAK,sBAAsB,QAAQ,IAAI;AAEvC,kBAAU,YAAY,UAAU;AAChC,kBAAU,eAAe;AAAA,MAC1B;AAED,UAAI,MAAO,UAAU,OAAO,YAAa;AAEzC,UAAI,UAAU;AAAoB,cAAM,IAAI;AAE5C,UAAI,UAAU,iBAAiB,UAAU,WAAW;AAClD,aAAK,sBAAsB,UAAU,YAAY,IAAI,MAAM;AAC3D,aAAK,sBAAsB,UAAU,SAAS,KAAK,IAAI,OAAO;AAAA,MACtE,OAAa;AACL,aAAK,sBAAsB,UAAU,YAAY,IAAI;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AACH;;"}