{"version": 3, "file": "LuminosityShader.js", "sources": ["../../src/shaders/LuminosityShader.ts"], "sourcesContent": ["/**\n * Luminosity\n * http://en.wikipedia.org/wiki/Luminosity\n */\n\nexport const LuminosityShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #include <common>\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tfloat l = linearToRelativeLuminance( texel.rgb );\n\n    \tgl_FragColor = vec4( l, l, l, texel.w );\n\n    }\n  `,\n}\n"], "names": [], "mappings": "AAKO,MAAM,mBAAmB;AAAA,EAC9B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,EAC1B;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiB7B;"}