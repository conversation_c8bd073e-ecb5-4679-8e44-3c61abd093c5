{"version": 3, "file": "SceneUtils.cjs", "sources": ["../../src/utils/SceneUtils.ts"], "sourcesContent": ["import { Group, Mesh } from 'three'\nimport type { BufferGeometry, InstancedMesh, Material, Object3D, Scene } from 'three'\n\nconst SceneUtils = {\n  createMeshesFromInstancedMesh: function (instancedMesh: InstancedMesh): Group {\n    const group = new Group()\n\n    const count = instancedMesh.count\n    const geometry = instancedMesh.geometry\n    const material = instancedMesh.material\n\n    for (let i = 0; i < count; i++) {\n      const mesh = new Mesh(geometry, material)\n\n      instancedMesh.getMatrixAt(i, mesh.matrix)\n      mesh.matrix.decompose(mesh.position, mesh.quaternion, mesh.scale)\n\n      group.add(mesh)\n    }\n\n    group.copy((instancedMesh as unknown) as Group)\n    group.updateMatrixWorld() // ensure correct world matrices of meshes\n\n    return group\n  },\n\n  createMultiMaterialObject: function (geometry: BufferGeometry, materials: Material[]): Group {\n    const group = new Group()\n\n    for (let i = 0, l = materials.length; i < l; i++) {\n      group.add(new Mesh(geometry, materials[i]))\n    }\n\n    return group\n  },\n\n  detach: function (child: Object3D, parent: Object3D, scene: Scene): void {\n    console.warn('THREE.SceneUtils: detach() has been deprecated. Use scene.attach( child ) instead.')\n\n    scene.attach(child)\n  },\n\n  attach: function (child: Object3D, scene: Scene, parent: Object3D): void {\n    console.warn('THREE.SceneUtils: attach() has been deprecated. Use parent.attach( child ) instead.')\n\n    parent.attach(child)\n  },\n}\n\nexport { SceneUtils }\n"], "names": ["Group", "<PERSON><PERSON>"], "mappings": ";;;AAGA,MAAM,aAAa;AAAA,EACjB,+BAA+B,SAAU,eAAqC;AACtE,UAAA,QAAQ,IAAIA,MAAAA;AAElB,UAAM,QAAQ,cAAc;AAC5B,UAAM,WAAW,cAAc;AAC/B,UAAM,WAAW,cAAc;AAE/B,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAM,OAAO,IAAIC,MAAAA,KAAK,UAAU,QAAQ;AAE1B,oBAAA,YAAY,GAAG,KAAK,MAAM;AACxC,WAAK,OAAO,UAAU,KAAK,UAAU,KAAK,YAAY,KAAK,KAAK;AAEhE,YAAM,IAAI,IAAI;AAAA,IAChB;AAEA,UAAM,KAAM,aAAkC;AAC9C,UAAM,kBAAkB;AAEjB,WAAA;AAAA,EACT;AAAA,EAEA,2BAA2B,SAAU,UAA0B,WAA8B;AACrF,UAAA,QAAQ,IAAID,MAAAA;AAElB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAChD,YAAM,IAAI,IAAIC,MAAA,KAAK,UAAU,UAAU,CAAC,CAAC,CAAC;AAAA,IAC5C;AAEO,WAAA;AAAA,EACT;AAAA,EAEA,QAAQ,SAAU,OAAiB,QAAkB,OAAoB;AACvE,YAAQ,KAAK,oFAAoF;AAEjG,UAAM,OAAO,KAAK;AAAA,EACpB;AAAA,EAEA,QAAQ,SAAU,OAAiB,OAAc,QAAwB;AACvE,YAAQ,KAAK,qFAAqF;AAElG,WAAO,OAAO,KAAK;AAAA,EACrB;AACF;;"}