{"version": 3, "file": "VRButton.js", "sources": ["../../src/webxr/VRButton.js"], "sourcesContent": ["const VRButton = /* @__PURE__ */ (() => {\n  class VRButton {\n    static createButton(renderer, sessionInit = {}) {\n      const button = document.createElement('button')\n\n      function showEnterVR(/*device*/) {\n        let currentSession = null\n\n        async function onSessionStarted(session) {\n          session.addEventListener('end', onSessionEnded)\n\n          await renderer.xr.setSession(session)\n          button.textContent = 'EXIT VR'\n\n          currentSession = session\n        }\n\n        function onSessionEnded(/*event*/) {\n          currentSession.removeEventListener('end', onSessionEnded)\n\n          button.textContent = 'ENTER VR'\n\n          currentSession = null\n        }\n\n        //\n\n        button.style.display = ''\n\n        button.style.cursor = 'pointer'\n        button.style.left = 'calc(50% - 50px)'\n        button.style.width = '100px'\n\n        button.textContent = 'ENTER VR'\n\n        button.onmouseenter = () => {\n          button.style.opacity = '1.0'\n        }\n\n        button.onmouseleave = () => {\n          button.style.opacity = '0.5'\n        }\n\n        button.onclick = () => {\n          if (currentSession === null) {\n            // WebXR's requestReferenceSpace only works if the corresponding feature\n            // was requested at session creation time. For simplicity, just ask for\n            // the interesting ones as optional features, but be aware that the\n            // requestReferenceSpace call will fail if it turns out to be unavailable.\n            // ('local' is always available for immersive sessions and doesn't need to\n            // be requested separately.)\n\n            const optionalFeatures = [sessionInit.optionalFeatures, 'local-floor', 'bounded-floor', 'hand-tracking']\n              .flat()\n              .filter(Boolean)\n\n            navigator.xr?.requestSession('immersive-vr', { ...sessionInit, optionalFeatures }).then(onSessionStarted)\n          } else {\n            currentSession.end()\n          }\n        }\n      }\n\n      function disableButton() {\n        button.style.display = ''\n\n        button.style.cursor = 'auto'\n        button.style.left = 'calc(50% - 75px)'\n        button.style.width = '150px'\n\n        button.onmouseenter = null\n        button.onmouseleave = null\n\n        button.onclick = null\n      }\n\n      function showWebXRNotFound() {\n        disableButton()\n\n        button.textContent = 'VR NOT SUPPORTED'\n      }\n\n      function stylizeElement(element) {\n        element.style.position = 'absolute'\n        element.style.bottom = '20px'\n        element.style.padding = '12px 6px'\n        element.style.border = '1px solid #fff'\n        element.style.borderRadius = '4px'\n        element.style.background = 'rgba(0,0,0,0.1)'\n        element.style.color = '#fff'\n        element.style.font = 'normal 13px sans-serif'\n        element.style.textAlign = 'center'\n        element.style.opacity = '0.5'\n        element.style.outline = 'none'\n        element.style.zIndex = '999'\n      }\n\n      if ('xr' in navigator) {\n        stylizeElement(button)\n        button.id = 'VRButton'\n        button.style.display = 'none'\n\n        // Query for session mode\n        navigator.xr.isSessionSupported('immersive-vr').then((supported) => {\n          supported ? showEnterVR() : showWebXRNotFound()\n\n          if (supported && VRButton.xrSessionIsGranted) {\n            button.click()\n          }\n        })\n\n        return button\n      } else {\n        const message = document.createElement('a')\n\n        if (window.isSecureContext === false) {\n          message.href = document.location.href.replace(/^http:/, 'https:')\n          message.innerHTML = 'WEBXR NEEDS HTTPS' // TODO Improve message\n        } else {\n          message.href = 'https://immersiveweb.dev/'\n          message.innerHTML = 'WEBXR NOT AVAILABLE'\n        }\n\n        message.style.left = 'calc(50% - 90px)'\n        message.style.width = '180px'\n        message.style.textDecoration = 'none'\n\n        stylizeElement(message)\n\n        return message\n      }\n    }\n\n    static xrSessionIsGranted = false\n\n    static registerSessionGrantedListener() {\n      if (typeof navigator !== 'undefined' && 'xr' in navigator) {\n        navigator.xr.addEventListener('sessiongranted', () => {\n          VRButton.xrSessionIsGranted = true\n        })\n      }\n    }\n  }\n\n  VRButton.registerSessionGrantedListener()\n\n  return VRButton\n})()\n\nexport { VRButton }\n"], "names": ["VRButton"], "mappings": ";;;;;;AAAK,MAAC,WAA4B,uBAAM;AACtC,QAAM,YAAN,MAAe;AAAA,IACb,OAAO,aAAa,UAAU,cAAc,IAAI;AAC9C,YAAM,SAAS,SAAS,cAAc,QAAQ;AAE9C,eAAS,cAAwB;AAC/B,YAAI,iBAAiB;AAErB,uBAAe,iBAAiB,SAAS;AACvC,kBAAQ,iBAAiB,OAAO,cAAc;AAE9C,gBAAM,SAAS,GAAG,WAAW,OAAO;AACpC,iBAAO,cAAc;AAErB,2BAAiB;AAAA,QAClB;AAED,iBAAS,iBAA0B;AACjC,yBAAe,oBAAoB,OAAO,cAAc;AAExD,iBAAO,cAAc;AAErB,2BAAiB;AAAA,QAClB;AAID,eAAO,MAAM,UAAU;AAEvB,eAAO,MAAM,SAAS;AACtB,eAAO,MAAM,OAAO;AACpB,eAAO,MAAM,QAAQ;AAErB,eAAO,cAAc;AAErB,eAAO,eAAe,MAAM;AAC1B,iBAAO,MAAM,UAAU;AAAA,QACxB;AAED,eAAO,eAAe,MAAM;AAC1B,iBAAO,MAAM,UAAU;AAAA,QACxB;AAED,eAAO,UAAU,MAAM;AA3C1B;AA4CK,cAAI,mBAAmB,MAAM;AAQ3B,kBAAM,mBAAmB,CAAC,YAAY,kBAAkB,eAAe,iBAAiB,eAAe,EACpG,KAAM,EACN,OAAO,OAAO;AAEjB,4BAAU,OAAV,mBAAc,eAAe,gBAAgB,EAAE,GAAG,aAAa,iBAAkB,GAAE,KAAK;AAAA,UACpG,OAAiB;AACL,2BAAe,IAAK;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAED,eAAS,gBAAgB;AACvB,eAAO,MAAM,UAAU;AAEvB,eAAO,MAAM,SAAS;AACtB,eAAO,MAAM,OAAO;AACpB,eAAO,MAAM,QAAQ;AAErB,eAAO,eAAe;AACtB,eAAO,eAAe;AAEtB,eAAO,UAAU;AAAA,MAClB;AAED,eAAS,oBAAoB;AAC3B,sBAAe;AAEf,eAAO,cAAc;AAAA,MACtB;AAED,eAAS,eAAe,SAAS;AAC/B,gBAAQ,MAAM,WAAW;AACzB,gBAAQ,MAAM,SAAS;AACvB,gBAAQ,MAAM,UAAU;AACxB,gBAAQ,MAAM,SAAS;AACvB,gBAAQ,MAAM,eAAe;AAC7B,gBAAQ,MAAM,aAAa;AAC3B,gBAAQ,MAAM,QAAQ;AACtB,gBAAQ,MAAM,OAAO;AACrB,gBAAQ,MAAM,YAAY;AAC1B,gBAAQ,MAAM,UAAU;AACxB,gBAAQ,MAAM,UAAU;AACxB,gBAAQ,MAAM,SAAS;AAAA,MACxB;AAED,UAAI,QAAQ,WAAW;AACrB,uBAAe,MAAM;AACrB,eAAO,KAAK;AACZ,eAAO,MAAM,UAAU;AAGvB,kBAAU,GAAG,mBAAmB,cAAc,EAAE,KAAK,CAAC,cAAc;AAClE,sBAAY,YAAa,IAAG,kBAAmB;AAE/C,cAAI,aAAa,UAAS,oBAAoB;AAC5C,mBAAO,MAAO;AAAA,UACf;AAAA,QACX,CAAS;AAED,eAAO;AAAA,MACf,OAAa;AACL,cAAM,UAAU,SAAS,cAAc,GAAG;AAE1C,YAAI,OAAO,oBAAoB,OAAO;AACpC,kBAAQ,OAAO,SAAS,SAAS,KAAK,QAAQ,UAAU,QAAQ;AAChE,kBAAQ,YAAY;AAAA,QAC9B,OAAe;AACL,kBAAQ,OAAO;AACf,kBAAQ,YAAY;AAAA,QACrB;AAED,gBAAQ,MAAM,OAAO;AACrB,gBAAQ,MAAM,QAAQ;AACtB,gBAAQ,MAAM,iBAAiB;AAE/B,uBAAe,OAAO;AAEtB,eAAO;AAAA,MACR;AAAA,IACF;AAAA,IAID,OAAO,iCAAiC;AACtC,UAAI,OAAO,cAAc,eAAe,QAAQ,WAAW;AACzD,kBAAU,GAAG,iBAAiB,kBAAkB,MAAM;AACpD,oBAAS,qBAAqB;AAAA,QACxC,CAAS;AAAA,MACF;AAAA,IACF;AAAA,EACF;AA7ID,MAAMA,YAAN;AAoIE,gBApIIA,WAoIG,sBAAqB;AAW9B,EAAAA,UAAS,+BAAgC;AAEzC,SAAOA;AACT,GAAC;"}