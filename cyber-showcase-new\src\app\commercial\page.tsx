'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, useScroll, useTransform, AnimatePresence, useInView } from 'framer-motion';
// 移除导航栏，保持极简geek风格
// import CyberNavigation from '@/components/navigation/CyberNavigation';
import { Shield, Target, Eye, Zap, Users, Award, ChevronDown, Play, ArrowRight, CheckCircle, TrendingUp, ChevronRight } from 'lucide-react';

/**
 * 🔴 商业红蓝对抗版本页面 - 现代化大气设计
 * 参考苹果和FireEye风格，分section展示
 */
export default function CommercialPage() {
  const [activeSection, setActiveSection] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const textY = useTransform(scrollYProgress, [0, 1], ["0%", "200%"]);

  const services = [
    {
      icon: Shield,
      title: "红队攻击演练",
      subtitle: "Red Team Operations",
      description: "模拟真实攻击场景，全面测试企业安全防护能力",
      features: ["APT攻击模拟", "社会工程学", "内网渗透", "0day利用"]
    },
    {
      icon: Target,
      title: "蓝队防御体系",
      subtitle: "Blue Team Defense",
      description: "构建完整的安全监控和响应体系",
      features: ["威胁狩猎", "事件响应", "安全运营", "取证分析"]
    },
    {
      icon: Eye,
      title: "APT威胁分析",
      subtitle: "Advanced Persistent Threat",
      description: "深度分析高级持续性威胁，提供专业情报支持",
      features: ["威胁情报", "恶意软件分析", "攻击链重构", "归因分析"]
    }
  ];

  return (
    <div ref={containerRef} className="relative bg-slate-900">
      {/* 极简geek风格，移除导航栏 */}

      {/* Hero Section - 大气首屏 */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* 动态背景 */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800"
          style={{ y: backgroundY }}
        />

        {/* 粒子效果背景 */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]" />
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(239,68,68,0.1),transparent_50%)]" />
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(34,197,94,0.1),transparent_50%)]" />
        </div>

        {/* 网格背景 */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />

        {/* 主要内容 */}
        <motion.div
          className="relative z-10 text-center max-w-6xl mx-auto px-4"
          style={{ y: textY }}
        >
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.2 }}
            className="mb-8"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              className="mb-6"
            >
              <span className="inline-block px-4 py-2 bg-blue-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-mono mb-6">
                Elite Cyber Security Solutions
              </span>
            </motion.div>

            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-white mb-6 tracking-tight leading-none">
              <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-red-400 bg-clip-text text-transparent">
                世界级
              </span>
              <br />
              <span className="text-white">红蓝对抗</span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-300 font-light mb-8 max-w-4xl mx-auto leading-relaxed">
              专业的网络安全攻防演练服务，帮助企业构建坚不可摧的安全防线
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <motion.button
              className="group px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold text-lg hover:shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="flex items-center gap-2">
                <Play size={20} />
                观看演示
                <ArrowRight size={16} className="group-hover:translate-x-1 transition-transform" />
              </span>
            </motion.button>
            <motion.button
              className="px-8 py-4 border-2 border-blue-400 text-blue-400 rounded-full font-semibold text-lg hover:bg-blue-400 hover:text-white transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              联系我们
            </motion.button>
          </motion.div>
        </motion.div>

        {/* 滚动指示器 */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <ChevronDown className="text-white/60" size={32} />
        </motion.div>
      </section>

      {/* Transition Section - 核心能力过渡 */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900/30 to-slate-900 overflow-hidden">
        {/* 网格背景 */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:100px_100px] animate-pulse" />

        {/* 中心光晕 */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(59,130,246,0.2),transparent_70%)]" />

        {/* 动态六边形 */}
        <div className="absolute inset-0 flex items-center justify-center">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute border-2 border-blue-400/30"
              style={{
                width: 200 + i * 100,
                height: 200 + i * 100,
                clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
              }}
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 10 + i * 2,
                repeat: Infinity,
                ease: "linear",
              }}
            />
          ))}
        </div>

        {/* 主标题 */}
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 2, ease: "easeOut" }}
          viewport={{ once: true }}
          className="relative z-10 text-center"
        >
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <h1 className="text-8xl md:text-9xl font-black text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-cyan-300 to-blue-500 mb-8 tracking-wider">
              CORE
            </h1>
            <h2 className="text-6xl md:text-7xl font-bold text-white mb-6 tracking-wide">
              CAPABILITIES
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-blue-400 to-cyan-400 mx-auto mb-8" />
            <p className="text-2xl text-blue-200 font-light tracking-widest">
              核心技术能力展示
            </p>
          </motion.div>
        </motion.div>

        {/* 底部指示器 */}
        <motion.div
          className="absolute bottom-16 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 20, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <div className="flex flex-col items-center">
            <div className="w-px h-16 bg-gradient-to-b from-blue-400 to-transparent mb-4" />
            <ChevronDown className="text-blue-400" size={24} />
          </div>
        </motion.div>
      </section>

      {/* Services Section - 服务展示 */}
      <section className="relative min-h-screen py-40 bg-slate-800/50 flex items-center justify-center">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-24"
          >
            <div className="mb-8">
              <span className="inline-block px-6 py-3 bg-blue-500/10 border border-blue-500/20 rounded-full text-blue-400 text-lg font-mono">
                Core Services
              </span>
            </div>
            <h2 className="text-5xl md:text-7xl font-black text-white mb-12 tracking-tight">
              核心服务能力
            </h2>
            <p className="text-2xl text-slate-400 max-w-4xl mx-auto leading-relaxed">
              提供全方位的网络安全攻防演练服务，从红队攻击到蓝队防御，全面提升企业安全能力
            </p>
          </motion.div>

          {/* Services Grid - 居中布局 */}
          <div className="flex flex-col lg:flex-row gap-16 justify-center items-center max-w-5xl mx-auto">
            {services.map((service, index) => {
              const Icon = service.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className="group relative max-w-sm mx-auto"
                >
                  <div className="relative p-10 bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-3xl hover:border-blue-500/50 transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/20 text-center">
                    {/* Icon */}
                    <div className="mb-8 flex justify-center">
                      <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <Icon className="text-white" size={40} />
                      </div>
                    </div>

                    {/* Content */}
                    <h3 className="text-3xl font-bold text-white mb-3">
                      {service.title}
                    </h3>
                    <p className="text-blue-400 font-mono text-lg mb-6">
                      {service.subtitle}
                    </p>
                    <p className="text-slate-300 mb-8 leading-relaxed text-lg">
                      {service.description}
                    </p>

                    {/* Features */}
                    <ul className="space-y-3">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center justify-center text-slate-400">
                          <CheckCircle className="text-green-400 mr-3" size={18} />
                          <span className="text-base">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    {/* Hover Effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Stats Transition - 专业数据过渡 */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-green-900/20 to-slate-900 overflow-hidden">
        {/* 数据矩阵背景 */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-[linear-gradient(rgba(34,197,94,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(34,197,94,0.1)_1px,transparent_1px)] bg-[size:80px_80px]" />
        </div>

        {/* 中心数据环 */}
        <div className="absolute inset-0 flex items-center justify-center">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute border border-green-400/40 rounded-full"
              style={{
                width: 300 + i * 150,
                height: 300 + i * 150,
              }}
              animate={{
                rotate: [0, 360],
                opacity: [0.2, 0.6, 0.2],
              }}
              transition={{
                duration: 8 + i * 2,
                repeat: Infinity,
                ease: "linear",
              }}
            />
          ))}
        </div>

        {/* 浮动数据点 */}
        <div className="absolute inset-0">
          {['99.9%', '500+', '24/7', '<5min'].map((stat, i) => (
            <motion.div
              key={i}
              className="absolute text-green-400 font-mono text-2xl font-bold"
              style={{
                left: `${20 + i * 20}%`,
                top: `${30 + (i % 2) * 40}%`,
              }}
              animate={{
                y: [0, -30, 0],
                opacity: [0.3, 1, 0.3],
                scale: [0.8, 1.2, 0.8],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 0.5,
              }}
            >
              {stat}
            </motion.div>
          ))}
        </div>

        {/* 主标题 */}
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 2, ease: "easeOut" }}
          viewport={{ once: true }}
          className="relative z-10 text-center"
        >
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <h1 className="text-8xl md:text-9xl font-black text-transparent bg-clip-text bg-gradient-to-r from-green-400 via-emerald-300 to-green-500 mb-8 tracking-wider">
              DATA
            </h1>
            <h2 className="text-6xl md:text-7xl font-bold text-white mb-6 tracking-wide">
              ANALYTICS
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-green-400 to-emerald-400 mx-auto mb-8" />
            <p className="text-2xl text-green-200 font-light tracking-widest">
              专业数据分析展示
            </p>
          </motion.div>
        </motion.div>

        {/* 底部指示器 */}
        <motion.div
          className="absolute bottom-16 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 20, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <div className="flex flex-col items-center">
            <div className="w-px h-16 bg-gradient-to-b from-green-400 to-transparent mb-4" />
            <TrendingUp className="text-green-400" size={24} />
          </div>
        </motion.div>
      </section>

      {/* Stats Section - 数据展示 */}
      <section className="relative min-h-screen py-32 bg-gradient-to-r from-blue-900/20 to-purple-900/20 backdrop-blur-sm flex items-center justify-center">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* Stats Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <h2 className="text-5xl md:text-7xl font-black text-white mb-8 tracking-tight">
              专业数据展示
            </h2>
            <p className="text-2xl text-slate-400 max-w-4xl mx-auto leading-relaxed">
              用数据说话，展示我们的专业实力和服务质量
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-16 max-w-5xl mx-auto">
            {[
              { number: "7x24", label: "全天候监控", icon: Eye, color: "text-blue-400" },
              { number: "99.9%", label: "威胁检测率", icon: Target, color: "text-green-400" },
              { number: "< 3min", label: "应急响应", icon: Zap, color: "text-yellow-400" },
              { number: "500+", label: "成功案例", icon: Award, color: "text-purple-400" }
            ].map((stat, index) => {
              const Icon = stat.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group relative"
                >
                  {/* 背景光晕 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  <div className="relative p-12">
                    <div className="mb-8">
                      <Icon className={`mx-auto ${stat.color} group-hover:scale-110 transition-all duration-300`} size={48} />
                    </div>
                    <div className={`text-5xl md:text-6xl font-black ${stat.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      {stat.number}
                    </div>
                    <div className="text-slate-300 text-xl font-medium">
                      {stat.label}
                    </div>

                    {/* 底部装饰线 */}
                    <div className={`w-20 h-1 ${stat.color.replace('text-', 'bg-')} mx-auto mt-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300`} />
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Transition - 立即行动过渡 */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-orange-900/20 to-slate-900 overflow-hidden">
        {/* 能量场背景 */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(249,115,22,0.2),transparent_60%)]" />
          <div className="absolute inset-0 bg-[linear-gradient(45deg,rgba(249,115,22,0.1)_1px,transparent_1px),linear-gradient(-45deg,rgba(249,115,22,0.1)_1px,transparent_1px)] bg-[size:60px_60px]" />
        </div>

        {/* 中心能量环 */}
        <div className="absolute inset-0 flex items-center justify-center">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute border-2 border-orange-400/50 rounded-full"
              style={{
                width: 400 + i * 200,
                height: 400 + i * 200,
              }}
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.8, 0.3],
                rotate: [0, 180, 360],
              }}
              transition={{
                duration: 4 + i,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>

        {/* 闪电效果 */}
        <div className="absolute inset-0">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 bg-gradient-to-b from-yellow-400 to-orange-500"
              style={{
                left: `${10 + i * 10}%`,
                top: '20%',
                height: '60%',
                transformOrigin: 'top',
              }}
              animate={{
                scaleY: [0, 1, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 0.5,
                repeat: Infinity,
                delay: i * 0.2,
                repeatDelay: 2,
              }}
            />
          ))}
        </div>

        {/* 主标题 */}
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 2, ease: "easeOut" }}
          viewport={{ once: true }}
          className="relative z-10 text-center"
        >
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <h1 className="text-8xl md:text-9xl font-black text-transparent bg-clip-text bg-gradient-to-r from-orange-400 via-red-400 to-yellow-400 mb-8 tracking-wider">
              ACTION
            </h1>
            <h2 className="text-6xl md:text-7xl font-bold text-white mb-6 tracking-wide">
              REQUIRED
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-orange-400 to-red-400 mx-auto mb-8" />
            <p className="text-2xl text-orange-200 font-light tracking-widest">
              立即开始行动计划
            </p>
          </motion.div>
        </motion.div>

        {/* 底部指示器 */}
        <motion.div
          className="absolute bottom-16 left-1/2 transform -translate-x-1/2"
          animate={{
            y: [0, 20, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{ duration: 1.5, repeat: Infinity }}
        >
          <div className="flex flex-col items-center">
            <div className="w-px h-16 bg-gradient-to-b from-orange-400 to-transparent mb-4" />
            <Zap className="text-orange-400" size={24} />
          </div>
        </motion.div>
      </section>

      {/* CTA Section - 行动召唤 */}
      <section className="relative min-h-screen py-40 bg-slate-900 flex items-center justify-center">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="mb-12">
              <span className="inline-block px-6 py-3 bg-green-500/10 border border-green-500/20 rounded-full text-green-400 text-lg font-mono">
                Ready to Start
              </span>
            </div>
            <h2 className="text-5xl md:text-7xl font-black text-white mb-12 tracking-tight">
              准备好提升您的安全防护了吗？
            </h2>
            <p className="text-2xl text-slate-400 mb-16 max-w-4xl mx-auto leading-relaxed">
              联系我们的专家团队，获取定制化的网络安全解决方案
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <motion.button
                className="px-12 py-5 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl font-bold text-xl hover:shadow-2xl hover:shadow-blue-500/25 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                立即咨询
              </motion.button>
              <motion.button
                onClick={() => window.location.href = '/'}
                className="px-12 py-5 border-2 border-slate-600 text-slate-300 rounded-2xl font-bold text-xl hover:border-slate-400 hover:text-white transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                返回主页
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
