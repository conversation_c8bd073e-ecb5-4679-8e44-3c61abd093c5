'use client';

import React from 'react';
import { motion } from 'framer-motion';
import CyberLayout from '@/components/layout/CyberLayout';
import PhilosophyModule from '@/components/internal/PhilosophyModule';
import MatrixRain from '@/components/effects/MatrixRain';

/**
 * 🟢 内部宇宙版本页面
 * 展示深度哲学思考和技术修行内容
 */
export default function InternalPage() {
  return (
    <CyberLayout>
      {/* 背景效果 */}
      <div className="fixed inset-0 z-0">
        <MatrixRain 
          color="#00ff41" 
          speed={0.5}
          density={0.2}
        />
      </div>

      {/* 主要内容 */}
      <div className="relative z-10">
        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-8 bg-black/80 backdrop-blur-sm border-b border-green-500/30"
        >
          <h1 className="text-3xl md:text-5xl font-bold font-mono text-green-400 mb-2">
            内部宇宙版本
          </h1>
          <p className="text-lg text-purple-300 font-mono">
            Digital Philosophy & Technical Enlightenment
          </p>
          <div className="mt-2 text-sm text-gray-400">
            技术驱动，安全至上，代码无界 | 数字世界技术探索
          </div>
        </motion.div>

        {/* 哲学模块 */}
        <PhilosophyModule />

        {/* 底部导航 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          className="fixed bottom-4 left-4 right-4 z-20"
        >
          <div className="flex justify-center space-x-4">
            <motion.button
              onClick={() => window.location.href = '/'}
              className="px-4 py-2 bg-green-600/80 hover:bg-green-500/80 border border-green-400 rounded-lg font-mono text-sm transition-colors backdrop-blur-sm"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              ← 返回主页
            </motion.button>
            
            {/* 版本切换按钮 - 可通过开关控制显示 */}
            {false && (
              <motion.button
                onClick={() => window.location.href = '/commercial'}
                className="px-4 py-2 bg-red-600/80 hover:bg-red-500/80 border border-red-400 rounded-lg font-mono text-sm transition-colors backdrop-blur-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                切换到商业版本 →
              </motion.button>
            )}
          </div>
        </motion.div>
      </div>
    </CyberLayout>
  );
}
