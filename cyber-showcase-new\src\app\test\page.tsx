'use client';

import React from 'react';
import { motion } from 'framer-motion';
import CyberLayout from '@/components/layout/CyberLayout';
import CyberButton from '@/components/ui/CyberButton';
import CyberCard from '@/components/ui/CyberCard';
import HologramText from '@/components/ui/HologramText';
import { Zap, Shield, Code, Database } from 'lucide-react';

/**
 * 🧪 组件测试页面
 */
export default function TestPage() {
  return (
    <CyberLayout>
      <div className="min-h-screen pt-20 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          
          {/* 页面标题 */}
          <div className="text-center mb-12">
            <HologramText 
              text="组件测试实验室"
              effect="glitch"
              size="3xl"
              color="green"
              glowIntensity="high"
            />
            <p className="text-cyber-gold font-mono mt-4">
              测试所有赛博朋克UI组件的功能和效果
            </p>
          </div>

          {/* 按钮测试区域 */}
          <motion.section 
            className="mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <h2 className="text-2xl font-bold font-mono text-cyber-green mb-8 text-center">
              🔘 CyberButton 组件测试
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* 主要按钮 */}
              <div className="space-y-4">
                <h3 className="text-lg font-mono text-cyber-gold">主要按钮</h3>
                <CyberButton variant="primary" size="sm">
                  小型按钮
                </CyberButton>
                <CyberButton variant="primary" size="md" icon={<Zap size={16} />}>
                  中型按钮
                </CyberButton>
                <CyberButton variant="primary" size="lg" loading>
                  加载中...
                </CyberButton>
              </div>

              {/* 次要按钮 */}
              <div className="space-y-4">
                <h3 className="text-lg font-mono text-cyber-gold">次要按钮</h3>
                <CyberButton variant="secondary" size="md">
                  次要按钮
                </CyberButton>
                <CyberButton variant="ghost" size="md" icon={<Shield size={16} />}>
                  幽灵按钮
                </CyberButton>
                <CyberButton variant="danger" size="md">
                  危险按钮
                </CyberButton>
              </div>

              {/* 特殊效果按钮 */}
              <div className="space-y-4">
                <h3 className="text-lg font-mono text-cyber-gold">特殊效果</h3>
                <CyberButton variant="matrix" size="md" glowEffect>
                  Matrix风格
                </CyberButton>
                <CyberButton variant="hologram" size="md" pulseEffect>
                  全息投影
                </CyberButton>
                <CyberButton variant="primary" size="md" fullWidth>
                  全宽按钮
                </CyberButton>
              </div>
            </div>
          </motion.section>

          {/* 卡片测试区域 */}
          <motion.section 
            className="mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <h2 className="text-2xl font-bold font-mono text-cyber-green mb-8 text-center">
              🃏 CyberCard 组件测试
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* 默认卡片 */}
              <CyberCard variant="default" interactive>
                <h3 className="text-lg font-bold font-mono text-cyber-green mb-2">
                  默认卡片
                </h3>
                <p className="text-cyber-green/80 text-sm">
                  这是一个默认样式的赛博朋克卡片，具有基础的交互效果。
                </p>
              </CyberCard>

              {/* 悬浮卡片 */}
              <CyberCard variant="elevated" glowEffect>
                <h3 className="text-lg font-bold font-mono text-cyber-green mb-2">
                  悬浮卡片
                </h3>
                <p className="text-cyber-green/80 text-sm">
                  具有阴影和发光效果的悬浮卡片。
                </p>
              </CyberCard>

              {/* 玻璃态卡片 */}
              <CyberCard variant="glass" scanlineEffect>
                <h3 className="text-lg font-bold font-mono text-cyber-green mb-2">
                  玻璃态卡片
                </h3>
                <p className="text-cyber-green/80 text-sm">
                  毛玻璃效果配合扫描线动画。
                </p>
              </CyberCard>

              {/* Matrix卡片 */}
              <CyberCard variant="matrix" borderAnimation>
                <h3 className="text-lg font-bold font-mono text-cyber-green mb-2">
                  Matrix卡片
                </h3>
                <p className="text-cyber-green/80 text-sm">
                  Matrix风格背景配合边框动画。
                </p>
              </CyberCard>

              {/* 全息投影卡片 */}
              <CyberCard variant="hologram" interactive glowEffect>
                <h3 className="text-lg font-bold font-mono text-cyber-green mb-2">
                  全息投影
                </h3>
                <p className="text-cyber-green/80 text-sm">
                  全息投影效果的交互式卡片。
                </p>
              </CyberCard>

              {/* 霓虹灯卡片 */}
              <CyberCard variant="neon" size="lg">
                <h3 className="text-lg font-bold font-mono text-cyber-green mb-2">
                  霓虹灯效果
                </h3>
                <p className="text-cyber-green/80 text-sm">
                  霓虹灯边框效果的大尺寸卡片。
                </p>
              </CyberCard>
            </div>
          </motion.section>

          {/* 全息文本测试区域 */}
          <motion.section 
            className="mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <h2 className="text-2xl font-bold font-mono text-cyber-green mb-8 text-center">
              📝 HologramText 组件测试
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* 文本效果展示 */}
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-lg font-mono text-cyber-gold mb-4">打字机效果</h3>
                  <HologramText 
                    text="正在初始化系统..."
                    effect="typewriter"
                    size="lg"
                    color="green"
                    speed="normal"
                    loop
                  />
                </div>

                <div className="text-center">
                  <h3 className="text-lg font-mono text-cyber-gold mb-4">故障效果</h3>
                  <HologramText 
                    text="SYSTEM ERROR"
                    effect="glitch"
                    size="xl"
                    color="red"
                    speed="fast"
                    loop
                  />
                </div>

                <div className="text-center">
                  <h3 className="text-lg font-mono text-cyber-gold mb-4">解码效果</h3>
                  <HologramText 
                    text="ACCESS GRANTED"
                    effect="decode"
                    size="lg"
                    color="gold"
                    speed="normal"
                    loop
                  />
                </div>
              </div>

              {/* 不同颜色和尺寸 */}
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-lg font-mono text-cyber-gold mb-4">闪烁效果</h3>
                  <HologramText 
                    text="WARNING: INTRUSION DETECTED"
                    effect="flicker"
                    size="md"
                    color="red"
                    loop
                  />
                </div>

                <div className="text-center">
                  <h3 className="text-lg font-mono text-cyber-gold mb-4">波浪效果</h3>
                  <HologramText 
                    text="数据流传输中..."
                    effect="wave"
                    size="lg"
                    color="cyan"
                    loop
                  />
                </div>

                <div className="text-center">
                  <h3 className="text-lg font-mono text-cyber-gold mb-4">扫描效果</h3>
                  <HologramText 
                    text="SCANNING NETWORK..."
                    effect="scan"
                    size="md"
                    color="purple"
                    loop
                  />
                </div>
              </div>
            </div>
          </motion.section>

          {/* 综合展示区域 */}
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <h2 className="text-2xl font-bold font-mono text-cyber-green mb-8 text-center">
              🎭 综合效果展示
            </h2>
            
            <CyberCard 
              variant="hologram" 
              size="lg" 
              interactive 
              glowEffect 
              borderAnimation
              className="max-w-4xl mx-auto"
            >
              <div className="text-center space-y-6">
                <HologramText 
                  text="神秘技术组织"
                  effect="glitch"
                  size="2xl"
                  color="green"
                  glowIntensity="high"
                />
                
                <HologramText 
                  text="众生即我，我即众生，宇宙之卵"
                  effect="typewriter"
                  size="lg"
                  color="gold"
                  speed="slow"
                />

                <div className="flex flex-wrap justify-center gap-4 mt-8">
                  <CyberButton variant="primary" icon={<Code size={16} />}>
                    ZenCode
                  </CyberButton>
                  <CyberButton variant="secondary" icon={<Database size={16} />}>
                    Spore
                  </CyberButton>
                  <CyberButton variant="matrix" icon={<Shield size={16} />}>
                    Security
                  </CyberButton>
                </div>
              </div>
            </CyberCard>
          </motion.section>

        </div>
      </div>
    </CyberLayout>
  );
}
