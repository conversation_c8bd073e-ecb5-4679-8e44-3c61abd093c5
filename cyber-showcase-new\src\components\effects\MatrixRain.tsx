'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';

interface MatrixColumn {
  x: number;
  y: number;
  speed: number;
  chars: string[];
  opacity: number;
}

/**
 * 🌧️ Matrix代码雨效果组件
 */
const MatrixRain: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const columnsRef = useRef<MatrixColumn[]>([]);
  const [isVisible, setIsVisible] = useState(true);

  // 🔤 Matrix字符集
  const matrixChars = [
    // 数字
    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
    // 英文字母
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
    'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
    // 特殊符号
    '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '-', '+', '=',
    '[', ']', '{', '}', '|', '\\', ':', ';', '"', "'", '<', '>', ',', '.', '?', '/',
    // 日文片假名（Matrix经典）
    'ア', 'イ', 'ウ', 'エ', 'オ', 'カ', 'キ', 'ク', 'ケ', 'コ',
    'サ', 'シ', 'ス', 'セ', 'ソ', 'タ', 'チ', 'ツ', 'テ', 'ト',
    'ナ', 'ニ', 'ヌ', 'ネ', 'ノ', 'ハ', 'ヒ', 'フ', 'ヘ', 'ホ',
    'マ', 'ミ', 'ム', 'メ', 'モ', 'ヤ', 'ユ', 'ヨ', 'ラ', 'リ',
    'ル', 'レ', 'ロ', 'ワ', 'ヲ', 'ン'
  ];

  // 🎨 初始化Canvas和列
  const initializeMatrix = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置Canvas尺寸
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    // 计算列数
    const fontSize = 14;
    const columnWidth = fontSize;
    const columnCount = Math.floor(canvas.width / columnWidth);

    // 初始化列
    columnsRef.current = [];
    for (let i = 0; i < columnCount; i++) {
      columnsRef.current.push({
        x: i * columnWidth,
        y: Math.random() * canvas.height,
        speed: Math.random() * 3 + 1, // 1-4的随机速度
        chars: [],
        opacity: Math.random() * 0.5 + 0.5 // 0.5-1的随机透明度
      });
    }

    // 为每列生成字符
    columnsRef.current.forEach(column => {
      const charCount = Math.floor(Math.random() * 20) + 10; // 10-30个字符
      for (let j = 0; j < charCount; j++) {
        column.chars.push(
          matrixChars[Math.floor(Math.random() * matrixChars.length)]
        );
      }
    });
  };

  // 🎬 动画循环
  const animate = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清除画布（带有轻微的拖尾效果）
    ctx.fillStyle = 'rgba(10, 10, 35, 0.05)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 设置字体
    ctx.font = '14px JetBrains Mono, monospace';
    ctx.textAlign = 'center';

    // 绘制每一列
    columnsRef.current.forEach((column, columnIndex) => {
      column.chars.forEach((char, charIndex) => {
        const y = column.y + charIndex * 16;
        
        // 如果字符超出屏幕底部，重置列
        if (y > canvas.height + 100) {
          if (charIndex === 0) {
            column.y = -column.chars.length * 16;
            column.speed = Math.random() * 3 + 1;
            column.opacity = Math.random() * 0.5 + 0.5;
            
            // 随机更换一些字符
            if (Math.random() < 0.1) {
              column.chars[Math.floor(Math.random() * column.chars.length)] = 
                matrixChars[Math.floor(Math.random() * matrixChars.length)];
            }
          }
          return;
        }

        // 计算字符的透明度（头部更亮）
        const distanceFromHead = charIndex;
        const maxDistance = column.chars.length;
        const alpha = Math.max(0, (maxDistance - distanceFromHead) / maxDistance) * column.opacity;

        // 头部字符使用白色，其他使用绿色
        if (charIndex === 0) {
          ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
        } else if (charIndex < 3) {
          ctx.fillStyle = `rgba(180, 255, 180, ${alpha})`;
        } else {
          ctx.fillStyle = `rgba(0, 255, 65, ${alpha})`;
        }

        // 绘制字符
        ctx.fillText(char, column.x + 7, y);

        // 随机闪烁效果
        if (Math.random() < 0.01) {
          ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.8})`;
          ctx.fillText(char, column.x + 7, y);
        }
      });

      // 更新列位置
      column.y += column.speed;
    });

    // 继续动画
    if (isVisible) {
      animationRef.current = requestAnimationFrame(animate);
    }
  };

  // 🚀 组件挂载
  useEffect(() => {
    initializeMatrix();
    animate();

    // 窗口大小变化处理
    const handleResize = () => {
      initializeMatrix();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isVisible]);

  // 🎯 可见性控制
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  return (
    <motion.div
      className="fixed inset-0 pointer-events-none"
      initial={{ opacity: 0 }}
      animate={{ opacity: 0.6 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 2 }}
    >
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{
          background: 'transparent',
          mixBlendMode: 'screen'
        }}
      />
      
      {/* 🔮 顶部渐变遮罩 */}
      <div 
        className="absolute top-0 left-0 w-full h-32 pointer-events-none"
        style={{
          background: 'linear-gradient(to bottom, rgba(10, 10, 35, 0.8) 0%, transparent 100%)'
        }}
      />
      
      {/* 🔮 底部渐变遮罩 */}
      <div 
        className="absolute bottom-0 left-0 w-full h-32 pointer-events-none"
        style={{
          background: 'linear-gradient(to top, rgba(10, 10, 35, 0.8) 0%, transparent 100%)'
        }}
      />
    </motion.div>
  );
};

export default MatrixRain;
