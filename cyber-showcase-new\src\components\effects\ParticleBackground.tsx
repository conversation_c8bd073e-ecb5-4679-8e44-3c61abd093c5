'use client';

import React, { useRef, useEffect, useMemo } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Points, PointMaterial } from '@react-three/drei';
import * as THREE from 'three';

/**
 * 🌟 3D粒子系统组件
 */
const ParticleSystem: React.FC = () => {
  const ref = useRef<THREE.Points>(null);
  const { viewport } = useThree();

  // 🎯 生成粒子位置
  const particlePositions = useMemo(() => {
    const positions = new Float32Array(2000 * 3); // 2000个粒子，每个3个坐标
    
    for (let i = 0; i < 2000; i++) {
      const i3 = i * 3;
      // 在更大的空间中随机分布粒子
      positions[i3] = (Math.random() - 0.5) * 20; // x
      positions[i3 + 1] = (Math.random() - 0.5) * 20; // y  
      positions[i3 + 2] = (Math.random() - 0.5) * 20; // z
    }
    
    return positions;
  }, []);

  // ⚡ 动画循环
  useFrame((state) => {
    if (ref.current) {
      // 缓慢旋转整个粒子系统
      ref.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.1;
      ref.current.rotation.y = state.clock.elapsedTime * 0.05;
      
      // 粒子呼吸效果
      const scale = 1 + Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      ref.current.scale.setScalar(scale);
    }
  });

  return (
    <Points ref={ref} positions={particlePositions} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        color="#00ff41"
        size={0.02}
        sizeAttenuation={true}
        depthWrite={false}
        blending={THREE.AdditiveBlending}
      />
    </Points>
  );
};

/**
 * 🌌 连接线系统组件
 */
const ConnectionLines: React.FC = () => {
  const ref = useRef<THREE.LineSegments>(null);
  
  const { positions, colors } = useMemo(() => {
    const particleCount = 100;
    const maxConnections = 200;
    const maxDistance = 3;
    
    // 生成粒子位置
    const particles: THREE.Vector3[] = [];
    for (let i = 0; i < particleCount; i++) {
      particles.push(new THREE.Vector3(
        (Math.random() - 0.5) * 15,
        (Math.random() - 0.5) * 15,
        (Math.random() - 0.5) * 15
      ));
    }
    
    const linePositions: number[] = [];
    const lineColors: number[] = [];
    
    // 计算连接
    let connectionCount = 0;
    for (let i = 0; i < particleCount && connectionCount < maxConnections; i++) {
      for (let j = i + 1; j < particleCount && connectionCount < maxConnections; j++) {
        const distance = particles[i].distanceTo(particles[j]);
        
        if (distance < maxDistance) {
          // 添加线段
          linePositions.push(
            particles[i].x, particles[i].y, particles[i].z,
            particles[j].x, particles[j].y, particles[j].z
          );
          
          // 根据距离计算透明度
          const alpha = 1 - (distance / maxDistance);
          lineColors.push(0, 1, 0.25, alpha); // 绿色
          lineColors.push(0, 1, 0.25, alpha);
          
          connectionCount++;
        }
      }
    }
    
    return {
      positions: new Float32Array(linePositions),
      colors: new Float32Array(lineColors)
    };
  }, []);

  useFrame((state) => {
    if (ref.current) {
      // 连接线的脉动效果
      const material = ref.current.material as THREE.LineBasicMaterial;
      if (material) {
        material.opacity = 0.3 + Math.sin(state.clock.elapsedTime * 2) * 0.2;
      }
    }
  });

  return (
    <lineSegments ref={ref}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          array={positions}
          count={positions.length / 3}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-color"
          array={colors}
          count={colors.length / 4}
          itemSize={4}
        />
      </bufferGeometry>
      <lineBasicMaterial
        transparent
        vertexColors
        blending={THREE.AdditiveBlending}
        opacity={0.3}
      />
    </lineSegments>
  );
};

/**
 * 🔮 主粒子背景组件
 */
const ParticleBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 🎨 Canvas样式
  const canvasStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    zIndex: -1,
    pointerEvents: 'none'
  };

  // 📱 响应式相机设置
  const cameraProps = {
    position: [0, 0, 10] as [number, number, number],
    fov: 60,
    near: 0.1,
    far: 100
  };

  return (
    <div className="particle-bg">
      <Canvas
        ref={canvasRef}
        style={canvasStyle}
        camera={cameraProps}
        gl={{
          alpha: true,
          antialias: true,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]} // 设备像素比
      >
        {/* 🌟 环境光 */}
        <ambientLight intensity={0.1} />
        
        {/* 🎯 主粒子系统 */}
        <ParticleSystem />
        
        {/* 🌐 连接线系统 */}
        <ConnectionLines />
        
        {/* 🔮 额外的装饰粒子 */}
        <Points
          positions={useMemo(() => {
            const positions = new Float32Array(500 * 3);
            for (let i = 0; i < 500; i++) {
              const i3 = i * 3;
              positions[i3] = (Math.random() - 0.5) * 30;
              positions[i3 + 1] = (Math.random() - 0.5) * 30;
              positions[i3 + 2] = (Math.random() - 0.5) * 30;
            }
            return positions;
          }, [])}
          stride={3}
        >
          <PointMaterial
            transparent
            color="#ffd700"
            size={0.01}
            sizeAttenuation={true}
            depthWrite={false}
            blending={THREE.AdditiveBlending}
            opacity={0.6}
          />
        </Points>
      </Canvas>
    </div>
  );
};

export default ParticleBackground;
