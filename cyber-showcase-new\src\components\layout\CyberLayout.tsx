'use client';

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '@/store/app-store';
import { globalPathDetector } from '@/lib/path-detector';
import ParticleBackground from '@/components/effects/ParticleBackground';
import MatrixRain from '@/components/effects/MatrixRain';
import CyberNavigation from '@/components/navigation/CyberNavigation';
import LoadingScreen from '@/components/ui/LoadingScreen';

interface CyberLayoutProps {
  children: React.ReactNode;
}

/**
 * 🔮 赛博朋克主布局组件
 * 提供全局的视觉效果、导航和状态管理
 */
const CyberLayout: React.FC<CyberLayoutProps> = ({ children }) => {
  const {
    theme,
    showParticles,
    enableAnimations,
    isLoading,
    loadingMessage,
    userType,
    setUserType,
    setPathRecommendation,
    recordInteraction,
    setBehaviorData
  } = useAppStore();

  const [mounted, setMounted] = useState(false);
  const [userDetected, setUserDetected] = useState(false);

  // 🚀 组件挂载和用户检测
  useEffect(() => {
    setMounted(true);
    
    // 记录初始访问
    recordInteraction('page_load');
    
    // 检测用户环境
    const detectUserEnvironment = () => {
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const userAgent = navigator.userAgent;
      const referrer = document.referrer;
      
      setBehaviorData({
        isMobile,
        userAgent,
        referrer
      });
      
      // 初始路径检测
      globalPathDetector.recordBehavior({
        isMobile,
        userAgent,
        referrer
      });
      
      // 延迟进行用户类型检测
      setTimeout(() => {
        const recommendation = globalPathDetector.detectUserType();
        setPathRecommendation(recommendation);
        setUserType(recommendation.userType);
        setUserDetected(true);
      }, 2000);
    };

    detectUserEnvironment();
  }, [recordInteraction, setBehaviorData, setPathRecommendation, setUserType]);

  // 🎯 页面可见性检测
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        recordInteraction('page_hidden');
      } else {
        recordInteraction('page_visible');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [recordInteraction]);

  // 🎨 主题样式计算
  const getThemeClasses = () => {
    const baseClasses = 'min-h-screen relative overflow-hidden';
    
    switch (theme) {
      case 'mystical':
        return `${baseClasses} bg-gradient-to-br from-cyber-dark via-cyber-black to-cyber-dark text-cyber-green`;
      case 'business':
        return `${baseClasses} bg-gradient-to-br from-slate-50 via-white to-slate-100 text-slate-800`;
      case 'adaptive':
        return `${baseClasses} bg-gradient-to-br from-cyber-dark via-slate-900 to-cyber-black text-cyber-green`;
      default:
        return `${baseClasses} matrix-bg text-cyber-green`;
    }
  };

  // 🎬 动画变体
  const layoutVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: { 
        duration: 1,
        ease: "easeOut"
      }
    },
    exit: { 
      opacity: 0,
      transition: { 
        duration: 0.5
      }
    }
  };

  const contentVariants = {
    initial: { y: 20, opacity: 0 },
    animate: { 
      y: 0, 
      opacity: 1,
      transition: { 
        delay: 0.3,
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  // 🔄 加载状态处理
  if (!mounted) {
    return <LoadingScreen message="初始化赛博空间..." />;
  }

  if (isLoading) {
    return <LoadingScreen message={loadingMessage || "加载中..."} />;
  }

  return (
    <motion.div
      className={getThemeClasses()}
      variants={enableAnimations ? layoutVariants : undefined}
      initial="initial"
      animate="animate"
      exit="exit"
    >
      {/* 🌟 背景效果层 */}
      <div className="fixed inset-0 z-0">
        {/* 粒子背景 */}
        <AnimatePresence>
          {showParticles && theme === 'mystical' && (
            <ParticleBackground key="particles" />
          )}
        </AnimatePresence>

        {/* Matrix雨效果 */}
        <AnimatePresence>
          {enableAnimations && theme === 'mystical' && (
            <MatrixRain key="matrix" />
          )}
        </AnimatePresence>

        {/* 商务主题背景 */}
        {theme === 'business' && (
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-slate-50 opacity-90" />
        )}

        {/* 自适应主题背景 */}
        {theme === 'adaptive' && (
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-gradient-to-br from-cyber-dark via-slate-900 to-cyber-black opacity-80" />
            <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-cyber-green/5 to-transparent" />
          </div>
        )}
      </div>

      {/* 🧭 导航层 */}
      <div className="relative z-20">
        <CyberNavigation />
      </div>

      {/* 📱 主内容层 */}
      <motion.main
        className="relative z-10 min-h-screen"
        variants={enableAnimations ? contentVariants : undefined}
        initial="initial"
        animate="animate"
      >
        {children}
      </motion.main>

      {/* 🎯 用户类型检测提示 */}
      <AnimatePresence>
        {userDetected && userType !== 'mixed-track' && (
          <motion.div
            className="fixed bottom-4 right-4 z-30 max-w-sm"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            transition={{ duration: 0.5 }}
          >
            <div className="cyber-bg cyber-border rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-cyber-green rounded-full animate-cyber-pulse" />
                <span className="text-sm font-mono">
                  {userType === 'tech-track' ? '🔧 技术路径已激活' : '💼 商务路径已激活'}
                </span>
              </div>
              <p className="text-xs text-cyber-gold mt-1">
                基于您的行为模式，我们为您定制了专属展示内容
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 🎵 音效控制 */}
      <div className="fixed bottom-4 left-4 z-30">
        <div className="flex space-x-2">
          <button
            onClick={() => useAppStore.getState().toggleSound()}
            className="cyber-border rounded-full p-2 cyber-bg hover:bg-cyber-green/20 transition-colors"
            title="切换音效"
          >
            <span className="text-xs">🔊</span>
          </button>
          <button
            onClick={() => useAppStore.getState().toggleBackgroundMusic()}
            className="cyber-border rounded-full p-2 cyber-bg hover:bg-cyber-green/20 transition-colors"
            title="切换背景音乐"
          >
            <span className="text-xs">🎵</span>
          </button>
        </div>
      </div>

      {/* 🔮 神秘元素 - 地藏王菩萨符号 */}
      {theme === 'mystical' && (
        <div className="fixed top-4 left-4 z-30 opacity-30">
          <motion.div
            className="text-cyber-gold text-2xl"
            animate={{ 
              rotate: [0, 360],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
          >
            ☸️
          </motion.div>
        </div>
      )}

      {/* 📊 开发调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-4 right-4 z-50 text-xs font-mono bg-black/80 text-green-400 p-2 rounded">
          <div>主题: {theme}</div>
          <div>用户类型: {userType}</div>
          <div>动画: {enableAnimations ? '开启' : '关闭'}</div>
          <div>粒子: {showParticles ? '开启' : '关闭'}</div>
        </div>
      )}
    </motion.div>
  );
};

export default CyberLayout;
