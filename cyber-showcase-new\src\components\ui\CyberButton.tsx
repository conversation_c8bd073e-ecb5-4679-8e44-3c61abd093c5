'use client';

import React, { forwardRef } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

// 🎨 按钮变体类型
export type CyberButtonVariant = 
  | 'primary'     // 主要按钮 - 绿色发光
  | 'secondary'   // 次要按钮 - 金色边框
  | 'ghost'       // 幽灵按钮 - 透明背景
  | 'danger'      // 危险按钮 - 红色警告
  | 'success'     // 成功按钮 - 绿色确认
  | 'matrix'      // Matrix风格 - 代码雨效果
  | 'hologram';   // 全息投影效果

// 🎯 按钮尺寸类型
export type CyberButtonSize = 'sm' | 'md' | 'lg' | 'xl';

// 🔧 按钮属性接口
export interface CyberButtonProps extends Omit<HTMLMotionProps<'button'>, 'size'> {
  variant?: CyberButtonVariant;
  size?: CyberButtonSize;
  loading?: boolean;
  loadingText?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  glowEffect?: boolean;
  pulseEffect?: boolean;
  soundEffect?: boolean;
  children: React.ReactNode;
}

/**
 * 🔮 赛博朋克风格按钮组件
 */
const CyberButton = forwardRef<HTMLButtonElement, CyberButtonProps>(({
  variant = 'primary',
  size = 'md',
  loading = false,
  loadingText = '加载中...',
  icon,
  iconPosition = 'left',
  fullWidth = false,
  glowEffect = true,
  pulseEffect = false,
  soundEffect = true,
  className,
  children,
  disabled,
  onClick,
  ...props
}, ref) => {
  
  // 🎨 获取变体样式
  const getVariantStyles = (variant: CyberButtonVariant) => {
    const styles = {
      primary: 'bg-cyber-green/20 border-cyber-green text-cyber-green hover:bg-cyber-green hover:text-cyber-dark cyber-glow',
      secondary: 'bg-transparent border-cyber-gold text-cyber-gold hover:bg-cyber-gold/20 hover:text-cyber-gold',
      ghost: 'bg-transparent border-transparent text-cyber-green hover:bg-cyber-green/10 hover:border-cyber-green/50',
      danger: 'bg-red-500/20 border-red-500 text-red-400 hover:bg-red-500 hover:text-white',
      success: 'bg-green-500/20 border-green-500 text-green-400 hover:bg-green-500 hover:text-white',
      matrix: 'bg-cyber-dark/80 border-cyber-green text-cyber-green hover:bg-cyber-green/10 matrix-bg',
      hologram: 'bg-gradient-to-r from-cyber-green/10 to-cyber-gold/10 border-cyber-green text-cyber-green hover:from-cyber-green/20 hover:to-cyber-gold/20 hologram-effect'
    };
    return styles[variant];
  };

  // 📏 获取尺寸样式
  const getSizeStyles = (size: CyberButtonSize) => {
    const styles = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg',
      xl: 'px-8 py-4 text-xl'
    };
    return styles[size];
  };

  // 🎵 音效处理
  const playSound = (soundType: 'hover' | 'click') => {
    if (!soundEffect) return;
    
    try {
      const audio = document.getElementById(`cyber-sound-${soundType}`) as HTMLAudioElement;
      if (audio) {
        audio.currentTime = 0;
        audio.play().catch(() => {
          // 静默处理音频播放失败
        });
      }
    } catch (error) {
      // 静默处理错误
    }
  };

  // 🎯 点击处理
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (loading || disabled) return;
    
    playSound('click');
    onClick?.(event);
  };

  // 🎬 动画变体
  const buttonVariants = {
    initial: { scale: 1 },
    hover: { 
      scale: 1.02,
      transition: { duration: 0.2 }
    },
    tap: { 
      scale: 0.98,
      transition: { duration: 0.1 }
    }
  };

  const glowVariants = {
    animate: glowEffect ? {
      boxShadow: [
        '0 0 5px currentColor',
        '0 0 20px currentColor',
        '0 0 5px currentColor'
      ],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    } : {}
  };

  const pulseVariants = {
    animate: pulseEffect ? {
      scale: [1, 1.05, 1],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    } : {}
  };

  // 🎨 组合样式类
  const buttonClasses = cn(
    // 基础样式
    'relative inline-flex items-center justify-center',
    'font-mono font-medium',
    'border-2 rounded-lg',
    'transition-all duration-300',
    'focus:outline-none focus:ring-2 focus:ring-cyber-green/50',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    'overflow-hidden',
    
    // 变体样式
    getVariantStyles(variant),
    
    // 尺寸样式
    getSizeStyles(size),
    
    // 宽度样式
    fullWidth && 'w-full',
    
    // 自定义样式
    className
  );

  return (
    <motion.button
      ref={ref}
      className={buttonClasses}
      variants={buttonVariants}
      initial="initial"
      whileHover="hover"
      whileTap="tap"
      animate={[
        glowEffect ? 'animate' : '',
        pulseEffect ? 'animate' : ''
      ].filter(Boolean)}
      disabled={disabled || loading}
      onClick={handleClick}
      onMouseEnter={() => playSound('hover')}
      {...props}
    >
      {/* 🌟 背景效果层 */}
      {variant === 'hologram' && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyber-green/5 to-transparent animate-pulse" />
      )}
      
      {variant === 'matrix' && (
        <div className="absolute inset-0 opacity-20">
          <div className="matrix-rain-mini" />
        </div>
      )}

      {/* 📱 内容区域 */}
      <div className="relative flex items-center justify-center space-x-2">
        {/* 左侧图标 */}
        {icon && iconPosition === 'left' && !loading && (
          <span className="flex-shrink-0">
            {icon}
          </span>
        )}

        {/* 加载指示器 */}
        {loading && (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            className="flex-shrink-0"
          >
            <Loader2 size={size === 'sm' ? 14 : size === 'md' ? 16 : size === 'lg' ? 18 : 20} />
          </motion.div>
        )}

        {/* 文本内容 */}
        <span className="flex-1 text-center">
          {loading ? loadingText : children}
        </span>

        {/* 右侧图标 */}
        {icon && iconPosition === 'right' && !loading && (
          <span className="flex-shrink-0">
            {icon}
          </span>
        )}
      </div>

      {/* 🎯 点击波纹效果 */}
      <motion.div
        className="absolute inset-0 bg-current opacity-0 rounded-lg"
        initial={{ scale: 0, opacity: 0 }}
        whileTap={{ 
          scale: 1, 
          opacity: 0.1,
          transition: { duration: 0.2 }
        }}
      />

      {/* ⚡ 发光边框效果 */}
      {glowEffect && (
        <motion.div
          className="absolute inset-0 border-2 border-current rounded-lg opacity-0"
          variants={glowVariants}
          animate="animate"
        />
      )}
    </motion.button>
  );
});

CyberButton.displayName = 'CyberButton';

export default CyberButton;
