'use client';

import React, { forwardRef, useState } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { cn } from '@/lib/utils';

// 🎨 卡片变体类型
export type CyberCardVariant = 
  | 'default'     // 默认样式
  | 'elevated'    // 悬浮效果
  | 'outlined'    // 边框样式
  | 'glass'       // 玻璃态
  | 'matrix'      // Matrix风格
  | 'hologram'    // 全息投影
  | 'neon';       // 霓虹灯效果

// 🎯 卡片尺寸类型
export type CyberCardSize = 'sm' | 'md' | 'lg' | 'xl';

// 🔧 卡片属性接口
export interface CyberCardProps extends Omit<HTMLMotionProps<'div'>, 'size'> {
  variant?: CyberCardVariant;
  size?: CyberCardSize;
  interactive?: boolean;
  glowEffect?: boolean;
  scanlineEffect?: boolean;
  borderAnimation?: boolean;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * 🔮 赛博朋克风格卡片组件
 */
const CyberCard = forwardRef<HTMLDivElement, CyberCardProps>(({
  variant = 'default',
  size = 'md',
  interactive = false,
  glowEffect = false,
  scanlineEffect = false,
  borderAnimation = false,
  header,
  footer,
  className,
  children,
  ...props
}, ref) => {
  
  const [isHovered, setIsHovered] = useState(false);

  // 🎨 获取变体样式
  const getVariantStyles = (variant: CyberCardVariant) => {
    const styles = {
      default: 'bg-cyber-dark/80 border-cyber-green/30 backdrop-blur-sm',
      elevated: 'bg-cyber-dark/90 border-cyber-green/50 shadow-lg shadow-cyber-green/20',
      outlined: 'bg-transparent border-cyber-green border-2',
      glass: 'bg-white/5 border-white/10 backdrop-blur-md',
      matrix: 'bg-cyber-dark/95 border-cyber-green matrix-bg',
      hologram: 'bg-gradient-to-br from-cyber-green/10 to-cyber-gold/10 border-cyber-green hologram-effect',
      neon: 'bg-cyber-dark/80 border-cyber-green neon-glow'
    };
    return styles[variant];
  };

  // 📏 获取尺寸样式
  const getSizeStyles = (size: CyberCardSize) => {
    const styles = {
      sm: 'p-3 rounded-md',
      md: 'p-4 rounded-lg',
      lg: 'p-6 rounded-xl',
      xl: 'p-8 rounded-2xl'
    };
    return styles[size];
  };

  // 🎬 动画变体
  const cardVariants = {
    initial: { 
      scale: 1,
      rotateX: 0,
      rotateY: 0
    },
    hover: interactive ? {
      scale: 1.02,
      rotateX: 2,
      rotateY: 2,
      transition: { 
        duration: 0.3,
        ease: "easeOut"
      }
    } : {},
    tap: interactive ? {
      scale: 0.98,
      transition: { 
        duration: 0.1
      }
    } : {}
  };

  const glowVariants = {
    animate: glowEffect ? {
      boxShadow: [
        '0 0 10px rgba(0, 255, 65, 0.3)',
        '0 0 30px rgba(0, 255, 65, 0.5)',
        '0 0 10px rgba(0, 255, 65, 0.3)'
      ],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    } : {}
  };

  const borderVariants = {
    animate: borderAnimation ? {
      borderColor: [
        'rgba(0, 255, 65, 0.3)',
        'rgba(255, 215, 0, 0.5)',
        'rgba(0, 255, 65, 0.3)'
      ],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    } : {}
  };

  // 🎨 组合样式类
  const cardClasses = cn(
    // 基础样式
    'relative border transition-all duration-300',
    'overflow-hidden',
    
    // 变体样式
    getVariantStyles(variant),
    
    // 尺寸样式
    getSizeStyles(size),
    
    // 交互样式
    interactive && 'cursor-pointer select-none',
    
    // 自定义样式
    className
  );

  return (
    <motion.div
      ref={ref}
      className={cardClasses}
      variants={cardVariants}
      initial="initial"
      whileHover="hover"
      whileTap="tap"
      animate={[
        glowEffect ? 'animate' : '',
        borderAnimation ? 'animate' : ''
      ].filter(Boolean)}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      style={{ perspective: '1000px' }}
      {...props}
    >
      {/* 🌟 背景效果层 */}
      {variant === 'matrix' && (
        <div className="absolute inset-0 opacity-10">
          <div className="matrix-rain-card" />
        </div>
      )}

      {variant === 'hologram' && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyber-green/5 to-transparent animate-pulse" />
      )}

      {/* 📺 扫描线效果 */}
      {scanlineEffect && (
        <div className="absolute inset-0 pointer-events-none">
          <motion.div
            className="absolute inset-0 bg-gradient-to-b from-transparent via-cyber-green/10 to-transparent"
            animate={{
              y: ['-100%', '100%'],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'linear'
            }}
            style={{ height: '20%' }}
          />
        </div>
      )}

      {/* 🔥 发光边框效果 */}
      {glowEffect && isHovered && (
        <motion.div
          className="absolute inset-0 border-2 border-cyber-green rounded-lg opacity-0"
          variants={glowVariants}
          animate="animate"
        />
      )}

      {/* 🎯 边框动画效果 */}
      {borderAnimation && (
        <>
          {/* 顶部边框 */}
          <motion.div
            className="absolute top-0 left-0 h-0.5 bg-cyber-green"
            animate={{
              width: ['0%', '100%', '0%'],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />
          
          {/* 右侧边框 */}
          <motion.div
            className="absolute top-0 right-0 w-0.5 bg-cyber-gold"
            animate={{
              height: ['0%', '100%', '0%'],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 0.5
            }}
          />
          
          {/* 底部边框 */}
          <motion.div
            className="absolute bottom-0 right-0 h-0.5 bg-cyber-green"
            animate={{
              width: ['0%', '100%', '0%'],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 1
            }}
          />
          
          {/* 左侧边框 */}
          <motion.div
            className="absolute bottom-0 left-0 w-0.5 bg-cyber-gold"
            animate={{
              height: ['0%', '100%', '0%'],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 1.5
            }}
          />
        </>
      )}

      {/* 📱 内容区域 */}
      <div className="relative z-10 h-full flex flex-col">
        {/* 头部区域 */}
        {header && (
          <div className="mb-4 pb-4 border-b border-current/20">
            {header}
          </div>
        )}

        {/* 主要内容 */}
        <div className="flex-1">
          {children}
        </div>

        {/* 底部区域 */}
        {footer && (
          <div className="mt-4 pt-4 border-t border-current/20">
            {footer}
          </div>
        )}
      </div>

      {/* 🎯 交互反馈 */}
      {interactive && (
        <motion.div
          className="absolute inset-0 bg-cyber-green/5 opacity-0 rounded-lg"
          animate={{
            opacity: isHovered ? 1 : 0
          }}
          transition={{ duration: 0.2 }}
        />
      )}

      {/* 🌐 霓虹灯效果 */}
      {variant === 'neon' && (
        <div className="absolute inset-0 rounded-lg">
          <div className="absolute inset-0 rounded-lg border-2 border-cyber-green animate-pulse opacity-50" />
          <div className="absolute inset-0 rounded-lg border border-cyber-gold animate-pulse opacity-30" 
               style={{ animationDelay: '0.5s' }} />
        </div>
      )}
    </motion.div>
  );
});

CyberCard.displayName = 'CyberCard';

export default CyberCard;
