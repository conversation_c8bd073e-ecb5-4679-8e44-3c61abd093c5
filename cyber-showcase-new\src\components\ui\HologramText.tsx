'use client';

import React, { useState, useEffect, forwardRef } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { cn } from '@/lib/utils';

// 🎨 全息文本效果类型
export type HologramEffect = 
  | 'glitch'      // 故障效果
  | 'typewriter'  // 打字机效果
  | 'matrix'      // Matrix数字雨
  | 'scan'        // 扫描线效果
  | 'flicker'     // 闪烁效果
  | 'wave'        // 波浪效果
  | 'decode';     // 解码效果

// 🎯 文本尺寸类型
export type HologramSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';

// 🔧 全息文本属性接口
export interface HologramTextProps extends Omit<HTMLMotionProps<'div'>, 'size'> {
  text: string;
  effect?: HologramEffect;
  size?: HologramSize;
  color?: 'green' | 'gold' | 'blue' | 'red' | 'purple' | 'cyan';
  speed?: 'slow' | 'normal' | 'fast';
  loop?: boolean;
  autoStart?: boolean;
  glowIntensity?: 'low' | 'medium' | 'high';
  onComplete?: () => void;
}

/**
 * 🔮 全息投影文本组件
 */
const HologramText = forwardRef<HTMLDivElement, HologramTextProps>(({
  text,
  effect = 'glitch',
  size = 'md',
  color = 'green',
  speed = 'normal',
  loop = false,
  autoStart = true,
  glowIntensity = 'medium',
  onComplete,
  className,
  ...props
}, ref) => {
  
  const [displayText, setDisplayText] = useState('');
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  // 🎨 获取颜色样式
  const getColorStyles = (color: string) => {
    const colors = {
      green: 'text-cyber-green',
      gold: 'text-cyber-gold',
      blue: 'text-blue-400',
      red: 'text-red-400',
      purple: 'text-purple-400',
      cyan: 'text-cyan-400'
    };
    return colors[color as keyof typeof colors] || colors.green;
  };

  // 📏 获取尺寸样式
  const getSizeStyles = (size: HologramSize) => {
    const sizes = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl',
      '2xl': 'text-2xl',
      '3xl': 'text-3xl'
    };
    return sizes[size];
  };

  // ⚡ 获取速度配置
  const getSpeedConfig = (speed: string) => {
    const configs = {
      slow: { typewriter: 150, glitch: 300, decode: 200 },
      normal: { typewriter: 100, glitch: 200, decode: 150 },
      fast: { typewriter: 50, glitch: 100, decode: 100 }
    };
    return configs[speed as keyof typeof configs] || configs.normal;
  };

  // 🎯 获取发光强度
  const getGlowIntensity = (intensity: string) => {
    const intensities = {
      low: 'drop-shadow-sm',
      medium: 'drop-shadow-md cyber-glow',
      high: 'drop-shadow-lg cyber-glow-intense'
    };
    return intensities[intensity as keyof typeof intensities] || intensities.medium;
  };

  // 🎬 打字机效果
  const typewriterEffect = () => {
    setIsAnimating(true);
    setDisplayText('');
    setCurrentIndex(0);

    const speedConfig = getSpeedConfig(speed);
    let index = 0;

    const timer = setInterval(() => {
      if (index < text.length) {
        setDisplayText(text.slice(0, index + 1));
        setCurrentIndex(index + 1);
        index++;
      } else {
        clearInterval(timer);
        setIsAnimating(false);
        onComplete?.();
        
        if (loop) {
          setTimeout(() => typewriterEffect(), 1000);
        }
      }
    }, speedConfig.typewriter);

    return () => clearInterval(timer);
  };

  // ⚡ 故障效果
  const glitchEffect = () => {
    setIsAnimating(true);
    const speedConfig = getSpeedConfig(speed);
    const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    let iterations = 0;
    const maxIterations = 10;

    const timer = setInterval(() => {
      if (iterations < maxIterations) {
        setDisplayText(
          text
            .split('')
            .map((char, index) => {
              if (Math.random() < 0.3) {
                return glitchChars[Math.floor(Math.random() * glitchChars.length)];
              }
              return char;
            })
            .join('')
        );
        iterations++;
      } else {
        setDisplayText(text);
        clearInterval(timer);
        setIsAnimating(false);
        onComplete?.();
        
        if (loop) {
          setTimeout(() => glitchEffect(), 2000);
        }
      }
    }, speedConfig.glitch);

    return () => clearInterval(timer);
  };

  // 🔓 解码效果
  const decodeEffect = () => {
    setIsAnimating(true);
    const speedConfig = getSpeedConfig(speed);
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    
    let frame = 0;
    const maxFrames = text.length * 3;

    const timer = setInterval(() => {
      if (frame < maxFrames) {
        setDisplayText(
          text
            .split('')
            .map((char, index) => {
              if (frame < index * 3) {
                return chars[Math.floor(Math.random() * chars.length)];
              }
              return char;
            })
            .join('')
        );
        frame++;
      } else {
        setDisplayText(text);
        clearInterval(timer);
        setIsAnimating(false);
        onComplete?.();
        
        if (loop) {
          setTimeout(() => decodeEffect(), 1500);
        }
      }
    }, speedConfig.decode);

    return () => clearInterval(timer);
  };

  // 🎯 启动效果
  const startEffect = () => {
    switch (effect) {
      case 'typewriter':
        return typewriterEffect();
      case 'glitch':
        return glitchEffect();
      case 'decode':
        return decodeEffect();
      default:
        setDisplayText(text);
        return () => {};
    }
  };

  // 🚀 自动启动
  useEffect(() => {
    if (autoStart) {
      const cleanup = startEffect();
      return cleanup;
    } else {
      setDisplayText(text);
    }
  }, [text, effect, speed, autoStart]);

  // 🎬 动画变体
  const containerVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  const textVariants = {
    glitch: {
      x: [0, -2, 2, 0],
      textShadow: [
        '0 0 0 transparent',
        '2px 0 0 #ff0000, -2px 0 0 #00ffff',
        '0 0 0 transparent'
      ],
      transition: {
        duration: 0.2,
        repeat: isAnimating ? Infinity : 0,
        repeatType: 'reverse' as const
      }
    },
    flicker: {
      opacity: [1, 0.8, 1, 0.9, 1],
      transition: {
        duration: 0.5,
        repeat: loop ? Infinity : 0,
        repeatType: 'loop' as const
      }
    },
    wave: {
      y: [0, -5, 0, 5, 0],
      transition: {
        duration: 2,
        repeat: loop ? Infinity : 0,
        ease: 'easeInOut'
      }
    },
    scan: {
      backgroundPosition: ['0% 0%', '100% 100%'],
      transition: {
        duration: 2,
        repeat: loop ? Infinity : 0,
        ease: 'linear'
      }
    }
  };

  // 🎨 组合样式类
  const textClasses = cn(
    // 基础样式
    'font-mono font-bold select-none',
    
    // 颜色样式
    getColorStyles(color),
    
    // 尺寸样式
    getSizeStyles(size),
    
    // 发光效果
    getGlowIntensity(glowIntensity),
    
    // 特殊效果样式
    effect === 'matrix' && 'matrix-text',
    effect === 'scan' && 'scan-text',
    
    // 自定义样式
    className
  );

  return (
    <motion.div
      ref={ref}
      className="relative inline-block"
      variants={containerVariants}
      initial="initial"
      animate="animate"
      {...props}
    >
      {/* 🌟 背景效果 */}
      {effect === 'scan' && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-current to-transparent opacity-20 animate-pulse" />
      )}

      {/* 📱 主文本 */}
      <motion.span
        className={textClasses}
        variants={textVariants}
        animate={effect}
      >
        {displayText}
        
        {/* 🎯 光标效果 */}
        {(effect === 'typewriter' && isAnimating) && (
          <motion.span
            className="inline-block w-0.5 h-full bg-current ml-1"
            animate={{ opacity: [1, 0] }}
            transition={{ duration: 0.8, repeat: Infinity }}
          />
        )}
      </motion.span>

      {/* ⚡ 故障叠加层 */}
      {effect === 'glitch' && isAnimating && (
        <>
          <motion.span
            className={cn(textClasses, 'absolute top-0 left-0 text-red-500 opacity-70')}
            animate={{
              x: [0, 2, -2, 0],
              clipPath: ['inset(0 0 0 0)', 'inset(0 0 50% 0)', 'inset(0 0 0 0)']
            }}
            transition={{ duration: 0.1, repeat: Infinity }}
          >
            {displayText}
          </motion.span>
          
          <motion.span
            className={cn(textClasses, 'absolute top-0 left-0 text-cyan-500 opacity-70')}
            animate={{
              x: [0, -2, 2, 0],
              clipPath: ['inset(0 0 0 0)', 'inset(50% 0 0 0)', 'inset(0 0 0 0)']
            }}
            transition={{ duration: 0.1, repeat: Infinity, delay: 0.05 }}
          >
            {displayText}
          </motion.span>
        </>
      )}

      {/* 🌊 波浪效果 */}
      {effect === 'wave' && (
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-current to-transparent opacity-30"
            animate={{
              x: ['-100%', '100%']
            }}
            transition={{
              duration: 2,
              repeat: loop ? Infinity : 0,
              ease: 'linear'
            }}
          />
        </div>
      )}
    </motion.div>
  );
});

HologramText.displayName = 'HologramText';

export default HologramText;
