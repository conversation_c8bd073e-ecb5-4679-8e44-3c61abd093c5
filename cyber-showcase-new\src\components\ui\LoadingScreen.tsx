'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface LoadingScreenProps {
  message?: string;
  progress?: number;
  showProgress?: boolean;
}

/**
 * 🔮 赛博朋克加载屏幕组件
 */
const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = "初始化赛博空间...", 
  progress,
  showProgress = false 
}) => {
  const [displayText, setDisplayText] = useState('');
  const [currentProgress, setCurrentProgress] = useState(0);
  const [loadingSteps, setLoadingSteps] = useState([
    { text: '连接赛博空间...', completed: false },
    { text: '加载神秘协议...', completed: false },
    { text: '初始化量子矩阵...', completed: false },
    { text: '激活防护系统...', completed: false },
    { text: '准备展示界面...', completed: false }
  ]);

  // 🎬 打字机效果
  useEffect(() => {
    let index = 0;
    const timer = setInterval(() => {
      if (index < message.length) {
        setDisplayText(message.slice(0, index + 1));
        index++;
      } else {
        clearInterval(timer);
      }
    }, 50);

    return () => clearInterval(timer);
  }, [message]);

  // 📊 进度模拟
  useEffect(() => {
    if (progress !== undefined) {
      setCurrentProgress(progress);
      return;
    }

    // 自动进度模拟
    const progressTimer = setInterval(() => {
      setCurrentProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressTimer);
          return 100;
        }
        return prev + Math.random() * 3;
      });
    }, 100);

    return () => clearInterval(progressTimer);
  }, [progress]);

  // 🎯 步骤完成模拟
  useEffect(() => {
    const stepTimer = setInterval(() => {
      setLoadingSteps(prev => {
        const nextIncompleteIndex = prev.findIndex(step => !step.completed);
        if (nextIncompleteIndex === -1) {
          clearInterval(stepTimer);
          return prev;
        }

        const newSteps = [...prev];
        newSteps[nextIncompleteIndex].completed = true;
        return newSteps;
      });
    }, 800);

    return () => clearInterval(stepTimer);
  }, []);

  // 🎨 动画变体
  const containerVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: { duration: 0.5 }
    },
    exit: { 
      opacity: 0,
      transition: { duration: 0.5 }
    }
  };

  const logoVariants = {
    initial: { scale: 0, rotate: -180 },
    animate: { 
      scale: 1, 
      rotate: 0,
      transition: { 
        duration: 1,
        ease: "easeOut"
      }
    }
  };

  const pulseVariants = {
    animate: {
      scale: [1, 1.2, 1],
      opacity: [0.5, 1, 0.5],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const progressBarVariants = {
    initial: { width: 0 },
    animate: { 
      width: `${currentProgress}%`,
      transition: { 
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-cyber-dark via-cyber-black to-cyber-dark"
      variants={containerVariants}
      initial="initial"
      animate="animate"
      exit="exit"
    >
      {/* 🌟 背景粒子效果 */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-cyber-green rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* 🔮 主要内容 */}
      <div className="relative z-10 text-center max-w-md mx-auto px-6">
        {/* Logo和符号 */}
        <motion.div
          className="mb-8"
          variants={logoVariants}
          initial="initial"
          animate="animate"
        >
          <div className="relative inline-block">
            <motion.div
              className="w-20 h-20 mx-auto mb-4 rounded-full border-2 border-cyber-green flex items-center justify-center text-3xl"
              variants={pulseVariants}
              animate="animate"
            >
              ☸️
            </motion.div>
            
            {/* 外圈脉冲效果 */}
            <motion.div
              className="absolute inset-0 rounded-full border-2 border-cyber-green opacity-30"
              animate={{
                scale: [1, 1.5, 2],
                opacity: [0.3, 0.1, 0],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeOut"
              }}
            />
          </div>

          <h1 className="text-2xl font-bold font-mono text-cyber-green cyber-glow mb-2">
            神秘技术组织
          </h1>
          <p className="text-cyber-gold text-sm font-mono">
            众生即我，我即众生，宇宙之卵
          </p>
        </motion.div>

        {/* 加载消息 */}
        <div className="mb-8">
          <motion.p 
            className="text-cyber-green font-mono text-lg mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            {displayText}
            <motion.span
              className="inline-block w-2 h-5 bg-cyber-green ml-1"
              animate={{ opacity: [1, 0] }}
              transition={{ duration: 0.8, repeat: Infinity }}
            />
          </motion.p>

          {/* 进度条 */}
          {showProgress && (
            <div className="mb-6">
              <div className="w-full h-2 bg-cyber-dark border border-cyber-green/30 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-cyber-green to-cyber-gold"
                  variants={progressBarVariants}
                  initial="initial"
                  animate="animate"
                />
              </div>
              <p className="text-cyber-gold text-sm font-mono mt-2">
                {Math.round(currentProgress)}%
              </p>
            </div>
          )}
        </div>

        {/* 加载步骤 */}
        <div className="space-y-3">
          {loadingSteps.map((step, index) => (
            <motion.div
              key={index}
              className="flex items-center space-x-3 text-sm font-mono"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.2 }}
            >
              <div className="relative">
                {step.completed ? (
                  <motion.div
                    className="w-3 h-3 bg-cyber-green rounded-full"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                ) : (
                  <motion.div
                    className="w-3 h-3 border border-cyber-green rounded-full"
                    animate={{ 
                      borderColor: ['#00ff41', '#ffd700', '#00ff41'],
                    }}
                    transition={{ 
                      duration: 1.5, 
                      repeat: Infinity 
                    }}
                  />
                )}
              </div>
              <span className={step.completed ? 'text-cyber-green' : 'text-cyber-gold'}>
                {step.text}
              </span>
              {step.completed && (
                <motion.span
                  className="text-cyber-green"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  ✓
                </motion.span>
              )}
            </motion.div>
          ))}
        </div>

        {/* 底部提示 */}
        <motion.div
          className="mt-8 text-xs text-cyber-gold/70 font-mono"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2 }}
        >
          <p>正在建立安全连接...</p>
          <p className="mt-1">请稍候，即将进入赛博朋克世界</p>
        </motion.div>
      </div>

      {/* 🌐 底部装饰线条 */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-cyber-green to-transparent">
        <motion.div
          className="h-full bg-cyber-gold"
          animate={{
            x: ['-100%', '100%'],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ width: '20%' }}
        />
      </div>
    </motion.div>
  );
};

export default LoadingScreen;
