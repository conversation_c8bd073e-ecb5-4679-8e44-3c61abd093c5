/**
 * 🧠 智能路径检测系统
 * 基于用户行为分析，智能判断用户类型并分发到对应路径
 */

export interface UserBehavior {
  // 页面浏览行为
  viewsCode: boolean;
  interactsWithDemo: boolean;
  timeOnTechPages: number;
  timeOnBusinessPages: number;
  
  // 交互行为
  clicksOnGitHub: boolean;
  downloadsWhitepaper: boolean;
  requestsDemo: boolean;
  contactsForBusiness: boolean;
  
  // 技术探索行为
  usesHackerChallenge: boolean;
  viewsThreatRadar: boolean;
  checksSourceCode: boolean;
  
  // 商务咨询行为
  viewsPricing: boolean;
  requestsQuote: boolean;
  schedulesCall: boolean;
  
  // 设备和环境
  isMobile: boolean;
  userAgent: string;
  referrer: string;
}

export type UserType = 'tech-track' | 'business-track' | 'mixed-track';

export interface PathRecommendation {
  userType: UserType;
  confidence: number;
  recommendedRoute: string;
  personalizedContent: string[];
  nextActions: string[];
}

/**
 * 🎯 用户类型检测算法
 */
export class PathDetector {
  private behaviorHistory: UserBehavior[] = [];
  private currentBehavior: Partial<UserBehavior> = {};
  
  /**
   * 记录用户行为
   */
  recordBehavior(behavior: Partial<UserBehavior>) {
    this.currentBehavior = { ...this.currentBehavior, ...behavior };
    
    // 每30秒保存一次行为快照
    if (this.shouldSaveBehavior()) {
      this.saveBehaviorSnapshot();
    }
  }
  
  /**
   * 智能检测用户类型
   */
  detectUserType(): PathRecommendation {
    const behavior = this.getCurrentBehavior();
    let techScore = 0;
    let businessScore = 0;
    
    // 🔧 技术指标评分
    if (behavior.viewsCode) techScore += 30;
    if (behavior.interactsWithDemo) techScore += 25;
    if (behavior.timeOnTechPages > 60) techScore += 20;
    if (behavior.clicksOnGitHub) techScore += 15;
    if (behavior.downloadsWhitepaper) techScore += 10;
    if (behavior.usesHackerChallenge) techScore += 35;
    if (behavior.viewsThreatRadar) techScore += 20;
    if (behavior.checksSourceCode) techScore += 25;
    
    // 💼 商务指标评分
    if (behavior.contactsForBusiness) businessScore += 40;
    if (behavior.requestsDemo) businessScore += 30;
    if (behavior.viewsPricing) businessScore += 25;
    if (behavior.requestsQuote) businessScore += 35;
    if (behavior.schedulesCall) businessScore += 40;
    if (behavior.timeOnBusinessPages > 60) businessScore += 20;
    
    // 📱 设备和环境调整
    if (behavior.isMobile) {
      businessScore += 10; // 移动端用户更可能是商务决策者
    }
    
    // 🌐 来源渠道调整
    if (behavior.referrer?.includes('linkedin')) businessScore += 15;
    if (behavior.referrer?.includes('github')) techScore += 20;
    if (behavior.referrer?.includes('google')) {
      // 搜索关键词分析（如果可获取）
      techScore += 5;
      businessScore += 5;
    }
    
    // 🎯 决策逻辑
    const totalScore = techScore + businessScore;
    const techRatio = totalScore > 0 ? techScore / totalScore : 0.5;
    const businessRatio = totalScore > 0 ? businessScore / totalScore : 0.5;
    
    let userType: UserType;
    let confidence: number;
    let recommendedRoute: string;
    
    if (techRatio > 0.7) {
      userType = 'tech-track';
      confidence = techRatio;
      recommendedRoute = '/tech-showcase';
    } else if (businessRatio > 0.7) {
      userType = 'business-track';
      confidence = businessRatio;
      recommendedRoute = '/business-showcase';
    } else {
      userType = 'mixed-track';
      confidence = Math.max(techRatio, businessRatio);
      recommendedRoute = '/adaptive-showcase';
    }
    
    return {
      userType,
      confidence,
      recommendedRoute,
      personalizedContent: this.generatePersonalizedContent(userType, behavior),
      nextActions: this.generateNextActions(userType, behavior)
    };
  }
  
  /**
   * 生成个性化内容推荐
   */
  private generatePersonalizedContent(userType: UserType, behavior: UserBehavior): string[] {
    const content: string[] = [];
    
    switch (userType) {
      case 'tech-track':
        content.push('深度技术架构解析');
        content.push('开源代码和API文档');
        content.push('实战攻防演示');
        if (behavior.usesHackerChallenge) {
          content.push('高级渗透测试挑战');
        }
        if (behavior.viewsThreatRadar) {
          content.push('威胁情报深度分析');
        }
        break;
        
      case 'business-track':
        content.push('ROI计算和成本分析');
        content.push('客户成功案例');
        content.push('合规性和认证信息');
        if (behavior.requestsDemo) {
          content.push('定制化演示方案');
        }
        if (behavior.viewsPricing) {
          content.push('企业级服务包装');
        }
        break;
        
      case 'mixed-track':
        content.push('技术优势与商业价值结合');
        content.push('分层次的产品介绍');
        content.push('灵活的展示路径选择');
        break;
    }
    
    return content;
  }
  
  /**
   * 生成下一步行动建议
   */
  private generateNextActions(userType: UserType, behavior: UserBehavior): string[] {
    const actions: string[] = [];
    
    switch (userType) {
      case 'tech-track':
        actions.push('查看技术白皮书');
        actions.push('尝试在线演示');
        actions.push('访问GitHub仓库');
        if (!behavior.usesHackerChallenge) {
          actions.push('参与黑客挑战');
        }
        break;
        
      case 'business-track':
        actions.push('申请产品演示');
        actions.push('下载解决方案手册');
        actions.push('预约专家咨询');
        if (!behavior.requestsQuote) {
          actions.push('获取报价方案');
        }
        break;
        
      case 'mixed-track':
        actions.push('选择感兴趣的展示路径');
        actions.push('探索产品功能特性');
        actions.push('了解技术和商业优势');
        break;
    }
    
    return actions;
  }
  
  /**
   * 获取当前行为数据
   */
  private getCurrentBehavior(): UserBehavior {
    const defaultBehavior: UserBehavior = {
      viewsCode: false,
      interactsWithDemo: false,
      timeOnTechPages: 0,
      timeOnBusinessPages: 0,
      clicksOnGitHub: false,
      downloadsWhitepaper: false,
      requestsDemo: false,
      contactsForBusiness: false,
      usesHackerChallenge: false,
      viewsThreatRadar: false,
      checksSourceCode: false,
      viewsPricing: false,
      requestsQuote: false,
      schedulesCall: false,
      isMobile: false,
      userAgent: '',
      referrer: ''
    };
    
    return { ...defaultBehavior, ...this.currentBehavior };
  }
  
  /**
   * 判断是否应该保存行为快照
   */
  private shouldSaveBehavior(): boolean {
    const lastSnapshot = this.behaviorHistory[this.behaviorHistory.length - 1];
    if (!lastSnapshot) return true;
    
    // 简单的时间间隔判断（实际应用中可以更复杂）
    return Date.now() - (lastSnapshot as any).timestamp > 30000;
  }
  
  /**
   * 保存行为快照
   */
  private saveBehaviorSnapshot() {
    const snapshot = {
      ...this.getCurrentBehavior(),
      timestamp: Date.now()
    };
    
    this.behaviorHistory.push(snapshot as UserBehavior);
    
    // 保持历史记录在合理范围内
    if (this.behaviorHistory.length > 10) {
      this.behaviorHistory.shift();
    }
  }
  
  /**
   * 获取行为历史分析
   */
  getBehaviorAnalytics() {
    return {
      totalSessions: this.behaviorHistory.length,
      averageSessionTime: this.calculateAverageSessionTime(),
      preferredContent: this.analyzeContentPreferences(),
      conversionFunnel: this.analyzeConversionFunnel()
    };
  }
  
  private calculateAverageSessionTime(): number {
    if (this.behaviorHistory.length === 0) return 0;
    
    const totalTime = this.behaviorHistory.reduce((sum, behavior) => {
      return sum + behavior.timeOnTechPages + behavior.timeOnBusinessPages;
    }, 0);
    
    return totalTime / this.behaviorHistory.length;
  }
  
  private analyzeContentPreferences(): Record<string, number> {
    const preferences: Record<string, number> = {};
    
    this.behaviorHistory.forEach(behavior => {
      if (behavior.viewsCode) preferences['code'] = (preferences['code'] || 0) + 1;
      if (behavior.interactsWithDemo) preferences['demo'] = (preferences['demo'] || 0) + 1;
      if (behavior.usesHackerChallenge) preferences['challenge'] = (preferences['challenge'] || 0) + 1;
      if (behavior.viewsThreatRadar) preferences['threat'] = (preferences['threat'] || 0) + 1;
    });
    
    return preferences;
  }
  
  private analyzeConversionFunnel(): Record<string, number> {
    const funnel: Record<string, number> = {
      visited: this.behaviorHistory.length,
      engaged: 0,
      interested: 0,
      converted: 0
    };
    
    this.behaviorHistory.forEach(behavior => {
      if (behavior.timeOnTechPages > 30 || behavior.timeOnBusinessPages > 30) {
        funnel.engaged++;
      }
      if (behavior.requestsDemo || behavior.downloadsWhitepaper) {
        funnel.interested++;
      }
      if (behavior.contactsForBusiness || behavior.requestsQuote) {
        funnel.converted++;
      }
    });
    
    return funnel;
  }
}

// 🌟 全局路径检测器实例
export const globalPathDetector = new PathDetector();
