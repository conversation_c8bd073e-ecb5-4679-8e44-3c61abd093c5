/**
 * 🔮 赛博朋克展示平台 - 全局状态管理
 * 使用Zustand实现轻量级状态管理
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { UserType, PathRecommendation, UserBehavior } from '@/lib/path-detector';

// 🎨 主题类型定义
export type Theme = 'mystical' | 'business' | 'adaptive';

// 🌟 展示模式定义
export type ShowcaseMode = 'intro' | 'products' | 'capabilities' | 'cases' | 'contact';

// 📊 产品类型定义
export type ProductType = 'zencode' | 'spore' | 'training';

// 🎯 用户交互状态
export interface UserInteraction {
  currentPage: string;
  timeSpent: number;
  interactions: string[];
  preferences: Record<string, any>;
}

// 🔧 应用状态接口
export interface AppState {
  // 🎨 主题和外观
  theme: Theme;
  isDarkMode: boolean;
  showParticles: boolean;
  enableAnimations: boolean;
  
  // 🧭 导航和路径
  currentMode: ShowcaseMode;
  userType: UserType;
  pathRecommendation: PathRecommendation | null;
  
  // 📱 用户交互
  userInteraction: UserInteraction;
  behaviorData: Partial<UserBehavior>;
  
  // 🎬 展示控制
  isIntroPlaying: boolean;
  currentProductFocus: ProductType | null;
  demoMode: boolean;
  
  // 📊 数据和内容
  contentLanguage: 'zh' | 'en';
  showTechnicalDetails: boolean;
  personalizedContent: string[];
  
  // 🔄 加载状态
  isLoading: boolean;
  loadingMessage: string;
  
  // 🎵 音效控制
  soundEnabled: boolean;
  backgroundMusicEnabled: boolean;
}

// ⚡ 状态操作接口
export interface AppActions {
  // 🎨 主题操作
  setTheme: (theme: Theme) => void;
  toggleDarkMode: () => void;
  toggleParticles: () => void;
  toggleAnimations: () => void;
  
  // 🧭 导航操作
  setCurrentMode: (mode: ShowcaseMode) => void;
  setUserType: (userType: UserType) => void;
  setPathRecommendation: (recommendation: PathRecommendation) => void;
  
  // 📱 用户交互操作
  recordInteraction: (interaction: string) => void;
  updateTimeSpent: (page: string, time: number) => void;
  setBehaviorData: (behavior: Partial<UserBehavior>) => void;
  
  // 🎬 展示控制操作
  startIntro: () => void;
  stopIntro: () => void;
  focusProduct: (product: ProductType) => void;
  toggleDemoMode: () => void;
  
  // 📊 内容操作
  setContentLanguage: (lang: 'zh' | 'en') => void;
  toggleTechnicalDetails: () => void;
  setPersonalizedContent: (content: string[]) => void;
  
  // 🔄 加载操作
  setLoading: (loading: boolean, message?: string) => void;
  
  // 🎵 音效操作
  toggleSound: () => void;
  toggleBackgroundMusic: () => void;
  
  // 🔄 重置操作
  resetUserData: () => void;
  resetToDefaults: () => void;
}

// 🏪 完整的Store类型
export type AppStore = AppState & AppActions;

// 🌟 默认状态
const defaultState: AppState = {
  // 🎨 主题和外观
  theme: 'mystical',
  isDarkMode: true,
  showParticles: true,
  enableAnimations: true,
  
  // 🧭 导航和路径
  currentMode: 'intro',
  userType: 'mixed-track',
  pathRecommendation: null,
  
  // 📱 用户交互
  userInteraction: {
    currentPage: '/',
    timeSpent: 0,
    interactions: [],
    preferences: {}
  },
  behaviorData: {},
  
  // 🎬 展示控制
  isIntroPlaying: false,
  currentProductFocus: null,
  demoMode: false,
  
  // 📊 数据和内容
  contentLanguage: 'zh',
  showTechnicalDetails: false,
  personalizedContent: [],
  
  // 🔄 加载状态
  isLoading: false,
  loadingMessage: '',
  
  // 🎵 音效控制
  soundEnabled: true,
  backgroundMusicEnabled: false
};

// 🔮 创建Zustand Store
export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...defaultState,
        
        // 🎨 主题操作实现
        setTheme: (theme) => {
          set({ theme }, false, 'setTheme');
          
          // 根据主题自动调整其他设置
          if (theme === 'business') {
            set({ 
              showParticles: false, 
              enableAnimations: false,
              isDarkMode: false 
            });
          } else if (theme === 'mystical') {
            set({ 
              showParticles: true, 
              enableAnimations: true,
              isDarkMode: true 
            });
          }
        },
        
        toggleDarkMode: () => {
          set((state) => ({ isDarkMode: !state.isDarkMode }), false, 'toggleDarkMode');
        },
        
        toggleParticles: () => {
          set((state) => ({ showParticles: !state.showParticles }), false, 'toggleParticles');
        },
        
        toggleAnimations: () => {
          set((state) => ({ enableAnimations: !state.enableAnimations }), false, 'toggleAnimations');
        },
        
        // 🧭 导航操作实现
        setCurrentMode: (mode) => {
          set({ currentMode: mode }, false, 'setCurrentMode');
          
          // 记录页面访问
          const { recordInteraction } = get();
          recordInteraction(`navigate_to_${mode}`);
        },
        
        setUserType: (userType) => {
          set({ userType }, false, 'setUserType');
          
          // 根据用户类型自动调整主题
          if (userType === 'tech-track') {
            get().setTheme('mystical');
          } else if (userType === 'business-track') {
            get().setTheme('business');
          } else {
            get().setTheme('adaptive');
          }
        },
        
        setPathRecommendation: (recommendation) => {
          set({ 
            pathRecommendation: recommendation,
            personalizedContent: recommendation.personalizedContent 
          }, false, 'setPathRecommendation');
        },
        
        // 📱 用户交互操作实现
        recordInteraction: (interaction) => {
          set((state) => ({
            userInteraction: {
              ...state.userInteraction,
              interactions: [...state.userInteraction.interactions, interaction]
            }
          }), false, 'recordInteraction');
        },
        
        updateTimeSpent: (page, time) => {
          set((state) => ({
            userInteraction: {
              ...state.userInteraction,
              currentPage: page,
              timeSpent: state.userInteraction.timeSpent + time
            }
          }), false, 'updateTimeSpent');
        },
        
        setBehaviorData: (behavior) => {
          set((state) => ({
            behaviorData: { ...state.behaviorData, ...behavior }
          }), false, 'setBehaviorData');
        },
        
        // 🎬 展示控制操作实现
        startIntro: () => {
          set({ isIntroPlaying: true, currentMode: 'intro' }, false, 'startIntro');
        },
        
        stopIntro: () => {
          set({ isIntroPlaying: false }, false, 'stopIntro');
        },
        
        focusProduct: (product) => {
          set({ 
            currentProductFocus: product,
            currentMode: 'products' 
          }, false, 'focusProduct');
        },
        
        toggleDemoMode: () => {
          set((state) => ({ demoMode: !state.demoMode }), false, 'toggleDemoMode');
        },
        
        // 📊 内容操作实现
        setContentLanguage: (lang) => {
          set({ contentLanguage: lang }, false, 'setContentLanguage');
        },
        
        toggleTechnicalDetails: () => {
          set((state) => ({ 
            showTechnicalDetails: !state.showTechnicalDetails 
          }), false, 'toggleTechnicalDetails');
        },
        
        setPersonalizedContent: (content) => {
          set({ personalizedContent: content }, false, 'setPersonalizedContent');
        },
        
        // 🔄 加载操作实现
        setLoading: (loading, message = '') => {
          set({ isLoading: loading, loadingMessage: message }, false, 'setLoading');
        },
        
        // 🎵 音效操作实现
        toggleSound: () => {
          set((state) => ({ soundEnabled: !state.soundEnabled }), false, 'toggleSound');
        },
        
        toggleBackgroundMusic: () => {
          set((state) => ({ 
            backgroundMusicEnabled: !state.backgroundMusicEnabled 
          }), false, 'toggleBackgroundMusic');
        },
        
        // 🔄 重置操作实现
        resetUserData: () => {
          set({
            userInteraction: defaultState.userInteraction,
            behaviorData: {},
            pathRecommendation: null,
            personalizedContent: []
          }, false, 'resetUserData');
        },
        
        resetToDefaults: () => {
          set(defaultState, false, 'resetToDefaults');
        }
      }),
      {
        name: 'cyber-showcase-store',
        partialize: (state) => ({
          // 只持久化用户偏好，不持久化临时状态
          theme: state.theme,
          isDarkMode: state.isDarkMode,
          showParticles: state.showParticles,
          enableAnimations: state.enableAnimations,
          contentLanguage: state.contentLanguage,
          soundEnabled: state.soundEnabled,
          backgroundMusicEnabled: state.backgroundMusicEnabled,
          userInteraction: state.userInteraction,
          behaviorData: state.behaviorData
        })
      }
    ),
    {
      name: 'cyber-showcase-store'
    }
  )
);

// 🎯 选择器函数 - 用于优化性能
export const selectTheme = (state: AppStore) => state.theme;
export const selectUserType = (state: AppStore) => state.userType;
export const selectCurrentMode = (state: AppStore) => state.currentMode;
export const selectIsLoading = (state: AppStore) => state.isLoading;
export const selectUserInteraction = (state: AppStore) => state.userInteraction;
export const selectPersonalizedContent = (state: AppStore) => state.personalizedContent;
